{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(docker buildx build:*)", "<PERSON><PERSON>(docker pull:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(echo:*)", "Bash(echo \"http_proxy: $http_proxy\")", "Bash(./build-docker.sh:*)", "Bash(ls:*)", "Bash(unset HTTP_PROXY)", "Bash(unset:*)", "Bash(unset http_proxy)", "<PERSON><PERSON>(unset https_proxy)", "Bash(osascript:*)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(mv:*)", "mcp__clear-thought__sequentialthinking", "Bash(rm:*)", "Bash(ping:*)", "<PERSON><PERSON>(nslookup:*)", "Bash(nc:*)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(mkdir:*)", "Bash(bash:*)", "mcp__interactive-feedback__interactive_feedback", "<PERSON><PERSON>(python:*)", "Bash(grep:*)", "Bash(sqlite3:*)", "Bash(./deploy.sh)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(comm:*)", "Bash(for name in \"box.mp3\" \"game.mp3\" \"orange.mp3\")", "Bash(do echo \"=== $name 的重复文件 ===\")", "Bash(done)", "Bash(for name in \"diploma.jpg\" \"fishing.jpg\" \"ill.jpg\" \"player.jpg\" \"pool.jpg\" \"swimming.jpg\")", "Bash(kill:*)", "Bash(pip install:*)", "mcp__promptx__promptx_init", "Ba<PERSON>(unzip:*)", "mcp__promptx__promptx_welcome", "mcp__promptx__promptx_action", "Bash(cp:*)", "Bash(for word in jam pie salt sauce sugar pancake missing)", "Bash(do if [ -f \"/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/static/cache/memory_help_new/$word.md\" ])", "Bash(then echo \"✅ $word.md - 已存在\")", "Bash(else echo \"❌ $word.md - 不存在\")", "Bash(fi)", "Bash(for word in across customer order rush)", "Bash(for:*)", "Bash(do echo -n \"$file.md: \")", "Bash(wc:*)", "Bash(do if [ -f \"$word.md\" ])", "Bash(then echo \"$word: EXISTS\")", "Bash(else echo \"$word: MISSING\")", "Bash(ln:*)", "<PERSON><PERSON>(touch:*)"], "deny": []}}