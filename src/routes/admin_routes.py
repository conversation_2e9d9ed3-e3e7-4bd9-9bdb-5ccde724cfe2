"""
管理员路由模块
处理管理员功能相关的API路由，包括数据库管理、数据导出、单词导入等
"""
import os
import csv
import json
from datetime import datetime
from flask import request, jsonify, session, send_file
from werkzeug.utils import secure_filename

from ..models import User, Word, db, enhanced_db


def register_admin_routes(app):
    """注册管理员路由"""
    
    def check_admin_permission():
        """检查管理员权限"""
        if 'user_id' not in session:
            return False, '请先登录'
        
        user = User.get_by_id(session['user_id'])
        if not user or user['username'] != 'admin':
            return False, '权限不足：仅管理员可访问'
        
        return True, ''
    
    # ===== 数据库管理API =====

    @app.route('/admin/test_session')
    def admin_test_session():
        """测试会话状态API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})

        return jsonify({
            'success': True,
            'session_active': True,
            'user_id': session.get('user_id'),
            'message': '会话状态正常'
        })

    @app.route('/admin/database/data')
    def admin_database_data():
        """获取数据库表数据API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})

        try:
            table = request.args.get('table', 'user')
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 50))

            # 验证表名
            valid_tables = ['user', 'word', 'user_word', 'learning_plan', 'word_record', 'import_history']
            if table not in valid_tables:
                return jsonify({'success': False, 'message': f'无效的表名: {table}'})

            # 计算偏移量
            offset = (page - 1) * per_page

            # 获取总记录数
            count_query = f"SELECT COUNT(*) as total FROM {table}"
            total_result = db.execute_query(count_query)
            total = total_result[0]['total'] if total_result else 0

            # 获取分页数据
            data_query = f"SELECT * FROM {table} ORDER BY id LIMIT ? OFFSET ?"
            data = db.execute_query(data_query, (per_page, offset))

            # 转换为字典列表
            data_list = []
            for row in data:
                row_dict = {}
                for key in row.keys():
                    row_dict[key] = row[key]
                data_list.append(row_dict)

            return jsonify({
                'success': True,
                'data': data_list,
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })

        except Exception as e:
            return jsonify({'success': False, 'message': f'获取数据失败: {str(e)}'})

    @app.route('/admin/database/delete', methods=['POST'])
    def admin_database_delete():
        """单个删除数据API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})

        try:
            data = request.get_json()
            table = data.get('table')
            record_id = data.get('id')
            admin_password = data.get('admin_password')

            # 验证管理员密码
            if not admin_password:
                return jsonify({'success': False, 'error': '请输入管理员密码'})

            # 获取admin用户并验证密码
            admin_user = User.get_by_username('admin')
            if not admin_user:
                return jsonify({'success': False, 'error': '管理员用户不存在'})

            # 使用AuthService验证密码（支持多种加密格式）
            from ..services.auth.auth_service import AuthService
            if not AuthService.verify_password(admin_user['password'], admin_password):
                return jsonify({'success': False, 'error': '管理员密码错误'})

            # 验证表名
            valid_tables = ['user', 'word', 'user_word', 'learning_plan', 'word_record', 'import_history']
            if table not in valid_tables:
                return jsonify({'success': False, 'error': f'无效的表名: {table}'})

            if not record_id:
                return jsonify({'success': False, 'error': '没有指定要删除的记录ID'})

            # 防止删除管理员用户
            if table == 'user':
                if admin_user and admin_user['id'] == record_id:
                    return jsonify({'success': False, 'error': '不能删除管理员用户'})

            # 执行删除
            delete_query = f"DELETE FROM {table} WHERE id = ?"
            affected_rows = db.execute_update(delete_query, (record_id,))

            if affected_rows > 0:
                return jsonify({
                    'success': True,
                    'message': '删除成功'
                })
            else:
                return jsonify({'success': False, 'error': '记录不存在或删除失败'})

        except Exception as e:
            return jsonify({'success': False, 'error': f'删除失败: {str(e)}'})

    @app.route('/admin/database/batch_delete', methods=['POST'])
    def admin_database_batch_delete():
        """批量删除数据API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})

        try:
            data = request.get_json()
            table = data.get('table')
            ids = data.get('ids', [])
            admin_password = data.get('admin_password')

            # 验证管理员密码
            if not admin_password:
                return jsonify({'success': False, 'error': '请输入管理员密码'})

            # 获取admin用户并验证密码
            admin_user = User.get_by_username('admin')
            if not admin_user:
                return jsonify({'success': False, 'error': '管理员用户不存在'})

            # 使用AuthService验证密码（支持多种加密格式）
            from ..services.auth.auth_service import AuthService
            if not AuthService.verify_password(admin_user['password'], admin_password):
                return jsonify({'success': False, 'error': '管理员密码错误'})

            # 验证表名
            valid_tables = ['user', 'word', 'user_word', 'learning_plan', 'word_record', 'import_history']
            if table not in valid_tables:
                return jsonify({'success': False, 'error': f'无效的表名: {table}'})

            if not ids:
                return jsonify({'success': False, 'error': '没有选择要删除的记录'})

            # 防止删除管理员用户
            if table == 'user':
                if admin_user and admin_user['id'] in ids:
                    return jsonify({'success': False, 'error': '不能删除管理员用户'})

            # 构建删除SQL
            placeholders = ','.join(['?' for _ in ids])
            delete_query = f"DELETE FROM {table} WHERE id IN ({placeholders})"

            # 执行删除
            affected_rows = db.execute_update(delete_query, ids)

            return jsonify({
                'success': True,
                'message': f'成功删除 {affected_rows} 条记录',
                'deleted_count': affected_rows
            })

        except Exception as e:
            return jsonify({'success': False, 'error': f'删除失败: {str(e)}'})

    @app.route('/admin/database/update', methods=['POST'])
    def admin_database_update():
        """数据库记录更新API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})

        try:
            data = request.get_json()
            table = data.get('table')
            record_id = data.get('id')
            updates = data.get('updates', {})
            admin_password = data.get('admin_password')

            # 验证管理员密码
            if not admin_password:
                return jsonify({'success': False, 'error': '请输入管理员密码'})

            # 获取admin用户并验证密码
            admin_user = User.get_by_username('admin')
            if not admin_user:
                return jsonify({'success': False, 'error': '管理员用户不存在'})

            # 使用AuthService验证密码（支持多种加密格式）
            from ..services.auth.auth_service import AuthService
            if not AuthService.verify_password(admin_user['password'], admin_password):
                return jsonify({'success': False, 'error': '管理员密码错误'})

            # 验证表名
            valid_tables = ['user', 'word', 'user_word', 'learning_plan', 'word_record', 'import_history']
            if table not in valid_tables:
                return jsonify({'success': False, 'error': f'无效的表名: {table}'})

            if not record_id:
                return jsonify({'success': False, 'error': '没有指定要更新的记录ID'})

            if not updates:
                return jsonify({'success': False, 'error': '没有指定要更新的字段'})

            # 构建更新SQL
            set_clauses = []
            values = []
            for field, value in updates.items():
                set_clauses.append(f"{field} = ?")
                values.append(value)
            
            values.append(record_id)  # WHERE条件的ID值
            
            update_query = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE id = ?"
            
            # 执行更新
            affected_rows = db.execute_update(update_query, values)

            if affected_rows > 0:
                return jsonify({
                    'success': True,
                    'message': '更新成功',
                    'affected_rows': affected_rows
                })
            else:
                return jsonify({'success': False, 'error': '记录不存在或无变更'})

        except Exception as e:
            return jsonify({'success': False, 'error': f'更新失败: {str(e)}'})

    @app.route('/api/admin/database_info')
    def api_admin_database_info():
        """获取数据库信息API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})
        
        try:
            # 获取各表的记录数
            user_count = db.execute_query("SELECT COUNT(*) as count FROM user")[0]['count']
            word_count = db.execute_query("SELECT COUNT(*) as count FROM word")[0]['count']
            user_word_count = db.execute_query("SELECT COUNT(*) as count FROM user_word")[0]['count']
            word_record_count = db.execute_query("SELECT COUNT(*) as count FROM word_record")[0]['count']
            learning_plan_count = db.execute_query("SELECT COUNT(*) as count FROM learning_plan")[0]['count']
            
            # 获取数据库文件大小
            db_path = app.config.get('DATABASE_PATH', 'data/word_learning.db')
            db_size = 0
            if os.path.exists(db_path):
                db_size = os.path.getsize(db_path)
            
            return jsonify({
                'success': True,
                'database_info': {
                    'user_count': user_count,
                    'word_count': word_count,
                    'user_word_count': user_word_count,
                    'word_record_count': word_record_count,
                    'learning_plan_count': learning_plan_count,
                    'database_size': db_size,
                    'database_size_mb': round(db_size / (1024 * 1024), 2)
                }
            })
        except Exception as e:
            return jsonify({'success': False, 'message': f'获取数据库信息失败: {str(e)}'})
    
    @app.route('/api/admin/backup_database', methods=['POST'])
    def api_admin_backup_database():
        """备份数据库API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})
        
        try:
            # 创建备份目录
            backup_dir = 'data/backups'
            os.makedirs(backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f'word_learning_backup_{timestamp}.db'
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # 复制数据库文件
            db_path = app.config.get('DATABASE_PATH', 'data/word_learning.db')
            if os.path.exists(db_path):
                import shutil
                shutil.copy2(db_path, backup_path)
                
                return jsonify({
                    'success': True,
                    'message': '数据库备份成功',
                    'backup_file': backup_filename,
                    'backup_path': backup_path
                })
            else:
                return jsonify({'success': False, 'message': '数据库文件不存在'})
                
        except Exception as e:
            return jsonify({'success': False, 'message': f'备份失败: {str(e)}'})
    
    @app.route('/api/admin/reset_database', methods=['POST'])
    def api_admin_reset_database():
        """重置数据库API（危险操作）"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})
        
        data = request.get_json()
        confirm = data.get('confirm', False)
        
        if not confirm:
            return jsonify({'success': False, 'message': '请确认重置操作'})
        
        try:
            # 先备份当前数据库
            backup_result = api_admin_backup_database()
            
            # 清空所有用户数据表（保留word表）
            db.execute_query("DELETE FROM word_record")
            db.execute_query("DELETE FROM learning_plan")
            db.execute_query("DELETE FROM user_word")
            db.execute_query("DELETE FROM user WHERE username != 'admin'")
            
            return jsonify({
                'success': True,
                'message': '数据库重置成功（已保留管理员账户和单词数据）',
                'backup_info': backup_result.get_json() if hasattr(backup_result, 'get_json') else None
            })
            
        except Exception as e:
            return jsonify({'success': False, 'message': f'重置失败: {str(e)}'})
    
    # ===== 数据导出API =====
    
    @app.route('/api/admin/export_users')
    def api_admin_export_users():
        """导出用户数据API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})
        
        try:
            # 获取所有用户数据
            users = db.execute_query("""
                SELECT id, username, points, vouchers, registration_date, created_at
                FROM user
                ORDER BY id
            """)
            
            # 创建导出目录
            export_dir = 'data/exports'
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成导出文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_filename = f'users_export_{timestamp}.csv'
            export_path = os.path.join(export_dir, export_filename)
            
            # 写入CSV文件
            with open(export_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['id', 'username', 'points', 'vouchers', 'registration_date', 'created_at']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for user in users:
                    writer.writerow(user)
            
            return send_file(export_path, as_attachment=True, download_name=export_filename)
            
        except Exception as e:
            return jsonify({'success': False, 'message': f'导出失败: {str(e)}'})
    
    @app.route('/api/admin/export_learning_records')
    def api_admin_export_learning_records():
        """导出学习记录API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})
        
        try:
            # 获取所有学习记录
            records = db.execute_query("""
                SELECT wr.id, u.username, w.english_word, w.chinese_meaning,
                       wr.duration_seconds, wr.user_input, wr.correct_answer,
                       wr.is_correct, wr.created_at
                FROM word_record wr
                JOIN user u ON wr.user_id = u.id
                JOIN word w ON wr.word_id = w.id
                ORDER BY wr.created_at DESC
            """)
            
            # 创建导出目录
            export_dir = 'data/exports'
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成导出文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_filename = f'learning_records_export_{timestamp}.csv'
            export_path = os.path.join(export_dir, export_filename)
            
            # 写入CSV文件
            with open(export_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['id', 'username', 'english_word', 'chinese_meaning',
                            'duration_seconds', 'user_input', 'correct_answer',
                            'is_correct', 'created_at']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for record in records:
                    writer.writerow(record)
            
            return send_file(export_path, as_attachment=True, download_name=export_filename)
            
        except Exception as e:
            return jsonify({'success': False, 'message': f'导出失败: {str(e)}'})
    
    # ===== 单词导入API =====
    
    @app.route('/api/admin/import_words_csv', methods=['POST'])
    def api_admin_import_words_csv():
        """CSV格式单词导入API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})
        
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有上传文件'})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'})
        
        if not file.filename.lower().endswith('.csv'):
            return jsonify({'success': False, 'message': '请上传CSV格式文件'})
        
        try:
            # 保存上传的文件
            filename = secure_filename(file.filename)
            upload_path = os.path.join('data/uploads', filename)
            os.makedirs('data/uploads', exist_ok=True)
            file.save(upload_path)
            
            # 读取CSV文件
            imported_count = 0
            skipped_count = 0
            error_count = 0
            
            with open(upload_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                
                for row in reader:
                    try:
                        english_word = row.get('english_word', '').strip()
                        chinese_meaning = row.get('chinese_meaning', '').strip()
                        section = row.get('section', '').strip()
                        
                        if not english_word or not chinese_meaning:
                            skipped_count += 1
                            continue
                        
                        # 检查是否已存在
                        existing = Word.get_by_english_word(english_word)
                        if existing:
                            skipped_count += 1
                            continue
                        
                        # 创建新单词
                        Word.create(english_word, chinese_meaning, section)
                        imported_count += 1
                        
                    except Exception as e:
                        error_count += 1
                        print(f"Import error for row {row}: {e}")
            
            # 删除临时文件
            os.remove(upload_path)
            
            return jsonify({
                'success': True,
                'message': f'导入完成：成功{imported_count}个，跳过{skipped_count}个，错误{error_count}个',
                'imported_count': imported_count,
                'skipped_count': skipped_count,
                'error_count': error_count
            })
            
        except Exception as e:
            return jsonify({'success': False, 'message': f'导入失败: {str(e)}'})

    @app.route('/admin/batch_update_learning_type', methods=['POST'])
    def admin_batch_update_learning_type():
        """批量更新单词学习类型API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})

        try:
            data = request.get_json()
            word_ids = data.get('word_ids', [])
            learning_requirement = data.get('learning_requirement', 'spelling')
            admin_password = data.get('admin_password', '')
            
            # 验证管理员密码
            if not admin_password:
                return jsonify({'success': False, 'error': '请输入管理员密码'})

            # 获取admin用户并验证密码
            admin_user = User.get_by_username('admin')
            if not admin_user:
                return jsonify({'success': False, 'error': '管理员用户不存在'})

            # 使用AuthService验证密码（支持多种加密格式）
            from ..services.auth.auth_service import AuthService
            if not AuthService.verify_password(admin_user['password'], admin_password):
                return jsonify({'success': False, 'error': '管理员密码错误'})
            
            # 验证参数
            if not word_ids:
                return jsonify({'success': False, 'error': '请选择要修改的单词'})
            
            if learning_requirement not in ['spelling', 'recognition']:
                return jsonify({'success': False, 'error': '无效的学习类型'})
            
            # 批量更新learning_requirement字段
            placeholders = ','.join(['?' for _ in word_ids])
            query = f"""
                UPDATE word 
                SET learning_requirement = ? 
                WHERE id IN ({placeholders})
            """
            
            # 使用统一的数据库管理器
            updated_count = db.execute_update(query, [learning_requirement] + word_ids)
            
            return jsonify({
                'success': True,
                'message': f'成功更新{updated_count}个单词的学习类型为{learning_requirement}',
                'updated_count': updated_count
            })
            
        except Exception as e:
            return jsonify({'success': False, 'error': f'批量更新失败: {str(e)}'})
    
    @app.route('/api/admin/find_duplicate_words')
    def api_admin_find_duplicate_words():
        """查找重复的英文单词API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})

        try:
            # 查找重复的英文单词
            query = """
                SELECT english_word, COUNT(*) as count, GROUP_CONCAT(id) as ids
                FROM word 
                GROUP BY english_word 
                HAVING COUNT(*) > 1 
                ORDER BY count DESC, english_word
            """
            
            duplicates = db.execute_query(query)
            
            # 为每个重复单词获取详细信息
            duplicate_details = []
            for dup in duplicates:
                word_ids = [int(id_str) for id_str in dup['ids'].split(',')]
                
                # 获取每个重复单词的详细信息
                detail_query = """
                    SELECT id, english_word, chinese_meaning, section, learning_requirement,
                           (SELECT COUNT(*) FROM user_word WHERE word_id = word.id) as user_count,
                           (SELECT COUNT(*) FROM word_record WHERE word_id = word.id) as record_count,
                           (SELECT COUNT(*) FROM learning_plan WHERE word_id = word.id) as plan_count
                    FROM word 
                    WHERE id IN ({})
                    ORDER BY id
                """.format(','.join(['?'] * len(word_ids)))
                
                word_details = db.execute_query(detail_query, word_ids)
                
                duplicate_details.append({
                    'english_word': dup['english_word'],
                    'total_count': dup['count'],
                    'words': [dict(word) for word in word_details]
                })
            
            return jsonify({
                'success': True,
                'duplicates': duplicate_details,
                'total_groups': len(duplicate_details)
            })
            
        except Exception as e:
            return jsonify({'success': False, 'message': f'查找重复单词失败: {str(e)}'})
    
    @app.route('/api/admin/auto_merge_duplicate_words', methods=['POST'])
    def api_admin_auto_merge_duplicate_words():
        """智能合并重复单词API - 自动选择最佳保留单词"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})

        try:
            data = request.get_json()
            admin_password = data.get('admin_password')
            
            # 验证管理员密码
            if not admin_password:
                return jsonify({'success': False, 'error': '请输入管理员密码'})

            admin_user = User.get_by_username('admin')
            if not admin_user:
                return jsonify({'success': False, 'error': '管理员用户不存在'})

            from ..services.auth.auth_service import AuthService
            if not AuthService.verify_password(admin_user['password'], admin_password):
                return jsonify({'success': False, 'error': '管理员密码错误'})
            
            # 查找所有重复单词组
            duplicates_query = """
                SELECT english_word, COUNT(*) as count, GROUP_CONCAT(id) as ids
                FROM word 
                GROUP BY english_word 
                HAVING COUNT(*) > 1 
                ORDER BY count DESC, english_word
            """
            
            duplicates = db.execute_query(duplicates_query)
            
            if not duplicates:
                return jsonify({
                    'success': True,
                    'message': '没有发现重复单词',
                    'merged_groups': [],
                    'total_merged': 0
                })
            
            merged_groups = []
            total_merged_words = 0
            
            # 使用事务执行合并操作
            with enhanced_db.transaction() as conn:
                cursor = conn.cursor()
                
                for dup in duplicates:
                    english_word = dup['english_word']
                    word_ids = [int(id_str) for id_str in dup['ids'].split(',')]
                    
                    # 获取每个单词的详细统计信息
                    detail_query = """
                        SELECT id, english_word, chinese_meaning, section, learning_requirement,
                               (SELECT COUNT(*) FROM user_word WHERE word_id = word.id) as user_count,
                               (SELECT COUNT(*) FROM word_record WHERE word_id = word.id) as record_count,
                               (SELECT COUNT(*) FROM learning_plan WHERE word_id = word.id) as plan_count
                        FROM word 
                        WHERE id IN ({})
                        ORDER BY id
                    """.format(','.join(['?'] * len(word_ids)))
                    
                    cursor.execute(detail_query, word_ids)
                    word_details = [dict(row) for row in cursor.fetchall()]
                    
                    # 智能选择保留单词：
                    # 1. 优先选择学习记录最多的
                    # 2. 其次选择用户关系最多的
                    # 3. 最后选择ID最小的（最早创建的）
                    best_word = max(word_details, key=lambda w: (
                        w['record_count'], 
                        w['user_count'], 
                        -w['id']  # 负号使ID小的排在前面
                    ))
                    
                    target_word_id = best_word['id']
                    merge_word_ids = [w['id'] for w in word_details if w['id'] != target_word_id]
                    
                    # 智能合并中文释义
                    chinese_meanings = [w['chinese_meaning'] for w in word_details if w['chinese_meaning']]
                    merged_chinese = '; '.join(set(chinese_meanings))  # 去重并用分号连接
                    
                    # 选择最好的section和learning_requirement
                    sections = [w['section'] for w in word_details if w['section']]
                    best_section = sections[0] if sections else None
                    
                    # 更新保留单词的信息
                    cursor.execute("""
                        UPDATE word 
                        SET chinese_meaning = ?, section = ?
                        WHERE id = ?
                    """, (merged_chinese, best_section, target_word_id))
                    
                    # 执行数据迁移
                    if merge_word_ids:
                        placeholders = ','.join(['?'] * len(merge_word_ids))
                        
                        # 1. 更新 user_word 表
                        cursor.execute(f"""
                            UPDATE user_word 
                            SET word_id = ? 
                            WHERE word_id IN ({placeholders})
                            AND NOT EXISTS (
                                SELECT 1 FROM user_word uw2 
                                WHERE uw2.user_id = user_word.user_id 
                                AND uw2.word_id = ?
                            )
                        """, [target_word_id] + merge_word_ids + [target_word_id])
                        
                        # 删除重复的user_word记录
                        cursor.execute(f"""
                            DELETE FROM user_word 
                            WHERE word_id IN ({placeholders})
                        """, merge_word_ids)
                        
                        # 2. 更新 word_record 表
                        cursor.execute(f"""
                            UPDATE word_record 
                            SET word_id = ? 
                            WHERE word_id IN ({placeholders})
                        """, [target_word_id] + merge_word_ids)
                        
                        # 3. 更新 learning_plan 表
                        cursor.execute(f"""
                            UPDATE learning_plan 
                            SET word_id = ? 
                            WHERE word_id IN ({placeholders})
                            AND NOT EXISTS (
                                SELECT 1 FROM learning_plan lp2 
                                WHERE lp2.user_id = learning_plan.user_id 
                                AND lp2.word_id = ? 
                                AND lp2.planned_date = learning_plan.planned_date
                            )
                        """, [target_word_id] + merge_word_ids + [target_word_id])
                        
                        # 删除重复的learning_plan记录
                        cursor.execute(f"""
                            DELETE FROM learning_plan 
                            WHERE word_id IN ({placeholders})
                        """, merge_word_ids)
                        
                        # 4. 删除被合并的单词记录
                        cursor.execute(f"""
                            DELETE FROM word 
                            WHERE id IN ({placeholders})
                        """, merge_word_ids)
                        
                        total_merged_words += len(merge_word_ids)
                        
                        merged_groups.append({
                            'english_word': english_word,
                            'kept_word_id': target_word_id,
                            'merged_word_ids': merge_word_ids,
                            'merged_chinese': merged_chinese,
                            'total_records': sum(w['record_count'] for w in word_details),
                            'total_users': sum(w['user_count'] for w in word_details)
                        })
            
            return jsonify({
                'success': True,
                'message': f'智能合并完成！处理了 {len(merged_groups)} 组重复单词，删除了 {total_merged_words} 个重复记录',
                'merged_groups': merged_groups,
                'total_merged': total_merged_words
            })
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"智能合并异常详情: {error_details}")
            return jsonify({'success': False, 'error': f'智能合并失败: {str(e)}', 'details': error_details})
    
    @app.route('/api/admin/test_merge', methods=['POST'])
    def api_admin_test_merge():
        """测试合并功能的简单端点"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})
        
        try:
            data = request.get_json()
            admin_password = data.get('admin_password')
            
            # 验证管理员密码
            if not admin_password:
                return jsonify({'success': False, 'error': '请输入管理员密码'})

            admin_user = User.get_by_username('admin')
            if not admin_user:
                return jsonify({'success': False, 'error': '管理员用户不存在'})

            from ..services.auth.auth_service import AuthService
            if not AuthService.verify_password(admin_user['password'], admin_password):
                return jsonify({'success': False, 'error': '管理员密码错误'})
            
            # 简单的数据库测试查询
            test_query = "SELECT COUNT(*) as count FROM word"
            result = db.execute_query(test_query)
            word_count = result[0]['count'] if result else 0
            
            return jsonify({
                'success': True,
                'message': f'测试成功！数据库中有 {word_count} 个单词',
                'word_count': word_count
            })
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"测试合并异常详情: {error_details}")
            return jsonify({'success': False, 'error': f'测试失败: {str(e)}', 'details': error_details})
    
    @app.route('/api/admin/import_words_json', methods=['POST'])
    def api_admin_import_words_json():
        """JSON格式单词导入API"""
        has_permission, error_msg = check_admin_permission()
        if not has_permission:
            return jsonify({'success': False, 'message': error_msg})
        
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有上传文件'})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'})
        
        if not file.filename.lower().endswith('.json'):
            return jsonify({'success': False, 'message': '请上传JSON格式文件'})
        
        try:
            # 读取JSON文件
            json_data = json.load(file)
            
            if not isinstance(json_data, list):
                return jsonify({'success': False, 'message': 'JSON文件格式错误，应为数组格式'})
            
            imported_count = 0
            skipped_count = 0
            error_count = 0
            
            for item in json_data:
                try:
                    english_word = item.get('english_word', '').strip()
                    chinese_meaning = item.get('chinese_meaning', '').strip()
                    section = item.get('section', '').strip()
                    
                    if not english_word or not chinese_meaning:
                        skipped_count += 1
                        continue
                    
                    # 检查是否已存在
                    existing = Word.get_by_english_word(english_word)
                    if existing:
                        skipped_count += 1
                        continue
                    
                    # 创建新单词
                    Word.create(english_word, chinese_meaning, section)
                    imported_count += 1
                    
                except Exception as e:
                    error_count += 1
                    print(f"Import error for item {item}: {e}")
            
            return jsonify({
                'success': True,
                'message': f'导入完成：成功{imported_count}个，跳过{skipped_count}个，错误{error_count}个',
                'imported_count': imported_count,
                'skipped_count': skipped_count,
                'error_count': error_count
            })
            
        except Exception as e:
            return jsonify({'success': False, 'message': f'导入失败: {str(e)}'})

