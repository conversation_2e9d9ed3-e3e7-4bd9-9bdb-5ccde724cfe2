"""
路由注册中心
统一管理和注册所有路由模块，确保路由的正确加载和向后兼容性
"""
from .page_routes import register_page_routes
from .auth_routes import register_auth_routes
from .learning_routes import register_learning_routes
from .admin_routes import register_admin_routes
from .api_routes import register_api_routes
from .feature_learning_routes import feature_learning_bp


def register_all_routes(app):
    """
    注册所有路由模块到Flask应用
    
    Args:
        app: Flask应用实例
    """
    try:
        # 1. 注册页面路由（最高优先级）
        register_page_routes(app)

        # 2. 注册认证路由
        register_auth_routes(app)

        # 3. 注册学习路由
        register_learning_routes(app)

        # 4. 注册管理员路由
        register_admin_routes(app)

        # 5. 注册特征学习路由
        app.register_blueprint(feature_learning_bp, url_prefix='/feature-learning')

        # 6. 注册通用API路由（最低优先级）
        register_api_routes(app)
        
    except Exception as e:
        print(f"❌ 路由注册失败: {e}")
        raise



def get_route_info():
    """
    获取路由信息（用于调试和监控）
    
    Returns:
        dict: 路由信息字典
    """
    return {
        'modules': [
            {
                'name': 'page_routes',
                'description': '页面渲染路由',
                'routes_count': 22,
                'file': 'src/routes/page_routes.py'
            },
            {
                'name': 'auth_routes',
                'description': '用户认证路由',
                'routes_count': 6,
                'file': 'src/routes/auth_routes.py'
            },
            {
                'name': 'learning_routes',
                'description': '学习功能路由',
                'routes_count': 20,
                'file': 'src/routes/learning_routes.py'
            },
            {
                'name': 'admin_routes',
                'description': '管理员功能路由',
                'routes_count': 8,
                'file': 'src/routes/admin_routes.py'
            },
            {
                'name': 'api_routes',
                'description': '通用API路由',
                'routes_count': 15,
                'file': 'src/routes/api_routes.py'
            },
            {
                'name': 'feature_learning_routes',
                'description': '特征学习功能路由',
                'routes_count': 8,
                'file': 'src/routes/feature_learning_routes.py'
            }
        ],
        'total_routes': 79,
        'version': '2.0.0',
        'architecture': 'modular'
    }


def validate_routes(app):
    """
    验证路由配置的正确性
    
    Args:
        app: Flask应用实例
        
    Returns:
        dict: 验证结果
    """
    validation_results = {
        'success': True,
        'errors': [],
        'warnings': [],
        'statistics': {}
    }
    
    try:
        # 检查重复路由
        routes = {}
        duplicates = []
        
        for rule in app.url_map.iter_rules():
            route_key = f"{rule.rule}:{','.join(sorted(rule.methods))}"
            if route_key in routes:
                duplicates.append({
                    'route': rule.rule,
                    'methods': list(rule.methods),
                    'endpoints': [routes[route_key], rule.endpoint]
                })
            else:
                routes[route_key] = rule.endpoint
        
        if duplicates:
            validation_results['errors'].extend([
                f"重复路由: {dup['route']} ({dup['methods']})" for dup in duplicates
            ])
            validation_results['success'] = False
        
        # 检查关键路由是否存在
        critical_routes = [
            '/',
            '/login',
            '/dashboard',
            '/api/login',
            '/api/register',
            '/api/daily_learning_plan'
        ]
        
        existing_routes = [rule.rule for rule in app.url_map.iter_rules()]
        missing_routes = [route for route in critical_routes if route not in existing_routes]
        
        if missing_routes:
            validation_results['errors'].extend([
                f"缺少关键路由: {route}" for route in missing_routes
            ])
            validation_results['success'] = False
        
        # 统计信息
        validation_results['statistics'] = {
            'total_routes': len(existing_routes),
            'unique_routes': len(routes),
            'duplicate_count': len(duplicates),
            'critical_routes_count': len(critical_routes),
            'missing_critical_routes': len(missing_routes)
        }
        
        # 警告检查
        if len(existing_routes) > 100:
            validation_results['warnings'].append("路由数量过多，建议进一步模块化")
        
        if len(missing_routes) == 0 and len(duplicates) == 0:
            validation_results['warnings'].append("所有路由验证通过")
        
    except Exception as e:
        validation_results['success'] = False
        validation_results['errors'].append(f"路由验证异常: {str(e)}")
    
    return validation_results


# 路由模块元信息
ROUTE_MODULES = {
    'page_routes': {
        'name': '页面路由模块',
        'description': '处理所有页面渲染相关的路由',
        'routes': [
            '/', '/login', '/register', '/dashboard', '/learning',
            '/learning_test', '/learning_simple', '/learning_fixed', '/learning_card',
            '/vocabulary_book', '/statistics', '/learning_plan', '/profile',
            '/settings', '/help', '/about', '/api/words/import', '/logout',
            '/admin/database', '/audio/<path:filename>'
        ],
        'dependencies': ['models.User', 'services.LearningPlanService', 'services.StatisticsService']
    },
    'auth_routes': {
        'name': '认证路由模块',
        'description': '处理用户注册、登录、登出相关的API路由',
        'routes': [
            '/api/register', '/api/login', '/api/logout',
            '/api/change_password', '/api/reset_password', '/api/check_session'
        ],
        'dependencies': ['services.AuthService', 'services.LearningPlanService', 'services.LearningService']
    },
    'learning_routes': {
        'name': '学习路由模块',
        'description': '处理学习功能相关的API路由',
        'routes': [
            '/api/daily_learning_plan', '/get_daily_words', '/update_record',
            '/api/submit_answer', '/api/finish_learning', '/api/add_to_vocabulary',
            '/add_to_vocabulary', '/api/vocabulary_book', '/api/remove_from_vocabulary',
            '/api/get_hint', '/api/get_memory_aid', '/api/skip_word',
            '/api/mark_difficult', '/api/word_audio/<int:word_id>',
            '/api/learning_progress', '/api/word_details/<int:word_id>'
        ],
        'dependencies': ['models.*', 'services.LearningPlanService', 'services.LearningService', 'services.StatisticsService']
    },
    'admin_routes': {
        'name': '管理员路由模块',
        'description': '处理管理员功能相关的API路由',
        'routes': [
            '/api/admin/database_info', '/api/admin/backup_database',
            '/api/admin/reset_database', '/api/admin/export_users',
            '/api/admin/export_learning_records', '/api/admin/import_words_csv',
            '/api/admin/import_words_json'
        ],
        'dependencies': ['models.*', 'models.db']
    },
    'api_routes': {
        'name': '通用API路由模块',
        'description': '处理统计、积分、健康检查等通用API路由',
        'routes': [
            '/api/user_statistics', '/api/learning_history', '/api/daily_plan_overview',
            '/api/proficiency_distribution', '/api/user_points', '/api/update_points',
            '/api/redeem_voucher', '/api/health', '/api/version', '/api/system_status',
            '/api/search_words', '/api/random_words', '/api/user_profile', '/api/update_profile'
        ],
        'dependencies': ['models.User', 'services.StatisticsService']
    },
    'feature_learning_routes': {
        'name': '特征学习路由模块',
        'description': '处理AI特征学习、个性化洞察和智能推荐功能',
        'routes': [
            '/feature-learning/insights', '/feature-learning/api/insights',
            '/feature-learning/api/predict-difficulty', '/feature-learning/api/recommendations',
            '/feature-learning/api/update-patterns', '/feature-learning/api/enhanced-similarity',
            '/feature-learning/admin/config', '/feature-learning/admin/api/config',
            '/feature-learning/api/similarity-test'
        ],
        'dependencies': ['services.feature_learning.*', 'models.feature_learning.*']
    }
}
