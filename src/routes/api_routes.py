"""
通用API路由模块
处理统计、积分、健康检查、错误处理等通用API路由
严格按照API_CONTRACT.md规范实现标准化响应格式
"""
from flask import request, jsonify, session
from datetime import date

from ..models import User
from ..services import StatisticsService
from ..core import get_logger
from ..api import (
    APIResponse, APIErrorCodes, APIMessages,
    api_response, require_auth
)
from ..services.pattern.recommendation_config import recommendation_config_manager

logger = get_logger(__name__)


def register_api_routes(app):
    """注册通用API路由"""

    # ===== 统计API =====

    @app.route('/api/user_statistics')
    @require_auth
    @api_response
    def api_user_statistics():
        """获取用户统计数据API - 标准化版本"""
        user_id = session['user_id']

        try:
            statistics = StatisticsService.get_user_statistics(user_id)

            if not statistics:
                return APIResponse.json_error(
                    APIErrorCodes.BUSINESS_LOGIC_ERROR,
                    "无法获取用户统计数据",
                    {"user_id": user_id}
                )

            return APIResponse.json_success(
                data=statistics,
                message="用户统计数据获取成功"
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"获取用户统计数据失败: {str(e)}",
                {"user_id": user_id}
            )

    @app.route('/api/learning_history')
    def api_learning_history():
        """获取学习历史API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']
        days = request.args.get('days', 30, type=int)

        history = StatisticsService.get_learning_history(user_id, days)

        return jsonify({'success': True, **history})


    @app.route('/api/proficiency_distribution')
    def api_proficiency_distribution():
        """获取熟练度分布API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']
        distribution = StatisticsService.get_proficiency_distribution(user_id)

        return jsonify({'success': True, 'distribution': distribution})

    # ===== 积分管理API =====

    @app.route('/api/get_user_points')
    def api_get_user_points():
        """获取用户积分API（兼容前端）- 已废弃，请使用 /api/user_points"""
        # 添加废弃警告
        import warnings
        warnings.warn("API /api/get_user_points 已废弃，请使用 /api/user_points",
                     DeprecationWarning, stacklevel=2)

        if 'user_id' not in session:
            return jsonify({'points': 0, 'vouchers': 0, 'coupons': 0,
                          'deprecated': True, 'new_api': '/api/user_points'})

        user_id = session['user_id']
        user = User.get_by_id(user_id)

        if user:
            return jsonify({
                'points': user['points'],
                'vouchers': user['vouchers'],
                'coupons': user['vouchers'],  # 兼容前端
                '积分': user['points'],
                '购物券': user['vouchers']
            })

        return jsonify({'points': 0, 'vouchers': 0, 'coupons': 0, '积分': 0, '购物券': 0})

    @app.route('/api/user_points')
    @require_auth
    @api_response
    def api_user_points():
        """获取用户积分API - 标准化版本"""
        user_id = session['user_id']

        try:
            user = User.get_by_id(user_id)

            if not user:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_USER_ID,
                    "用户不存在",
                    {"user_id": user_id}
                )

            return APIResponse.json_success(
                data={
                    'points': user['points'],
                    'vouchers': user['vouchers']
                },
                message="用户积分获取成功"
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"获取用户积分失败: {str(e)}",
                {"user_id": user_id}
            )

    @app.route('/api/update_user_points', methods=['POST'])
    @require_auth
    @api_response
    def api_update_user_points():
        """更新用户积分API - 标准化版本"""
        data = request.get_json()
        if not data:
            return APIResponse.json_error(
                APIErrorCodes.INVALID_DATA_FORMAT,
                "请求数据不能为空"
            )

        user_id = session['user_id']
        points_change = data.get('points_change', 0)
        reason = data.get('reason', '积分变更')

        # 验证积分变更值
        if not isinstance(points_change, (int, float)) or points_change == 0:
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                "积分变更值必须是非零数字",
                {"points_change": points_change}
            )

        try:
            # 获取更新前的用户信息
            old_user = User.get_by_id(user_id)
            if not old_user:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_USER_ID,
                    "用户不存在",
                    {"user_id": user_id}
                )

            old_points = old_user['points']

            # 更新用户积分
            User.update_points(user_id, points_change)

            # 获取更新后的用户信息
            user = User.get_by_id(user_id)
            new_points = user['points'] if user else old_points

            return APIResponse.json_success(
                data={
                    'old_points': old_points,
                    'new_points': new_points,
                    'points_change': points_change,
                    'reason': reason
                },
                message=APIMessages.POINTS_UPDATED
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                f"积分更新失败: {str(e)}",
                {"user_id": user_id, "points_change": points_change}
            )

    @app.route('/api/update_user_vouchers', methods=['POST'])
    @require_auth
    @api_response
    def api_update_user_vouchers():
        """更新用户购物券API - 标准化版本"""
        data = request.get_json()
        if not data:
            return APIResponse.json_error(
                APIErrorCodes.INVALID_DATA_FORMAT,
                "请求数据不能为空"
            )

        user_id = session['user_id']
        vouchers_change = data.get('vouchers_change', 0)
        reason = data.get('reason', '购物券变更')

        # 验证购物券变更值
        if (not isinstance(vouchers_change, (int, float)) or
            vouchers_change == 0):
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                "购物券变更值必须是非零数字",
                {"vouchers_change": vouchers_change}
            )

        try:
            # 如果是增加购物券，检查每日限制
            if vouchers_change > 0:
                today = date.today()
                daily_earned = _get_daily_vouchers_earned(user_id, today)
                max_daily = 2
                remaining = max_daily - daily_earned

                if remaining <= 0:
                    return APIResponse.json_error(
                        APIErrorCodes.BUSINESS_LOGIC_ERROR,
                        f"今日购物券已达上限({max_daily}张)，明天再来吧！",
                        {
                            "daily_earned": daily_earned,
                            "daily_limit": max_daily,
                            "remaining": 0
                        }
                    )

                # 确保不超过每日限制
                if vouchers_change > remaining:
                    vouchers_change = remaining

            # 获取更新前的用户信息
            user_before = User.get_by_id(user_id)
            if not user_before:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_USER_ID,
                    "用户不存在",
                    {"user_id": user_id}
                )

            old_vouchers = user_before['vouchers']

            # 更新用户购物券
            User.update_vouchers(user_id, vouchers_change)

            # 获取更新后的用户信息
            user_after = User.get_by_id(user_id)
            new_vouchers = user_after['vouchers'] if user_after else old_vouchers

            return APIResponse.json_success(
                data={
                    'old_vouchers': old_vouchers,
                    'new_vouchers': new_vouchers,
                    'vouchers_change': vouchers_change,
                    'reason': reason
                },
                message=APIMessages.VOUCHERS_UPDATED
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                f"购物券更新失败: {str(e)}",
                {"user_id": user_id, "vouchers_change": vouchers_change}
            )

    @app.route('/api/update_points', methods=['POST'])
    def api_update_points():
        """更新用户积分API（管理员功能）"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        # 检查管理员权限
        user = User.get_by_id(session['user_id'])
        if not user or user['username'] != 'admin':
            return jsonify({'success': False, 'message': '权限不足'})

        data = request.get_json()
        target_user_id = data.get('user_id')
        points_change = data.get('points_change', 0)

        if not target_user_id:
            return jsonify({'success': False, 'message': '缺少用户ID'})

        try:
            User.update_points(target_user_id, points_change)
            return jsonify({'success': True, 'message': '积分更新成功'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'积分更新失败: {str(e)}'})

    @app.route('/api/redeem_voucher', methods=['POST'])
    def api_redeem_voucher():
        """兑换代金券API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        user_id = session['user_id']
        points_cost = data.get('points_cost', 100)  # 默认100积分兑换1代金券

        user = User.get_by_id(user_id)
        if not user:
            return jsonify({'success': False, 'message': '用户不存在'})

        if user['points'] < points_cost:
            return jsonify({'success': False, 'message': '积分不足'})

        try:
            # 扣除积分，增加代金券
            User.update_points(user_id, -points_cost)
            User.update_vouchers(user_id, 1)

            return jsonify({
                'success': True,
                'message': f'成功兑换1张代金券，消耗{points_cost}积分',
                'remaining_points': user['points'] - points_cost,
                'vouchers': user['vouchers'] + 1
            })
        except Exception as e:
            return jsonify({'success': False, 'message': f'兑换失败: {str(e)}'})

    # ===== 购物券API =====

    @app.route('/api/voucher/popup-info')
    def api_voucher_popup_info():
        """获取购物券弹窗信息API"""
        if 'user_id' not in session:
            return jsonify({'current_vouchers': 0, 'show_popup': False})

        user_id = session['user_id']
        user = User.get_by_id(user_id)

        if user:
            return jsonify({
                'current_vouchers': user['vouchers'],
                'show_popup': user['vouchers'] > 0,
                'message': f'您有{user["vouchers"]}张购物券可用' if user['vouchers'] > 0 else '暂无购物券'
            })

        return jsonify({'current_vouchers': 0, 'show_popup': False})

    @app.route('/api/voucher/record-daily', methods=['POST'])
    def api_voucher_record_daily():
        """记录今日购物券获得API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        user_id = session['user_id']
        vouchers_earned = data.get('vouchers_earned', 0)

        try:
            from datetime import date
            today = date.today()

            # 记录今日购物券
            _record_daily_voucher(user_id, today, vouchers_earned)

            # 获取更新后的状态
            daily_earned = _get_daily_vouchers_earned(user_id, today)
            max_daily = 2

            return jsonify({
                'success': True,
                'daily_earned': daily_earned,
                'daily_limit': max_daily,
                'remaining': max_daily - daily_earned
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'记录失败: {str(e)}'
            })

    @app.route('/api/voucher/daily-status')
    def api_voucher_daily_status():
        """获取今日购物券状态API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']
        from datetime import date
        today = date.today()

        daily_earned = _get_daily_vouchers_earned(user_id, today)
        max_daily = 2
        remaining = max_daily - daily_earned

        return jsonify({
            'success': True,
            'daily_earned': daily_earned,
            'daily_limit': max_daily,
            'remaining': remaining,
            'can_earn_more': remaining > 0
        })

    @app.route('/api/voucher/evaluate-session', methods=['POST'])
    def api_voucher_evaluate_session():
        """评估学习会话获得购物券API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        user_id = session['user_id']

        # 获取会话数据
        words_learned = data.get('words_learned', 0)
        correct_rate = data.get('correct_rate', 0.0)
        session_duration = data.get('session_duration', 0)

        try:
            # 检查今日已获得的购物券数量（简单实现）
            from datetime import date
            today = date.today()
            session_key = f'daily_vouchers_{user_id}_{today.strftime("%Y-%m-%d")}'
            daily_vouchers_earned = session.get(session_key, 0)

            # 每日最多获得2张购物券
            if daily_vouchers_earned >= 2:
                return jsonify({
                    'success': True,
                    'vouchers_earned': 0,
                    'message': '今日购物券已达上限（2张），明天再来吧！',
                    'show_celebration': False
                })

            # 简单的购物券奖励逻辑
            vouchers_earned = 0

            # 根据学习表现给予购物券，但不超过每日限制
            if words_learned >= 20 and correct_rate >= 0.8:
                vouchers_earned = min(2, 2 - daily_vouchers_earned)
            elif words_learned >= 10 and correct_rate >= 0.7:
                vouchers_earned = min(1, 2 - daily_vouchers_earned)

            if vouchers_earned > 0:
                # 更新用户购物券
                User.update_vouchers(user_id, vouchers_earned)

                # 记录今日获得的购物券（使用session简单记录）
                session[session_key] = daily_vouchers_earned + vouchers_earned

                return jsonify({
                    'success': True,
                    'vouchers_earned': vouchers_earned,
                    'message': f'恭喜！您获得了{vouchers_earned}张购物券！',
                    'show_celebration': True
                })
            else:
                return jsonify({
                    'success': True,
                    'vouchers_earned': 0,
                    'message': '继续努力，下次可以获得购物券！',
                    'show_celebration': False
                })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'评估会话失败: {str(e)}'
            })

    @app.route('/api/get_low_correct_rate_words')
    def api_get_low_correct_rate_words():
        """获取低正确率单词API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']
        threshold = request.args.get('threshold', 0.6, type=float)  # 默认60%以下
        limit = request.args.get('limit', 20, type=int)

        try:
            from ..models import WordRecord

            # 获取低正确率单词
            low_accuracy_words = WordRecord.get_difficult_words(user_id, threshold, limit)

            return jsonify({
                'success': True,
                'words': low_accuracy_words,
                'threshold': threshold,
                'count': len(low_accuracy_words)
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取低正确率单词失败: {str(e)}'
            })

    @app.route('/api/check_learning_completion')
    def api_check_learning_completion():
        """检查学习完成度API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']

        try:
            from ..models import WordRecord, LearningPlan
            from datetime import date

            # 获取今日学习记录
            today_records = WordRecord.get_today_records(user_id)

            # 获取今日计划
            from ..services import LearningPlanService
            daily_plan = LearningPlanService.get_daily_plan(user_id)

            # 计算统计数据
            total_attempts = len(today_records)
            correct_attempts = sum(1 for r in today_records if r['is_correct'])
            accuracy = (correct_attempts / total_attempts * 100) if total_attempts > 0 else 0

            # 计算连续答对次数
            consecutive_correct = 0
            for record in reversed(today_records):
                if record['is_correct']:
                    consecutive_correct += 1
                else:
                    break

            # 计算掌握程度
            completed_count = sum(1 for item in daily_plan if item['star_level'] >= 4)
            mastery_ratio = (completed_count / len(daily_plan) * 100) if daily_plan else 0

            # 判断是否应该停止学习
            should_stop = False
            reason = "学习状态良好"
            recommendations = []

            if total_attempts >= 50 and accuracy < 60:
                should_stop = True
                reason = "正确率偏低，建议休息"
                recommendations.append("建议休息后再继续学习")
                recommendations.append("可以复习之前学过的单词")
            elif total_attempts >= 100:
                should_stop = True
                reason = "学习时间较长，建议适当休息"
                recommendations.append("今天学习量已足够")
                recommendations.append("保持规律学习习惯")
            elif mastery_ratio >= 90:
                should_stop = True
                reason = "今日计划基本完成"
                recommendations.append("今日学习目标已达成")
                recommendations.append("可以自由探索其他单词")
            else:
                recommendations.append("继续保持良好的学习节奏")
                if accuracy >= 80:
                    recommendations.append("正确率很高，可以适当增加难度")
                elif accuracy >= 60:
                    recommendations.append("正确率良好，继续加油")
                else:
                    recommendations.append("注意理解单词含义，不要急于求成")

            # 生成鼓励语
            encouragement = {}
            if consecutive_correct >= 10:
                encouragement = {
                    'type': '连续答对',
                    'text': f'太棒了！连续答对{consecutive_correct}个单词！'
                }
            elif total_attempts >= 50:
                encouragement = {
                    'type': '学习坚持',
                    'text': f'今天已经学习了{total_attempts}个单词，很棒！'
                }
            elif accuracy >= 80:
                encouragement = {
                    'type': '正确率高',
                    'text': f'正确率{accuracy:.1f}%，表现优秀！'
                }
            else:
                encouragement = {
                    'type': '继续加油',
                    'text': '学习是一个循序渐进的过程，继续加油！'
                }

            return jsonify({
                'success': True,
                'should_stop': should_stop,
                'reason': reason,
                'total_attempts': total_attempts,
                'correct_attempts': correct_attempts,
                'accuracy': round(accuracy, 1),
                'consecutive_correct': consecutive_correct,
                'mastery_ratio': round(mastery_ratio, 1),
                'recommendations': recommendations,
                'encouragement': encouragement
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'检查学习完成度失败: {str(e)}'
            })


    # ===== 用户报告API =====

    @app.route('/api/v2/user_report')
    def api_user_report_v2():
        """获取用户学习报告API v2"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']

        try:
            # 获取用户统计信息
            stats = StatisticsService.get_user_statistics(user_id)

            # 获取学习历史
            history = StatisticsService.get_learning_history(user_id, 30)

            # 获取熟练度分布
            distribution = StatisticsService.get_proficiency_distribution(user_id)

            report = {
                'user_id': user_id,
                'generated_at': '2025-07-08',
                'statistics': stats,
                'learning_history': history,
                'proficiency_distribution': distribution,
                'summary': {
                    'total_words_learned': stats.get('words_actually_learned', 0),
                    'total_words_in_db': stats.get('total_words_in_db', 0),
                    'current_points': stats.get('points', 0),
                    'learning_progress': f"{(stats.get('words_actually_learned', 0) / max(stats.get('total_words_in_db', 1), 1) * 100):.1f}%"
                }
            }

            return jsonify({
                'success': True,
                'report': report,
                'message': '学习报告生成成功'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'生成报告失败: {str(e)}'
            })

    # ===== 健康检查API =====

    @app.route('/api/health')
    @api_response
    def api_health():
        """API健康检查 - 增强版，支持前端集成调试"""
        from datetime import datetime

        # 检查各个服务状态
        health_status = {
            'api_server': 'healthy',
            'database': 'healthy',
            'rewards_system': 'healthy',
            'learning_session': 'healthy'
        }

        # 检查数据库连接
        try:
            from ..models import User
            test_user = User.get_by_id(1)
            if test_user:
                health_status['database'] = 'healthy'
            else:
                health_status['database'] = 'warning'
        except Exception:
            health_status['database'] = 'error'

        # 检查奖励系统
        try:
            from ..services.learning.rewards_service import RewardsSystemService
            test_result = RewardsSystemService.get_user_rewards_summary(1)
            if test_result.get('success'):
                health_status['rewards_system'] = 'healthy'
            else:
                health_status['rewards_system'] = 'warning'
        except Exception:
            health_status['rewards_system'] = 'error'

        # 检查学习会话
        try:
            from ..services.learning.session_service import LearningSessionService
            # 简单的服务可用性检查
            health_status['learning_session'] = 'healthy'
        except Exception:
            health_status['learning_session'] = 'error'

        # 计算总体健康状态
        error_count = sum(1 for status in health_status.values() if status == 'error')
        warning_count = sum(1 for status in health_status.values() if status == 'warning')

        if error_count > 0:
            overall_status = 'error'
        elif warning_count > 0:
            overall_status = 'warning'
        else:
            overall_status = 'healthy'

        return APIResponse.json_success(
            data={
                'overall_status': overall_status,
                'services': health_status,
                'timestamp': datetime.now().isoformat(),
                'version': '2.0.0',
                'service': 'Word Learning App - AI-Beta Backend',
                'api_endpoints': {
                    'rewards_system': [
                        'POST /api/rewards_system/calculate',
                        'POST /api/rewards_system/purchase_check',
                        'GET /api/rewards_system/summary'
                    ],
                    'learning_session': [
                        'POST /api/learning_session/start',
                        'POST /api/learning_session/submit_and_advance'
                    ]
                },
                'frontend_integration': {
                    'rewards_system_ready': health_status['rewards_system'] == 'healthy',
                    'learning_session_ready': health_status['learning_session'] == 'healthy',
                    'all_systems_ready': overall_status == 'healthy',
                    'integration_guide': '/协作同步文件/FRONTEND_INTEGRATION_GUIDE.md'
                }
            },
            message=f"API健康状态: {overall_status}"
        )

    @app.route('/api/version')
    def api_version():
        """版本信息API"""
        return jsonify({
            'version': '2.0.0',
            'build_date': '2025-01-08',
            'features': [
                'Modular Architecture',
                'Service Layer Refactoring',
                'Route Layer Refactoring',
                'Enhanced Statistics',
                'Improved Learning Algorithm'
            ]
        })

    @app.route('/api/system_status')
    def api_system_status():
        """系统状态API"""
        try:
            # 检查数据库连接
            from ..models import db
            db.execute_query("SELECT 1")
            db_status = 'connected'
        except:
            db_status = 'disconnected'

        # 检查会话状态
        session_status = 'active' if 'user_id' in session else 'inactive'

        return jsonify({
            'success': True,
            'system_status': {
                'database': db_status,
                'session': session_status,
                'service': 'running'
            }
        })

    # ===== 错误处理API =====

    @app.errorhandler(400)
    def bad_request(error):
        """400错误处理"""
        return jsonify({
            'success': False,
            'error_code': 400,
            'message': '请求格式错误'
        }), 400

    @app.errorhandler(401)
    def unauthorized(error):
        """401错误处理"""
        return jsonify({
            'success': False,
            'error_code': 401,
            'message': '未授权访问'
        }), 401

    @app.errorhandler(403)
    def forbidden(error):
        """403错误处理"""
        return jsonify({
            'success': False,
            'error_code': 403,
            'message': '访问被禁止'
        }), 403

    @app.errorhandler(405)
    def method_not_allowed(error):
        """405错误处理"""
        return jsonify({
            'success': False,
            'error_code': 405,
            'message': '请求方法不被允许'
        }), 405

    @app.errorhandler(429)
    def too_many_requests(error):
        """429错误处理"""
        return jsonify({
            'success': False,
            'error_code': 429,
            'message': '请求过于频繁'
        }), 429

    # ===== 搜索API =====

    @app.route('/api/search_words')
    def api_search_words():
        """搜索单词API"""
        query = request.args.get('q', '').strip()
        limit = request.args.get('limit', 10, type=int)

        if not query:
            return jsonify({'success': False, 'message': '搜索关键词不能为空'})

        try:
            from ..models import Word
            results = Word.search(query, limit)

            return jsonify({
                'success': True,
                'query': query,
                'results': results,
                'count': len(results)
            })
        except Exception as e:
            return jsonify({'success': False, 'message': f'搜索失败: {str(e)}'})

    @app.route('/api/random_words')
    def api_random_words():
        """获取随机单词API"""
        count = request.args.get('count', 5, type=int)
        section = request.args.get('section', '')

        try:
            from ..models import Word
            if section:
                words = Word.get_random_by_section(section, count)
            else:
                words = Word.get_random(count)

            return jsonify({
                'success': True,
                'words': words,
                'count': len(words)
            })
        except Exception as e:
            return jsonify({'success': False, 'message': f'获取随机单词失败: {str(e)}'})

    # ===== 用户信息API =====

    @app.route('/api/user_profile')
    def api_user_profile():
        """获取用户档案API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']
        user = User.get_by_id(user_id)

        if not user:
            return jsonify({'success': False, 'message': '用户不存在'})

        # 获取用户统计信息
        stats = StatisticsService.get_user_statistics(user_id)

        return jsonify({
            'success': True,
            'profile': {
                'id': user['id'],
                'username': user['username'],
                'points': user['points'],
                'vouchers': user['vouchers'],
                'registration_date': user['registration_date'] or '',
                'created_at': user['created_at'] or ''
            },
            'statistics': stats
        })

    @app.route('/api/update_profile', methods=['POST'])
    def api_update_profile():
        """更新用户档案API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        user_id = session['user_id']

        # 目前只支持更新用户名（如果需要的话）
        # 这里可以根据需要扩展更多字段

        return jsonify({
            'success': True,
            'message': '档案更新功能待实现'
        })

    # 注册单词导入相关API
    register_word_import_apis(app)


# ===== 购物券每日限制辅助函数 =====

def _get_daily_vouchers_earned(user_id: int, date_obj) -> int:
    """获取用户今日已获得的购物券数量"""
    try:
        from ..models import db

        # 使用简单的方法：检查今日的word_record表中的学习记录
        # 这里简化实现，假设每次学习会话最多获得2张购物券
        # 实际应该有专门的voucher_log表来记录

        date_str = date_obj.strftime('%Y-%m-%d')

        # 临时使用session存储今日购物券数量
        session_key = f'daily_vouchers_{user_id}_{date_str}'

        if session_key in session:
            return session[session_key]
        else:
            # 首次访问，初始化为0
            session[session_key] = 0
            return 0

    except Exception as e:
        print(f"Error getting daily vouchers: {e}")
        return 0


def _record_daily_voucher(user_id: int, date_obj, vouchers_earned: int):
    """记录用户今日获得的购物券"""
    try:
        date_str = date_obj.strftime('%Y-%m-%d')
        session_key = f'daily_vouchers_{user_id}_{date_str}'

        # 累加今日获得的购物券数量
        current_count = session.get(session_key, 0)
        session[session_key] = current_count + vouchers_earned

        print(f"用户 {user_id} 今日已获得购物券: {session[session_key]}")

    except Exception as e:
        print(f"Error recording daily voucher: {e}")


def register_word_import_apis(app):
    """注册单词导入相关API"""
    from ..models import Word

    @app.route('/api/words/import/csv', methods=['POST'])
    @require_auth
    @api_response
    def api_words_import_csv():
        """CSV文件上传导入API (用户版本)"""
        try:
            if 'file' not in request.files:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_DATA_FORMAT,
                    "未找到上传文件"
                )

            file = request.files['file']
            if file.filename == '':
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_DATA_FORMAT,
                    "未选择文件"
                )

            if not file.filename.endswith('.csv'):
                return APIResponse.json_error(
                    APIErrorCodes.VALIDATION_ERROR,
                    "文件格式错误，请上传CSV文件"
                )

            # 读取CSV文件
            import csv
            import io

            content = file.read().decode('utf-8')
            csv_reader = csv.DictReader(io.StringIO(content))

            imported_count = 0
            errors = []

            for row_num, row in enumerate(csv_reader, 1):
                try:
                    # 验证必需字段
                    if not row.get('english_word') or not row.get('chinese_meaning'):
                        errors.append(f"第{row_num}行: 缺少必需字段")
                        continue

                    # 创建单词
                    word_data = {
                        'english_word': row['english_word'].strip(),
                        'chinese_meaning': row['chinese_meaning'].strip(),
                        'section': row.get('section', '用户导入').strip(),
                        'audio_path': row.get('audio_path', '').strip()
                    }

                    word_id = Word.create(word_data)
                    if word_id:
                        imported_count += 1
                    else:
                        errors.append(f"第{row_num}行: 单词创建失败")

                except Exception as e:
                    errors.append(f"第{row_num}行: {str(e)}")

            return APIResponse.json_success(
                "CSV导入完成",
                {
                    "imported_count": imported_count,
                    "error_count": len(errors),
                    "errors": errors[:10]  # 只返回前10个错误
                }
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_ERROR,
                f"CSV导入失败: {str(e)}"
            )

    @app.route('/api/words/add', methods=['POST'])
    @require_auth
    @api_response
    def api_words_add():
        """单个单词手动添加API"""
        try:
            data = request.get_json()
            if not data:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_DATA_FORMAT,
                    "请求数据格式错误"
                )

            # 验证必需字段
            english_word = data.get('english_word', '').strip()
            chinese_meaning = data.get('chinese_meaning', '').strip()

            if not english_word or not chinese_meaning:
                return APIResponse.json_error(
                    APIErrorCodes.VALIDATION_ERROR,
                    "英文单词和中文含义不能为空"
                )

            # 检查单词是否已存在
            existing_word = Word.search(english_word)
            if existing_word:
                for word in existing_word:
                    if word['english_word'].lower() == english_word.lower():
                        return APIResponse.json_error(
                            APIErrorCodes.VALIDATION_ERROR,
                            f"单词 '{english_word}' 已存在"
                        )

            # 创建单词
            word_data = {
                'english_word': english_word,
                'chinese_meaning': chinese_meaning,
                'section': data.get('section', '用户添加'),
                'audio_path': data.get('audio_path', '')
            }

            word_id = Word.create(word_data)
            if word_id:
                return APIResponse.json_success(
                    "单词添加成功",
                    {
                        "word_id": word_id,
                        "english_word": english_word,
                        "chinese_meaning": chinese_meaning
                    }
                )
            else:
                return APIResponse.json_error(
                    APIErrorCodes.INTERNAL_ERROR,
                    "单词创建失败"
                )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_ERROR,
                f"单词添加失败: {str(e)}"
            )

    @app.route('/api/words/import/json', methods=['POST'])
    @require_auth
    @api_response
    def api_words_import_json():
        """JSON格式批量导入API"""
        try:
            data = request.get_json()
            if not data or 'words' not in data:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_DATA_FORMAT,
                    "请求数据格式错误，需要words数组"
                )

            words_list = data['words']
            if not isinstance(words_list, list):
                return APIResponse.json_error(
                    APIErrorCodes.VALIDATION_ERROR,
                    "words必须是数组格式"
                )

            imported_count = 0
            errors = []

            for index, word_data in enumerate(words_list):
                try:
                    # 验证必需字段
                    if not word_data.get('english_word') or not word_data.get('chinese_meaning'):
                        errors.append(f"第{index+1}个单词: 缺少必需字段")
                        continue

                    # 创建单词
                    word_info = {
                        'english_word': word_data['english_word'].strip(),
                        'chinese_meaning': word_data['chinese_meaning'].strip(),
                        'section': word_data.get('section', '用户导入').strip(),
                        'audio_path': word_data.get('audio_path', '').strip()
                    }

                    word_id = Word.create(word_info)
                    if word_id:
                        imported_count += 1
                    else:
                        errors.append(f"第{index+1}个单词: 创建失败")

                except Exception as e:
                    errors.append(f"第{index+1}个单词: {str(e)}")

            return APIResponse.json_success(
                "JSON导入完成",
                {
                    "imported_count": imported_count,
                    "error_count": len(errors),
                    "errors": errors[:10]
                }
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_ERROR,
                f"JSON导入失败: {str(e)}"
            )

    @app.route('/api/words/history')
    @require_auth
    @api_response
    def api_words_history():
        """用户导入历史查询API"""
        try:
            user_id = session['user_id']

            # 查询用户导入的单词（通过section字段识别）
            from ...models.word import Word

            # 获取用户相关的单词
            user_sections = ['用户导入', '用户添加']
            user_words = []

            for section in user_sections:
                words = Word.get_by_section(section) if hasattr(Word, 'get_by_section') else []
                user_words.extend(words)

            # 按创建时间排序（如果有的话）
            user_words.sort(key=lambda x: x.get('id', 0), reverse=True)

            return APIResponse.json_success(
                "获取导入历史成功",
                {
                    "total_count": len(user_words),
                    "words": user_words[:50],  # 只返回最近50个
                    "sections": user_sections
                }
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_ERROR,
                f"获取导入历史失败: {str(e)}"
            )

    @app.route('/api/add_to_vocabulary', methods=['POST'])
    @require_auth
    def api_add_to_vocabulary():
        """添加单词到词汇表API - 前端learning_logic.js调用"""
        try:
            data = request.get_json() or {}
            user_id = session['user_id']

            # 获取单词信息
            english_word = data.get('english_word', '').strip()
            chinese_meaning = data.get('chinese_meaning', '').strip()

            if not english_word:
                return jsonify({
                    "success": False,
                    "error": "缺少英文单词"
                }), 400

            # 检查单词是否已存在
            from ..models import Word
            existing_word = Word.get_by_english_word(english_word)

            if existing_word:
                return jsonify({
                    "success": True,
                    "message": "单词已存在于词汇表中",
                    "word_id": existing_word['id']
                })

            # 添加新单词
            word_id = Word.create(
                english_word=english_word,
                chinese_meaning=chinese_meaning or "待补充",
                section="用户添加"
            )

            return jsonify({
                "success": True,
                "message": "单词已添加到词汇表",
                "word_id": word_id,
                "english_word": english_word,
                "chinese_meaning": chinese_meaning
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"添加单词失败: {str(e)}"
            }), 500

    @app.route('/api/daily_learning_plan', methods=['GET', 'POST'])
    @require_auth
    @api_response
    def api_daily_learning_plan():
        """获取每日学习计划API - 前端learning_logic.js调用"""
        try:
            user_id = session['user_id']

            # 支持GET和POST请求
            if request.method == 'POST':
                data = request.get_json() or {}
                date_param = data.get('date')
                filters = data.get('filters', {})
            else:
                date_param = request.args.get('date')
                filters = {}

            # 解析日期参数
            if date_param:
                try:
                    from datetime import datetime
                    target_date = datetime.strptime(date_param, '%Y-%m-%d').date()
                except ValueError:
                    target_date = date.today()
            else:
                target_date = date.today()

            # 获取学习计划 - 增强错误处理和日志
            from ..models.planning import LearningPlan
            from ..services.learning.plan_service import LearningPlanService

            # 首先确保有学习计划
            try:
                plan_generated = LearningPlanService.generate_daily_plan(user_id, target_date)
                print(f"📅 API请求学习计划: user_id={user_id}, date={target_date}, generated={plan_generated}")
            except Exception as e:
                print(f"❌ 学习计划生成失败: user_id={user_id}, date={target_date}, error={e}")

            plan = LearningPlan.get_daily_plan(user_id, target_date)
            print(f"📊 获取到学习计划: user_id={user_id}, date={target_date}, count={len(plan) if plan else 0}")

            if not plan:
                return {
                    "success": True,
                    "daily_words": [],
                    "total_count": 0,
                    "date": str(target_date),
                    "message": f"日期 {target_date} 没有学习计划"
                }

            # 格式化学习计划数据 - 匹配前端期望的格式
            daily_words = []
            for word in plan:
                word_dict = dict(word)
                daily_words.append({
                    "id": word_dict.get('word_id'),  # 使用word_id而不是id
                    "word": word_dict.get('chinese_meaning'),  # 前端期望的字段名
                    "answer": word_dict.get('english_word'),   # 前端期望的字段名
                    "section": word_dict.get('section', ''),
                    "star_level": word_dict.get('star_level', 3),
                    "item_type": word_dict.get('item_type', 'review'),
                    "learning_requirement": word_dict.get('learning_requirement', 'spelling'),  # 新增字段
                    "long_term_proficiency": word_dict.get('proficiency', 0) * 100,  # 转换为百分比
                    "is_new": word_dict.get('item_type') == 'new',
                    "accuracy_rate": 0,  # 默认值，可以后续从统计中获取
                    "learn_count": 0,    # 默认值，可以后续从统计中获取
                    "last_learned": '未学习'  # 默认值
                })

            # 计算星级分布
            star_distribution = {}
            for word in daily_words:
                star_level = word['star_level']
                star_distribution[star_level] = star_distribution.get(star_level, 0) + 1

            return {
                "success": True,
                "words": daily_words,  # 🔧 修复：前端期望words字段，不是daily_words
                "daily_words": daily_words,  # 保留兼容性
                "total_count": len(daily_words),
                "date": str(target_date),
                "star_distribution": star_distribution
            }

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"获取每日学习计划失败: {str(e)}"
            )

    def _calculate_star_distribution(self, words):
        """计算星级分布"""
        distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
        for word in words:
            star_level = word.get('star_level', 3)
            if star_level in distribution:
                distribution[star_level] += 1
        return distribution

    @app.route('/api/words/template')
    def api_words_template():
        """CSV模板文件下载API"""
        try:
            from flask import make_response

            # 创建CSV模板内容
            template_content = """english_word,chinese_meaning,section,audio_path
apple,苹果,水果类,/static/audio/apple.mp3
book,书,学习用品,/static/audio/book.mp3
cat,猫,动物类,/static/audio/cat.mp3"""

            # 创建响应
            response = make_response(template_content)
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = 'attachment; filename=word_import_template.csv'

            return response

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_ERROR,
                f"模板下载失败: {str(e)}"
            )

    # ================== Recognition模式相关API ==================

    @app.route('/api/recognition/question', methods=['POST'])
    def get_recognition_question():
        """获取Recognition模式的选择题"""
        try:
            data = request.get_json()
            word_id = data.get('word_id')
            question_type = data.get('question_type', 'cn_to_en')  # cn_to_en 或 en_to_cn
            
            if not word_id:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_PARAMS,
                    "缺少word_id参数"
                )
            
            # 导入Recognition服务
            from ..services.recognition_service import RecognitionService
            
            # 生成选择题
            question_data = RecognitionService.generate_question(word_id, question_type)
            
            if question_data:
                return APIResponse.json_success(question_data)
            else:
                return APIResponse.json_error(
                    APIErrorCodes.WORD_NOT_FOUND,
                    "无法生成选择题"
                )
                
        except Exception as e:
            logger.error(f"❌ 获取Recognition选择题异常: {e}")
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"获取选择题失败: {str(e)}"
            )

    @app.route('/api/recognition/submit', methods=['POST'])
    def submit_recognition_answer():
        """提交Recognition模式的答案（支持多选）"""
        try:
            data = request.get_json()
            word_id = data.get('word_id')
            
            # 🚀 支持多选：新参数格式
            selected_options = data.get('selected_options', [])
            correct_answers = data.get('correct_answers', [])
            is_multiple_choice = data.get('is_multiple_choice', False)
            
            # 🔧 兼容旧格式
            if not selected_options and data.get('selected_option'):
                selected_options = [data.get('selected_option')]
            if not correct_answers and data.get('correct_answer'):
                correct_answers = [data.get('correct_answer')]
            
            duration_seconds = data.get('duration_seconds', 0)
            question_type = data.get('question_type', 'cn_to_en')
            
            # 🚀 更新参数验证
            if not all([word_id, selected_options, correct_answers]):
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_PARAMS,
                    "缺少必要参数：word_id, selected_options, correct_answers"
                )
            
            # 获取当前用户ID
            if 'user_id' not in session:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_USER_ID,
                    "需要登录"
                )
            user_id = session['user_id']
            
            logger.info(f"🎯 Recognition答案提交: user_id={user_id}, word_id={word_id}, "
                       f"selected={selected_options}, correct={correct_answers}, "
                       f"multiple={is_multiple_choice}")
            
            # 导入Recognition服务
            from ..services.recognition_service import RecognitionService
            
            # 🚀 使用新的多选接口
            result = RecognitionService.submit_answer(
                user_id=user_id,
                word_id=word_id,
                selected_options=selected_options,
                correct_answers=correct_answers,
                duration_seconds=duration_seconds,
                question_type=question_type,
                is_multiple_choice=is_multiple_choice
            )
            
            if result and result.get('success'):
                return APIResponse.json_success(result.get('data', result))
            else:
                # 🔧 确保错误情况下也返回包含is_correct的标准格式
                error_data = {
                    'is_correct': False,
                    'new_star_level': None,
                    'points_change': 0,
                    'question_type': question_type,
                    'is_multiple_choice': is_multiple_choice,
                    'selected_options': selected_options,
                    'correct_answers': correct_answers,
                    'error': True,
                    'error_message': result.get('message', '答案提交失败')
                }
                return APIResponse.json_success(error_data), 400
                
        except Exception as e:
            logger.error(f"❌ 提交Recognition答案异常: user_id={user_id if 'user_id' in locals() else 'unknown'}, error={e}")
            # 🔧 异常情况下也返回包含is_correct的标准格式
            error_data = {
                'is_correct': False,
                'new_star_level': None,
                'points_change': 0,
                'question_type': question_type if 'question_type' in locals() else 'cn_to_en',
                'is_multiple_choice': is_multiple_choice if 'is_multiple_choice' in locals() else False,
                'selected_options': selected_options if 'selected_options' in locals() else [],
                'correct_answers': correct_answers if 'correct_answers' in locals() else [],
                'error': True,
                'error_message': f"系统异常: {str(e)}"
            }
            return APIResponse.json_success(error_data), 500

    # ===== 推荐系统配置API =====

    @app.route('/api/recommendation/config', methods=['GET'])
    @require_auth
    @api_response
    def api_get_recommendation_config():
        """获取用户推荐配置API"""
        try:
            user_id = session['user_id']
            config = recommendation_config_manager.get_user_config(user_id)
            
            return APIResponse.json_success(
                data={
                    'config': {
                        'enable_semantic_recommendations': config.enable_semantic_recommendations,
                        'enable_morphology_recommendations': config.enable_morphology_recommendations,
                        'enable_phonetic_recommendations': config.enable_phonetic_recommendations,
                        'enable_grammar_recommendations': config.enable_grammar_recommendations,
                        'testing_mode_disable_semantic': config.testing_mode_disable_semantic,
                        'testing_mode_max_recommendations': config.testing_mode_max_recommendations,
                        'learning_mode_max_recommendations': config.learning_mode_max_recommendations,
                        'semantic_weight_factor': config.semantic_weight_factor,
                        'morphology_weight_factor': config.morphology_weight_factor,
                        'phonetic_weight_factor': config.phonetic_weight_factor
                    }
                },
                message="推荐配置获取成功"
            )
        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"获取推荐配置失败: {str(e)}"
            )

    @app.route('/api/recommendation/config', methods=['POST'])
    @require_auth
    @api_response
    def api_set_recommendation_config():
        """设置用户推荐配置API"""
        try:
            user_id = session['user_id']
            data = request.get_json()
            
            if not data:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_DATA_FORMAT,
                    "请求数据不能为空"
                )
            
            # 验证配置数据
            config_data = data.get('config', {})
            
            # 创建新配置
            from ..services.pattern.recommendation_config import RecommendationConfig
            
            try:
                new_config = RecommendationConfig(
                    enable_semantic_recommendations=config_data.get('enable_semantic_recommendations', True),
                    enable_morphology_recommendations=config_data.get('enable_morphology_recommendations', True),
                    enable_phonetic_recommendations=config_data.get('enable_phonetic_recommendations', True),
                    enable_grammar_recommendations=config_data.get('enable_grammar_recommendations', True),
                    testing_mode_disable_semantic=config_data.get('testing_mode_disable_semantic', True),
                    testing_mode_max_recommendations=config_data.get('testing_mode_max_recommendations', 2),
                    learning_mode_max_recommendations=config_data.get('learning_mode_max_recommendations', 4),
                    semantic_weight_factor=config_data.get('semantic_weight_factor', 0.5),
                    morphology_weight_factor=config_data.get('morphology_weight_factor', 1.2),
                    phonetic_weight_factor=config_data.get('phonetic_weight_factor', 1.1)
                )
                
                recommendation_config_manager.set_user_config(user_id, new_config)
                
                return APIResponse.json_success(
                    data={'message': '推荐配置已更新'},
                    message="推荐配置更新成功"
                )
                
            except (ValueError, TypeError) as e:
                return APIResponse.json_error(
                    APIErrorCodes.VALIDATION_ERROR,
                    f"配置数据格式错误: {str(e)}"
                )
            
        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"设置推荐配置失败: {str(e)}"
            )

    @app.route('/api/recommendation/preference', methods=['POST'])
    @require_auth
    @api_response
    def api_set_recommendation_preference():
        """设置用户推荐偏好API（简化版）"""
        try:
            user_id = session['user_id']
            data = request.get_json()
            
            if not data:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_DATA_FORMAT,
                    "请求数据不能为空"
                )
            
            preference = data.get('preference')
            valid_preferences = ['memory_focused', 'learning_focused', 'default']
            
            if preference not in valid_preferences:
                return APIResponse.json_error(
                    APIErrorCodes.VALIDATION_ERROR,
                    f"无效的偏好类型，支持的类型: {', '.join(valid_preferences)}"
                )
            
            recommendation_config_manager.set_user_preference(user_id, preference)
            
            # 获取偏好说明
            preference_descriptions = {
                'memory_focused': '记忆导向：禁用语义推荐，加强词根词缀和发音规律推荐，更专注于记忆技巧',
                'learning_focused': '学习导向：平衡各种推荐策略，适度减少语义推荐权重',
                'default': '默认配置：标准的推荐策略组合'
            }
            
            return APIResponse.json_success(
                data={
                    'preference': preference,
                    'description': preference_descriptions[preference]
                },
                message=f"推荐偏好已设置为: {preference}"
            )
            
        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"设置推荐偏好失败: {str(e)}"
            )

    @app.route('/api/recommendation/reset', methods=['POST'])
    @require_auth
    @api_response
    def api_reset_recommendation_config():
        """重置用户推荐配置API"""
        try:
            user_id = session['user_id']
            recommendation_config_manager.reset_user_config(user_id)
            
            return APIResponse.json_success(
                data={'message': '推荐配置已重置为默认值'},
                message="推荐配置重置成功"
            )
            
        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"重置推荐配置失败: {str(e)}"
            )
