"""
页面路由模块
处理所有页面渲染相关的路由
"""
from flask import render_template, session, redirect, url_for, send_from_directory
import os

from ..models import User
from ..services import LearningPlanService, StatisticsService


def register_page_routes(app):
    """注册页面路由"""
    
    @app.route('/execute_new_user_test')
    def execute_new_user_test():
        """新用户API验证测试页面"""
        return render_template('simple_dashboard.html')
    
    @app.route('/')
    def index():
        """首页"""
        if 'user_id' in session:
            # 检查session中的用户是否有效
            user = User.get_by_id(session['user_id'])
            if user:
                return redirect('/dashboard')
            else:
                # 用户不存在，清除session
                session.clear()
        return render_template('welcome.html')
    
    @app.route('/login')
    def login_page():
        """登录页面"""
        return render_template('login.html')
    
    @app.route('/register')
    def register_page():
        """注册页面"""
        return render_template('register.html')
    
    @app.route('/dashboard')
    def dashboard():
        """用户仪表板"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))

        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else {'id': 0, 'username': '', 'points': 0, 'vouchers': 0}

        # 获取学习统计
        stats = StatisticsService.get_user_statistics(user_id)

        # 展开统计数据，使模板可以直接访问变量
        return render_template('simple_dashboard.html', user=user_info, **stats)
    
    @app.route('/learning')
    def learning():
        """学习页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        # 获取用户信息传递给前端
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else {'id': 0, 'username': '', 'points': 0, 'vouchers': 0}
        
        return render_template('learning.html', user=user_info)
    
    @app.route('/learning_test')
    def learning_test():
        """学习测试页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        return render_template('learning.html')
    
    @app.route('/learning_simple')
    def learning_simple():
        """简化版学习页面 - 完全独立，无外部JS依赖"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        return render_template('learning.html')
    
    @app.route('/learning_fixed')
    def learning_fixed():
        """修复版学习页面 - 干净的代码，保持原有功能"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        return render_template('learning.html')
    
    @app.route('/learning_card')
    def learning_card():
        """卡片学习页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        return render_template('learning_card.html')
    
    @app.route('/vocabulary_book')
    def vocabulary_book():
        """生词本页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))

        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else None

        # 获取生词本数据
        words = StatisticsService.get_vocabulary_book(user_id)
        print(f"🔍 vocabulary_book: 用户信息 = {user_info}")
        print(f"🔍 vocabulary_book: 获取到 {len(words)} 个生词")

        # 🔧 修复：转换为模板期望的格式
        vocabulary_list = []
        for word in words:
            # 处理日期字段
            last_reviewed = None
            if word['last_learning_date'] and word['last_learning_date'] != '未学习':
                try:
                    from datetime import datetime
                    last_reviewed = datetime.strptime(word['last_learning_date'], '%Y-%m-%d')
                except:
                    last_reviewed = None

            vocabulary_list.append({
                'word': word['chinese_meaning'],
                'answer': word['english_word'],
                'proficiency': word['proficiency'],
                'recitation_count': word['learning_count'],
                'correct_count': word['correct_count'],
                'last_reviewed': last_reviewed,
                'accuracy': word['correct_count'] / word['learning_count'] * 100 if word['learning_count'] > 0 else 0,
                'type': 'manual'  # 暂时标记为手动添加
            })

        # 准备模板所需的统计数据
        context = {
            'vocabulary_list': vocabulary_list,
            'manual_count': len(vocabulary_list),
            'low_accuracy_count': 0,
            'total_count': len(vocabulary_list),
            'low_accuracy_threshold': 60,
            'user': user_info
        }

        return render_template('vocabulary_book.html', **context)
    
    @app.route('/practice_vocabulary')
    def practice_vocabulary():
        """生词练习页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else None
        
        # 重定向到学习页面，添加生词练习模式参数
        return redirect('/learning?mode=vocabulary')
    
    @app.route('/statistics')
    def statistics():
        """统计页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else None
        
        return render_template('simple_dashboard.html', user=user_info)
    
    @app.route('/learning_plan')
    def learning_plan():
        """学习计划页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else None
        
        return render_template('daily_words.html', user=user_info)
    
    @app.route('/profile')
    def profile():
        """用户档案页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else None
        
        return render_template('simple_dashboard.html', user=user_info)
    
    @app.route('/settings')
    def settings():
        """设置页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else None
        
        return render_template('simple_dashboard.html', user=user_info)
    
    @app.route('/help')
    def help_page():
        """帮助页面"""
        return render_template('welcome.html')
    
    @app.route('/about')
    def about():
        """关于页面"""
        return render_template('welcome.html')
    
    @app.route('/api/words/import')
    def word_import_page():
        """单词导入页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else None
        
        return render_template('word_import.html', user=user_info)
    
    @app.route('/logout')
    def logout():
        """GET方式的登出路由"""
        # 在登出前完成学习会话
        if 'user_id' in session:
            from ..services import LearningService
            LearningService.finish_learning_session(session['user_id'])
        
        session.clear()
        return redirect(url_for('index'))
    
    @app.route('/admin/database')
    def admin_database():
        """数据库管理页面（仅管理员）"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        
        # 检查是否为管理员用户
        user = User.get_by_id(session['user_id'])
        if not user or user['username'] != 'admin':
            return "访问被拒绝：仅管理员可访问", 403
        
        # 获取用户信息
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        }
        
        return render_template('admin_database.html', user=user_info)
    
    @app.route('/audio/<path:filename>')
    def serve_audio(filename):
        """音频文件服务路由 - 重定向到正确的静态路径"""
        return redirect(url_for('static', filename=f'audio/words/{filename}'))

    @app.route('/favicon.ico')
    def favicon():
        """网站图标路由 - 避免404错误"""
        from flask import Response
        return Response(status=204)  # 返回空响应，避免404错误

    @app.route('/daily_words')
    def daily_words():
        """每日单词详细计划页面"""
        if 'user_id' not in session:
            return redirect(url_for('login_page'))

        # 获取用户信息
        user_id = session['user_id']
        user = User.get_by_id(user_id)
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        } if user else None

        return render_template('daily_words.html', user=user_info)



    @app.route('/low_correct_rate_words')
    def low_correct_rate_words():
        """低正确率单词页面（兼容前端AJAX调用）"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']
        threshold = request.args.get('threshold', 0.6, type=float)
        limit = request.args.get('limit', 20, type=int)

        try:
            from ..models import WordRecord

            # 获取低正确率单词
            low_accuracy_words = WordRecord.get_difficult_words(user_id, threshold, limit)

            # 如果是AJAX请求，返回JSON
            if request.headers.get('Content-Type') == 'application/json' or request.args.get('format') == 'json':
                return jsonify({
                    'success': True,
                    'low_correct_rate_words': low_accuracy_words,
                    'threshold': threshold,
                    'count': len(low_accuracy_words)
                })

            # 否则渲染页面
            return render_template('low_correct_rate_words.html',
                                 words=low_accuracy_words,
                                 threshold=threshold)

        except Exception as e:
            if request.headers.get('Content-Type') == 'application/json' or request.args.get('format') == 'json':
                return jsonify({
                    'success': False,
                    'message': f'获取低正确率单词失败: {str(e)}'
                })
            else:
                return render_template('low_correct_rate_words.html',
                                     error=f'获取数据失败: {str(e)}')

    # 错误处理页面
    @app.errorhandler(404)
    def not_found(error):
        return render_template('error.html', 
                             error_code=404, 
                             error_message='页面未找到'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('error.html',
                             error_code=500,
                             error_message='服务器内部错误'), 500

    # API测试页面
    @app.route('/api_test')
    def api_test():
        """API测试页面"""
        return render_template('simple_dashboard.html')
    
    @app.route('/api_integration_test')
    def api_integration_test():
        """API集成测试页面"""
        return render_template('simple_dashboard.html')
