"""
单词模型模块
提供单词管理、用户-单词关系管理、搜索、分组等功能
"""
import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any

from .base import BaseModel, db
from ..core.exceptions import ValidationError, ResourceNotFoundError, DuplicateResourceError
from ..core.decorators import validate_args, validate_string_length, timing, cache


class Word(BaseModel):
    """单词模型"""
    
    table_name = 'word'
    required_fields = ['english_word', 'chinese_meaning']
    
    @staticmethod
    @timing()
    @cache(ttl=300)  # 缓存5分钟
    def get_all() -> List[sqlite3.Row]:
        """
        获取所有单词
        
        Returns:
            List[sqlite3.Row]: 所有单词列表
        """
        query = "SELECT * FROM word ORDER BY id"
        return db.execute_query(query)
    
    @staticmethod
    @timing()
    @cache(ttl=600)  # 缓存10分钟
    def get_by_id(word_id: int) -> Optional[sqlite3.Row]:
        """
        通过ID获取单词
        
        Args:
            word_id: 单词ID
            
        Returns:
            Optional[sqlite3.Row]: 单词记录，不存在则返回None
        """
        query = "SELECT * FROM word WHERE id = ?"
        results = db.execute_query(query, (word_id,))
        return results[0] if results else None
    
    @staticmethod
    @timing()
    @cache(ttl=300)
    def get_by_section(section: str) -> List[sqlite3.Row]:
        """
        按章节获取单词
        
        Args:
            section: 章节名称
            
        Returns:
            List[sqlite3.Row]: 该章节的单词列表
        """
        query = "SELECT * FROM word WHERE section = ? ORDER BY id"
        return db.execute_query(query, (section,))
    
    @staticmethod
    @timing()
    def search(keyword: str, limit: int = 100) -> List[sqlite3.Row]:
        """
        搜索单词
        
        Args:
            keyword: 搜索关键词
            limit: 结果数量限制
            
        Returns:
            List[sqlite3.Row]: 匹配的单词列表
        """
        query = """
        SELECT * FROM word 
        WHERE english_word LIKE ? OR chinese_meaning LIKE ? 
        ORDER BY 
            CASE 
                WHEN english_word = ? THEN 1
                WHEN english_word LIKE ? THEN 2
                WHEN chinese_meaning LIKE ? THEN 3
                ELSE 4
            END,
            id
        LIMIT ?
        """
        pattern = f"%{keyword}%"
        exact_pattern = keyword
        start_pattern = f"{keyword}%"
        
        return db.execute_query(query, (
            pattern, pattern, exact_pattern, start_pattern, start_pattern, limit
        ))
    
    @staticmethod
    @timing()
    @cache(ttl=3600)  # 缓存1小时
    def get_total_count() -> int:
        """
        获取数据库中单词总数
        
        Returns:
            int: 单词总数
        """
        query = "SELECT COUNT(*) as count FROM word"
        results = db.execute_query(query)
        return results[0]['count'] if results else 0
    
    @staticmethod
    @timing()
    @validate_args(
        english_word=validate_string_length(1, 100),
        chinese_meaning=validate_string_length(1, 500),
        section=validate_string_length(1, 50)
    )
    def create(english_word: str, chinese_meaning: str, section: str = '新增') -> int:
        """
        创建新单词
        
        Args:
            english_word: 英文单词
            chinese_meaning: 中文释义
            section: 章节分类
            
        Returns:
            int: 新单词的ID
            
        Raises:
            ValidationError: 参数验证失败
            DuplicateResourceError: 单词已存在
        """
        # 检查单词是否已存在
        existing = Word.get_by_english_word(english_word)
        if existing:
            raise DuplicateResourceError("单词", field="english_word", value=english_word)
        
        query = """
        INSERT INTO word (english_word, chinese_meaning, section)
        VALUES (?, ?, ?)
        """
        return db.execute_insert(query, (english_word, chinese_meaning, section))
    
    @staticmethod
    @timing()
    def get_by_english_word(english_word: str) -> Optional[sqlite3.Row]:
        """
        通过英文单词获取记录
        
        Args:
            english_word: 英文单词
            
        Returns:
            Optional[sqlite3.Row]: 单词记录，不存在则返回None
        """
        query = "SELECT * FROM word WHERE english_word = ?"
        results = db.execute_query(query, (english_word,))
        return results[0] if results else None
    
    @staticmethod
    @timing()
    @cache(ttl=1800)  # 缓存30分钟
    def get_sections() -> List[str]:
        """
        获取所有章节列表
        
        Returns:
            List[str]: 章节名称列表
        """
        query = "SELECT DISTINCT section FROM word ORDER BY section"
        results = db.execute_query(query)
        return [row['section'] for row in results]
    
    @staticmethod
    @timing()
    def get_section_stats() -> List[Dict[str, Any]]:
        """
        获取各章节统计信息
        
        Returns:
            List[Dict[str, Any]]: 章节统计列表
        """
        query = """
        SELECT section, COUNT(*) as word_count
        FROM word
        GROUP BY section
        ORDER BY word_count DESC, section
        """
        results = db.execute_query(query)
        return [{'section': row['section'], 'word_count': row['word_count']} for row in results]
    
    @staticmethod
    def update_word(word_id: int, **kwargs) -> bool:
        """
        更新单词信息
        
        Args:
            word_id: 单词ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        if not kwargs:
            return True
        
        # 过滤允许更新的字段
        allowed_fields = ['english_word', 'chinese_meaning', 'section']
        update_data = {k: v for k, v in kwargs.items() if k in allowed_fields}
        
        if not update_data:
            return True
        
        # 构建更新查询
        set_clause = ', '.join([f"{field} = ?" for field in update_data.keys()])
        query = f"UPDATE word SET {set_clause} WHERE id = ?"
        
        params = list(update_data.values()) + [word_id]
        affected_rows = db.execute_update(query, tuple(params))
        return affected_rows > 0
    
    @staticmethod
    def delete_word(word_id: int) -> bool:
        """
        删除单词（谨慎使用，会影响相关学习记录）
        
        Args:
            word_id: 单词ID
            
        Returns:
            bool: 删除是否成功
        """
        # 检查是否有相关的学习记录
        check_query = """
        SELECT COUNT(*) as count FROM user_word WHERE word_id = ?
        UNION ALL
        SELECT COUNT(*) as count FROM word_record WHERE word_id = ?
        UNION ALL
        SELECT COUNT(*) as count FROM learning_plan WHERE word_id = ?
        """
        results = db.execute_query(check_query, (word_id, word_id, word_id))
        total_related = sum(row['count'] for row in results)
        
        if total_related > 0:
            raise ValidationError(
                f"无法删除单词，存在{total_related}条相关记录",
                details={'related_records': total_related}
            )
        
        query = "DELETE FROM word WHERE id = ?"
        affected_rows = db.execute_update(query, (word_id,))
        return affected_rows > 0


class UserWord(BaseModel):
    """用户单词关系模型"""
    
    table_name = 'user_word'
    required_fields = ['user_id', 'word_id']
    
    @staticmethod
    @timing()
    def get_or_create(user_id: int, word_id: int) -> sqlite3.Row:
        """
        获取或创建用户单词关系
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            
        Returns:
            sqlite3.Row: 用户单词关系记录
            
        Raises:
            ValidationError: 用户或单词不存在
        """
        # 验证用户和单词是否存在
        from .user import User  # 延迟导入避免循环依赖
        
        if not User.get_by_id(user_id):
            raise ValidationError("用户不存在", field="user_id", value=user_id)
        
        if not Word.get_by_id(word_id):
            raise ValidationError("单词不存在", field="word_id", value=word_id)
        
        # 先尝试获取
        query = "SELECT * FROM user_word WHERE user_id = ? AND word_id = ?"
        results = db.execute_query(query, (user_id, word_id))
        
        if results:
            return results[0]
        
        # 如果不存在，创建新记录
        insert_query = """
        INSERT INTO user_word (user_id, word_id, learning_count, correct_count,
                              status, proficiency)
        VALUES (?, ?, 0, 0, 'new', 0.0)
        """
        new_id = db.execute_insert(insert_query, (user_id, word_id))
        
        # 返回新创建的记录
        return UserWord.get_by_id(new_id)
    
    @staticmethod
    @timing()
    def get_by_id(record_id: int) -> Optional[sqlite3.Row]:
        """
        通过ID获取记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            Optional[sqlite3.Row]: 用户单词关系记录
        """
        query = "SELECT * FROM user_word WHERE id = ?"
        results = db.execute_query(query, (record_id,))
        return results[0] if results else None
    
    @staticmethod
    @timing()
    def get_by_user_and_word(user_id: int, word_id: int) -> Optional[sqlite3.Row]:
        """
        获取用户单词关系
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            
        Returns:
            Optional[sqlite3.Row]: 用户单词关系记录
        """
        query = "SELECT * FROM user_word WHERE user_id = ? AND word_id = ?"
        results = db.execute_query(query, (user_id, word_id))
        return results[0] if results else None

    @staticmethod
    @timing()
    def update_learning_stats(user_id: int, word_id: int,
                            learning_count: int, correct_count: int,
                            proficiency: float) -> bool:
        """
        更新学习统计数据

        Args:
            user_id: 用户ID
            word_id: 单词ID
            learning_count: 学习次数
            correct_count: 正确次数
            proficiency: 熟练度 (0.0-100.0)

        Returns:
            bool: 更新是否成功
        """
        # 验证熟练度范围 (0-100)
        if not 0.0 <= proficiency <= 100.0:
            raise ValidationError("熟练度必须在0.0-100.0之间", field="proficiency", value=proficiency)

        query = """
        UPDATE user_word
        SET learning_count = ?, correct_count = ?, proficiency = ?,
            last_learning_date = ?
        WHERE user_id = ? AND word_id = ?
        """
        now = datetime.now()
        affected_rows = db.execute_update(query, (
            learning_count, correct_count, proficiency, now.date(), user_id, word_id
        ))
        return affected_rows > 0

    @staticmethod
    @timing()
    def update_status(user_id: int, word_id: int, status: str) -> bool:
        """
        更新单词状态

        Args:
            user_id: 用户ID
            word_id: 单词ID
            status: 状态 ('new', 'review', 'attention', 'mastered')

        Returns:
            bool: 更新是否成功
        """
        valid_statuses = ['new', 'review', 'attention', 'mastered']
        if status not in valid_statuses:
            raise ValidationError(
                f"无效的状态值: {status}",
                field="status",
                details={'valid_statuses': valid_statuses}
            )

        query = "UPDATE user_word SET status = ? WHERE user_id = ? AND word_id = ?"
        affected_rows = db.execute_update(query, (status, user_id, word_id))
        return affected_rows > 0

    @staticmethod
    @timing()
    # 移除缓存：新词选择需要考虑实时的learning_plan状态，不适合缓存
    def get_new_words(user_id: int, limit: int = 7) -> List[sqlite3.Row]:
        """
        获取新单词用于学习计划
        基于data_model.md更新规则：排除已在学习计划中达到star_level>=2的单词

        Args:
            user_id: 用户ID
            limit: 数量限制

        Returns:
            List[sqlite3.Row]: 新单词列表
        """
        query = """
        SELECT w.* FROM word w
        LEFT JOIN user_word uw ON w.id = uw.word_id AND uw.user_id = ?
        WHERE (uw.id IS NULL OR uw.status = 'new')
          AND NOT EXISTS (
            SELECT 1 FROM learning_plan lp 
            WHERE lp.user_id = ? 
              AND lp.word_id = w.id 
              AND lp.star_level >= 2
          )
        ORDER BY w.id
        LIMIT ?
        """
        return db.execute_query(query, (user_id, user_id, limit))

    @staticmethod
    @timing()
    # 移除缓存：复习词选择需要考虑实时的proficiency和status，不适合缓存
    def get_review_words(user_id: int, limit: int = 18) -> List[sqlite3.Row]:
        """
        获取复习单词，按熟练度倒序

        Args:
            user_id: 用户ID
            limit: 数量限制

        Returns:
            List[sqlite3.Row]: 复习单词列表
        """
        query = """
        SELECT w.*, uw.proficiency FROM word w
        JOIN user_word uw ON w.id = uw.word_id
        WHERE uw.user_id = ? AND uw.status IN ('review', 'attention')
        ORDER BY uw.proficiency ASC, w.id
        LIMIT ?
        """
        return db.execute_query(query, (user_id, limit))

    @staticmethod
    @timing()
    def get_attention_words(user_id: int) -> List[sqlite3.Row]:
        """
        获取生词本单词

        Args:
            user_id: 用户ID

        Returns:
            List[sqlite3.Row]: 生词本单词列表
        """
        query = """
        SELECT w.*, uw.* FROM word w
        JOIN user_word uw ON w.id = uw.word_id
        WHERE uw.user_id = ? AND uw.status = 'attention'
        ORDER BY uw.proficiency ASC, uw.last_learning_date DESC
        """
        return db.execute_query(query, (user_id,))

    @staticmethod
    @timing()
    def get_user_words_with_proficiency(user_id: int, status: str = None) -> List[sqlite3.Row]:
        """
        获取用户所有学习过的单词及其熟练度

        Args:
            user_id: 用户ID
            status: 可选的状态筛选

        Returns:
            List[sqlite3.Row]: 用户单词列表
        """
        base_query = """
        SELECT w.*, uw.proficiency, uw.learning_count, uw.correct_count,
               uw.status, uw.last_learning_date
        FROM word w
        JOIN user_word uw ON w.id = uw.word_id
        WHERE uw.user_id = ? AND uw.learning_count > 0
        """

        if status:
            base_query += " AND uw.status = ?"
            params = (user_id, status)
        else:
            params = (user_id,)

        base_query += " ORDER BY uw.proficiency DESC, uw.last_learning_date DESC"

        return db.execute_query(base_query, params)

    @staticmethod
    @timing()
    def get_user_word_stats(user_id: int) -> Dict[str, Any]:
        """
        获取用户单词学习统计

        Args:
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 统计信息
        """
        query = """
        SELECT
            COUNT(*) as total_words,
            SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_words,
            SUM(CASE WHEN status = 'review' THEN 1 ELSE 0 END) as review_words,
            SUM(CASE WHEN status = 'attention' THEN 1 ELSE 0 END) as attention_words,
            SUM(CASE WHEN status = 'mastered' THEN 1 ELSE 0 END) as mastered_words,
            AVG(proficiency) as avg_proficiency,
            SUM(learning_count) as total_learning_count,
            SUM(correct_count) as total_correct_count
        FROM user_word
        WHERE user_id = ?
        """
        results = db.execute_query(query, (user_id,))

        if results:
            row = results[0]
            return {
                'total_words': row['total_words'] or 0,
                'new_words': row['new_words'] or 0,
                'review_words': row['review_words'] or 0,
                'attention_words': row['attention_words'] or 0,
                'mastered_words': row['mastered_words'] or 0,
                'avg_proficiency': round(row['avg_proficiency'] or 0.0, 3),
                'total_learning_count': row['total_learning_count'] or 0,
                'total_correct_count': row['total_correct_count'] or 0,
                'accuracy_rate': round(
                    (row['total_correct_count'] or 0) / max(row['total_learning_count'] or 1, 1), 3
                )
            }

        return {
            'total_words': 0, 'new_words': 0, 'review_words': 0,
            'attention_words': 0, 'mastered_words': 0, 'avg_proficiency': 0.0,
            'total_learning_count': 0, 'total_correct_count': 0, 'accuracy_rate': 0.0
        }
