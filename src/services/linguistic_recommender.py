"""
语言学专家推荐服务
基于专家语言学标注提供准确的相似单词推荐，解决传统字符串匹配的误导问题
"""

import sqlite3
import json
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from src.core import get_logger
from .pattern.recommendation_config import recommendation_config_manager

logger = get_logger(__name__)


@dataclass
class LinguisticSimilarWord:
    """基于语言学的相似单词"""
    word_id: int
    english_word: str
    chinese_meaning: str
    similarity_reason: str
    similarity_type: str  # 'prefix', 'semantic', 'etymology', 'morphology'
    confidence: float
    proficiency: float
    learning_status: str


@dataclass
class LinguisticRecommendation:
    """语言学推荐结果"""
    category: str
    category_name: str
    explanation: str
    linguistic_principle: str
    similar_words: List[LinguisticSimilarWord]
    educational_value: str


class LinguisticRecommenderService:
    """基于语言学专家标注的推荐服务"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._max_recommendations_per_category = 5
        self._min_words_per_category = 2
    
    def get_linguistic_recommendations(self, user_id: int, word_id: int, 
                                     context: str = 'learning') -> List[LinguisticRecommendation]:
        """获取基于语言学专家标注的推荐
        
        Args:
            user_id: 用户ID
            word_id: 目标单词ID
            context: 使用场景 'learning'(学习) 或 'testing'(答题)
            
        Returns:
            按语言学价值排序的推荐列表
        """
        try:
            # 获取目标单词的语言学标注
            target_annotation = self._get_word_annotation(word_id)
            if not target_annotation:
                logger.info(f"单词{word_id}没有语言学标注")
                return []
            
            # 获取用户已学词汇
            user_words = self._get_user_learned_words(user_id)
            if not user_words:
                logger.info(f"用户{user_id}没有已学词汇")
                return []
            
            recommendations = []
            
            # 1. 真前缀推荐（最高优先级）
            if target_annotation.get('has_true_prefix'):
                prefix_rec = self._get_prefix_recommendations(
                    target_annotation, user_words, word_id
                )
                if prefix_rec:
                    recommendations.append(prefix_rec)
            
            # 2. 语义组推荐 (根据用户配置和使用场景决定是否启用)
            config = recommendation_config_manager.get_user_config(user_id)
            if config.should_enable_semantic(context):
                semantic_recs = self._get_semantic_recommendations(
                    target_annotation, user_words, word_id
                )
                recommendations.extend(semantic_recs)
            
            # 3. 学习组推荐 (答题模式下仅保留对比词汇)
            learning_recs = self._get_learning_cluster_recommendations(
                target_annotation, user_words, word_id, context
            )
            recommendations.extend(learning_recs)
            
            # 4. 反义词/对比推荐
            contrast_recs = self._get_contrast_recommendations(
                target_annotation, user_words, word_id
            )
            recommendations.extend(contrast_recs)
            
            # 按教育价值排序并限制数量
            recommendations = self._rank_by_educational_value(recommendations, user_id)
            max_recs = config.get_max_recommendations(context)
            return recommendations[:max_recs]
            
        except Exception as e:
            logger.error(f"获取语言学推荐失败: {e}")
            return []
    
    def _get_word_annotation(self, word_id: int) -> Optional[Dict]:
        """获取单词的语言学标注"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT wla.*, w.english_word, w.chinese_meaning
                FROM word_linguistic_annotations wla
                JOIN word w ON wla.word_id = w.id
                WHERE wla.word_id = ?
            """, (word_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                return None
            
            columns = [desc[0] for desc in cursor.description]
            annotation = dict(zip(columns, row))
            
            # 解析JSON字段
            for json_field in ['linguistic_tags', 'semantic_groups', 'learning_clusters']:
                if annotation.get(json_field):
                    try:
                        annotation[json_field] = json.loads(annotation[json_field])
                    except json.JSONDecodeError:
                        annotation[json_field] = []
            
            return annotation
            
        except Exception as e:
            logger.error(f"获取单词标注失败: {e}")
            return None
    
    def _get_user_learned_words(self, user_id: int) -> List[Dict]:
        """获取用户已学词汇及其标注"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning,
                       uw.status, uw.proficiency,
                       wla.linguistic_tags, wla.semantic_groups, 
                       wla.learning_clusters, wla.has_true_prefix, wla.prefix
                FROM user_word uw
                JOIN word w ON uw.word_id = w.id
                LEFT JOIN word_linguistic_annotations wla ON w.id = wla.word_id
                WHERE uw.user_id = ? AND uw.status != 'new'
            """, (user_id,))
            
            words = []
            for row in cursor.fetchall():
                word_info = {
                    'word_id': row[0],
                    'english_word': row[1],
                    'chinese_meaning': row[2],
                    'status': row[3],
                    'proficiency': row[4] or 0.0,
                    'has_annotation': row[5] is not None
                }
                
                # 解析JSON字段
                if row[5]:  # 有标注
                    try:
                        word_info['linguistic_tags'] = json.loads(row[5]) if row[5] else []
                        word_info['semantic_groups'] = json.loads(row[6]) if row[6] else []
                        word_info['learning_clusters'] = json.loads(row[7]) if row[7] else []
                        word_info['has_true_prefix'] = bool(row[8])
                        word_info['prefix'] = row[9]
                    except json.JSONDecodeError:
                        word_info['linguistic_tags'] = []
                        word_info['semantic_groups'] = []
                        word_info['learning_clusters'] = []
                
                words.append(word_info)
            
            conn.close()
            return words
            
        except Exception as e:
            logger.error(f"获取用户已学词汇失败: {e}")
            return []
    
    def _get_prefix_recommendations(self, target_annotation: Dict, 
                                  user_words: List[Dict], 
                                  exclude_word_id: int) -> Optional[LinguisticRecommendation]:
        """获取真前缀推荐"""
        target_prefix = target_annotation.get('prefix')
        if not target_prefix:
            return None
        
        prefix_words = []
        for word in user_words:
            if (word.get('has_annotation') and 
                word.get('has_true_prefix') and 
                word.get('prefix') == target_prefix and
                word['word_id'] != exclude_word_id):
                
                similar_word = LinguisticSimilarWord(
                    word_id=word['word_id'],
                    english_word=word['english_word'],
                    chinese_meaning=word['chinese_meaning'],
                    similarity_reason=f'都含有{target_prefix}前缀',
                    similarity_type='prefix',
                    confidence=0.95,
                    proficiency=word['proficiency'],
                    learning_status=word['status']
                )
                prefix_words.append(similar_word)
        
        if len(prefix_words) < self._min_words_per_category:
            return None
        
        # 前缀的教育价值说明
        prefix_explanations = {
            'un-': 'un-前缀表示否定，是英语中最常用的否定前缀。掌握这个规律后，遇到新的un-单词就能猜出意思！',
            're-': 're-前缀表示\"重新、再次\"，学会后能理解很多表示重复动作的词汇。',
            'pre-': 'pre-前缀表示\"预先、在...之前\"，多用于正式词汇中。',
            'dis-': 'dis-前缀表示\"不、分离\"，是构成反义词的重要方法。',
            'mis-': 'mis-前缀表示\"错误、不当\"，掌握后能精确理解词汇含义。',
            'over-': 'over-前缀表示\"超过、过度\"，常用于描述过量的概念。',
            'under-': 'under-前缀表示\"不足、在...下\"，与over-形成对比。',
            'uni-': 'uni-前缀表示\"一、统一\"，来自拉丁语，多见于学术词汇。'
        }
        
        explanation = prefix_explanations.get(
            target_prefix, 
            f'{target_prefix}前缀有固定的语法意义，掌握词缀规律是高效词汇学习的关键'
        )
        
        return LinguisticRecommendation(
            category='true_prefix',
            category_name=f'{target_prefix}前缀词汇',
            explanation=explanation,
            linguistic_principle=f'词缀学习法：{target_prefix}前缀的语法功能',
            similar_words=prefix_words[:self._max_recommendations_per_category],
            educational_value='高价值：掌握前缀规律能举一反三'
        )
    
    def _get_semantic_recommendations(self, target_annotation: Dict, 
                                    user_words: List[Dict], 
                                    exclude_word_id: int) -> List[LinguisticRecommendation]:
        """获取语义组推荐"""
        target_semantic_groups = target_annotation.get('semantic_groups', [])
        if not target_semantic_groups:
            return []
        
        recommendations = []
        
        for semantic_group in target_semantic_groups:
            group_words = []
            for word in user_words:
                if (word.get('has_annotation') and 
                    semantic_group in word.get('semantic_groups', []) and
                    word['word_id'] != exclude_word_id):
                    
                    similar_word = LinguisticSimilarWord(
                        word_id=word['word_id'],
                        english_word=word['english_word'],
                        chinese_meaning=word['chinese_meaning'],
                        similarity_reason=f'都属于{semantic_group}语义范畴',
                        similarity_type='semantic',
                        confidence=0.85,
                        proficiency=word['proficiency'],
                        learning_status=word['status']
                    )
                    group_words.append(similar_word)
            
            if len(group_words) >= self._min_words_per_category:
                # 语义组的中文名称和教育价值
                semantic_group_info = self._get_semantic_group_info(semantic_group)
                
                recommendation = LinguisticRecommendation(
                    category=f'semantic_{semantic_group}',
                    category_name=semantic_group_info['chinese_name'],
                    explanation=semantic_group_info['explanation'],
                    linguistic_principle='语义场学习法：相同概念领域的词汇',
                    similar_words=group_words[:self._max_recommendations_per_category],
                    educational_value=semantic_group_info['educational_value']
                )
                recommendations.append(recommendation)
        
        return recommendations
    
    def _get_learning_cluster_recommendations(self, target_annotation: Dict, 
                                            user_words: List[Dict], 
                                            exclude_word_id: int,
                                            context: str = 'learning') -> List[LinguisticRecommendation]:
        """获取学习组推荐"""
        target_clusters = target_annotation.get('learning_clusters', [])
        if not target_clusters:
            return []
        
        recommendations = []
        
        for cluster in target_clusters:
            # 答题模式下只保留对比词汇，过滤掉可能泄露答案的集群
            if context == 'testing' and cluster not in ['opposite_pairs', 'contrast_pairs']:
                continue
                
            cluster_words = []
            for word in user_words:
                if (word.get('has_annotation') and 
                    cluster in word.get('learning_clusters', []) and
                    word['word_id'] != exclude_word_id):
                    
                    similar_word = LinguisticSimilarWord(
                        word_id=word['word_id'],
                        english_word=word['english_word'],
                        chinese_meaning=word['chinese_meaning'],
                        similarity_reason=f'都属于{cluster}学习组',
                        similarity_type='learning_cluster',
                        confidence=0.80,
                        proficiency=word['proficiency'],
                        learning_status=word['status']
                    )
                    cluster_words.append(similar_word)
            
            if len(cluster_words) >= self._min_words_per_category:
                cluster_info = self._get_learning_cluster_info(cluster)
                
                recommendation = LinguisticRecommendation(
                    category=f'cluster_{cluster}',
                    category_name=cluster_info['chinese_name'],
                    explanation=cluster_info['explanation'],
                    linguistic_principle='主题学习法：功能相关的词汇组合',
                    similar_words=cluster_words[:self._max_recommendations_per_category],
                    educational_value=cluster_info['educational_value']
                )
                recommendations.append(recommendation)
        
        return recommendations
    
    def _get_contrast_recommendations(self, target_annotation: Dict, 
                                    user_words: List[Dict], 
                                    exclude_word_id: int) -> List[LinguisticRecommendation]:
        """获取对比词推荐（反义词等）"""
        target_tags = target_annotation.get('linguistic_tags', [])
        
        # 寻找反义词对
        contrast_pairs = {
            'happy': ['sad', 'unhappy'],
            'good': ['bad'],
            'big': ['small', 'little'],
            'hot': ['cold'],
            'fast': ['slow'],
            'new': ['old'],
            'young': ['old'],
            'long': ['short'],
            'black': ['white'],
            'run': ['walk']
        }
        
        target_word = target_annotation.get('english_word', '').lower()
        
        recommendations = []
        
        # 直接查找反义词
        if target_word in contrast_pairs:
            contrast_words = []
            for word in user_words:
                if (word['english_word'].lower() in contrast_pairs[target_word] and
                    word['word_id'] != exclude_word_id):
                    
                    similar_word = LinguisticSimilarWord(
                        word_id=word['word_id'],
                        english_word=word['english_word'],
                        chinese_meaning=word['chinese_meaning'],
                        similarity_reason=f'与{target_word}构成对比概念',
                        similarity_type='contrast',
                        confidence=0.90,
                        proficiency=word['proficiency'],
                        learning_status=word['status']
                    )
                    contrast_words.append(similar_word)
            
            if contrast_words:
                recommendation = LinguisticRecommendation(
                    category='contrast',
                    category_name='对比概念词汇',
                    explanation=f'对比学习法是最有效的记忆方法之一。通过对比{target_word}与其反义词，能加深对两个概念的理解。',
                    linguistic_principle='对比学习法：反义词对比记忆',
                    similar_words=contrast_words,
                    educational_value='高价值：对比记忆加深理解'
                )
                recommendations.append(recommendation)
        
        return recommendations
    
    def _get_semantic_group_info(self, semantic_group: str) -> Dict[str, str]:
        """获取语义组的中文名称和说明"""
        semantic_info = {
            'family': {
                'chinese_name': '家庭成员',
                'explanation': '家庭成员词汇贴近生活，通过家庭关系图谱记忆效果更佳。',
                'educational_value': '高价值：生活必需词汇'
            },
            'emotion': {
                'chinese_name': '情感表达',
                'explanation': '情感词汇学习建议：将词汇与个人经历结合，情感记忆最深刻。',
                'educational_value': '高价值：表达能力核心'
            },
            'color': {
                'chinese_name': '颜色词汇',
                'explanation': '颜色词汇具有强烈视觉联想，通过想象具体物品(如red apple)能加深记忆。',
                'educational_value': '中价值：基础描述词汇'
            },
            'basic_adjectives': {
                'chinese_name': '基础形容词',
                'explanation': '基础形容词是描述世界的核心词汇，掌握后能大幅提升表达能力。',
                'educational_value': '高价值：表达基础'
            },
            'movement': {
                'chinese_name': '运动动作',
                'explanation': '运动词汇可以通过做动作来记忆，身体记忆比纯视觉记忆更持久。',
                'educational_value': '中价值：日常动作'
            },
            'time': {
                'chinese_name': '时间概念',
                'explanation': '时间词汇是日常交流的核心，按时间顺序记忆效果更佳。',
                'educational_value': '高价值：日常必需'
            },
            'daily_activities': {
                'chinese_name': '日常活动',
                'explanation': '日常活动词汇实用性强，学习时可以联想自己的日常生活场景。',
                'educational_value': '高价值：生活实用'
            }
        }
        
        return semantic_info.get(semantic_group, {
            'chinese_name': semantic_group,
            'explanation': f'{semantic_group}相关词汇有共同的概念核心，主题学习法能建立强联想网络。',
            'educational_value': '中价值：主题相关'
        })
    
    def _get_learning_cluster_info(self, cluster: str) -> Dict[str, str]:
        """获取学习组的中文名称和说明"""
        cluster_info = {
            'family_members': {
                'chinese_name': '家庭称谓',
                'explanation': '家庭称谓是社交基础，按关系远近分层记忆。',
                'educational_value': '高价值：社交必需'
            },
            'emotion_adjectives': {
                'chinese_name': '情感形容词',
                'explanation': '情感形容词能精确表达内心状态，是高级表达的基础。',
                'educational_value': '高价值：表达精度'
            },
            'color_words': {
                'chinese_name': '颜色表达',
                'explanation': '颜色词汇通过视觉联想记忆，想象彩虹的颜色顺序。',
                'educational_value': '中价值：描述丰富性'
            },
            'opposite_pairs': {
                'chinese_name': '反义词对',
                'explanation': '反义词对比学习法：一次学会两个相反概念，记忆效果翻倍。',
                'educational_value': '高价值：对比记忆法'
            },
            'size_adjectives': {
                'chinese_name': '尺寸描述',
                'explanation': '尺寸形容词是物理描述的基础，可以用手势辅助记忆。',
                'educational_value': '中价值：基础描述'
            }
        }
        
        return cluster_info.get(cluster, {
            'chinese_name': cluster,
            'explanation': f'{cluster}组词汇有共同的学习特征，组合学习效果更佳。',
            'educational_value': '中价值：组合学习'
        })
    
    def _rank_by_educational_value(self, recommendations: List[LinguisticRecommendation], user_id: int) -> List[LinguisticRecommendation]:
        """按教育价值对推荐进行排序 - 优化版：降低语义推荐权重"""
        
        # 获取用户配置并应用权重调整
        config = recommendation_config_manager.get_user_config(user_id)
        
        # 基础优先级权重 - 语音优先策略
        base_priority_weights = {
            # 语音相关 - 最高优先级（直接影响拼写和发音）
            'silent_letter': 100,     # 不发音字母规律 - 最重要
            'phonetic': 95,          # 发音规律（双元音等）
            'rhyme': 90,             # 押韵模式（有意义的词缀）
            'syllable': 88,          # 音节结构（开音节等）
            
            # 词缀相关 - 次优先级（有助于词汇扩展）
            'true_prefix': 85,       # 真前缀 - 构词规律
            'suffix': 80,            # 后缀 - 词性变化
            'morphology': 75,        # 词根词缀
            'root': 73,              # 词根
            
            # 语法相关 - 中等优先级
            'grammar': 70,           # 语法变形
            'plural_forms': 68,      # 复数形式
            'adjective_forms': 65,   # 形容词变化
            
            # 对比学习 - 保留但降权
            'contrast': 60,          # 对比概念
            'cluster_opposite_pairs': 55,  # 反义词对
            
            # 语义推荐 - 大幅降权（避免干扰）
            'semantic_family': 20,    # 家庭成员
            'semantic_emotion': 15,   # 情感词汇
            'semantic_basic_adjectives': 10,  # 基础形容词
            'semantic_time': 10,      # 时间概念
            'semantic_color': 5,      # 颜色词汇
            'cluster_family_members': 15,
            'cluster_emotion_adjectives': 10
        }
        
        # 应用用户配置的权重调整
        priority_weights = config.apply_weights(base_priority_weights)
        
        def get_priority(rec: LinguisticRecommendation) -> int:
            return priority_weights.get(rec.category, 50)
        
        # 按优先级排序，同优先级按相似词数量排序
        recommendations.sort(
            key=lambda r: (get_priority(r), len(r.similar_words)), 
            reverse=True
        )
        
        return recommendations
    
    def get_linguistic_statistics(self) -> Dict:
        """获取语言学标注统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 基础统计
            cursor.execute("SELECT COUNT(*) FROM word_linguistic_annotations")
            total_annotated = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM word")
            total_words = cursor.fetchone()[0]
            
            # 前缀统计
            cursor.execute("""
                SELECT prefix, COUNT(*) as count
                FROM word_linguistic_annotations 
                WHERE has_true_prefix = 1 AND prefix IS NOT NULL
                GROUP BY prefix
                ORDER BY count DESC
            """)
            prefix_stats = cursor.fetchall()
            
            # 语义组统计
            cursor.execute("""
                SELECT semantic_groups, COUNT(*) as count
                FROM word_linguistic_annotations 
                WHERE semantic_groups IS NOT NULL AND semantic_groups != '[]'
                GROUP BY semantic_groups
                ORDER BY count DESC
                LIMIT 10
            """)
            semantic_stats = cursor.fetchall()
            
            conn.close()
            
            return {
                'total_words': total_words,
                'total_annotated': total_annotated,
                'annotation_rate': round(total_annotated / total_words * 100, 1),
                'prefix_distribution': prefix_stats,
                'top_semantic_groups': semantic_stats
            }
            
        except Exception as e:
            logger.error(f"获取语言学统计失败: {e}")
            return {}