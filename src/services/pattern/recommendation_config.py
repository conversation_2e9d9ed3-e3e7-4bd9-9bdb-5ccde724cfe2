"""
推荐系统配置管理
提供灵活的推荐策略配置选项
"""

from typing import Dict, Any
from dataclasses import dataclass
from src.core import get_logger

logger = get_logger(__name__)


@dataclass
class RecommendationConfig:
    """推荐系统配置"""
    
    # 推荐策略控制
    enable_semantic_recommendations: bool = True      # 是否启用语义推荐
    enable_morphology_recommendations: bool = True    # 是否启用词根词缀推荐
    enable_phonetic_recommendations: bool = True      # 是否启用发音规律推荐
    enable_grammar_recommendations: bool = True       # 是否启用语法变形推荐
    
    # 场景控制
    testing_mode_disable_semantic: bool = True        # 答题模式是否禁用语义推荐
    testing_mode_max_recommendations: int = 2         # 答题模式最大推荐数
    learning_mode_max_recommendations: int = 4        # 学习模式最大推荐数
    
    # 权重配置
    semantic_weight_factor: float = 0.5               # 语义推荐权重因子 (乘以原权重)
    morphology_weight_factor: float = 1.2             # 词根词缀权重因子
    phonetic_weight_factor: float = 1.1               # 发音规律权重因子
    
    # 过滤控制
    min_similarity_threshold: float = 0.3             # 最低相似度阈值
    max_semantic_group_size: int = 3                  # 语义组最大推荐数
    
    @classmethod
    def get_default_config(cls) -> 'RecommendationConfig':
        """获取默认配置"""
        return cls()
    
    @classmethod 
    def get_memory_focused_config(cls) -> 'RecommendationConfig':
        """获取记忆导向配置：减少语义推荐，增强记忆技巧推荐"""
        return cls(
            enable_semantic_recommendations=False,  # 完全禁用语义推荐
            morphology_weight_factor=1.2,          # 适度加强词根词缀
            phonetic_weight_factor=2.0,            # 大幅加强发音规律（最重要）
            max_semantic_group_size=0,              # 完全禁止语义组推荐
            testing_mode_max_recommendations=2,     # 答题时最多2个推荐
            semantic_weight_factor=0.0,             # 语义权重归零
        )
    
    @classmethod
    def get_learning_focused_config(cls) -> 'RecommendationConfig':
        """获取学习导向配置：平衡各种推荐策略"""
        return cls(
            enable_semantic_recommendations=True,
            semantic_weight_factor=0.7,            # 适度降低语义权重
            morphology_weight_factor=1.3,          # 适度增强词根词缀
            phonetic_weight_factor=1.2,            # 适度增强发音规律
            max_semantic_group_size=2,              # 限制语义组大小
        )
    
    @classmethod
    def get_phonetic_focused_config(cls) -> 'RecommendationConfig':
        """获取语音优先配置：专注于发音规律学习"""
        return cls(
            enable_semantic_recommendations=False,  # 禁用语义推荐
            enable_morphology_recommendations=True, # 保留词根词缀（次要）
            enable_phonetic_recommendations=True,   # 启用发音规律（主要）
            enable_grammar_recommendations=False,   # 禁用语法推荐
            testing_mode_disable_semantic=True,     # 答题模式禁用语义
            testing_mode_max_recommendations=3,     # 答题时最多3个推荐
            learning_mode_max_recommendations=5,    # 学习时最多5个推荐
            semantic_weight_factor=0.0,             # 语义权重归零
            morphology_weight_factor=1.0,           # 词根词缀正常权重
            phonetic_weight_factor=2.5,             # 发音规律2.5倍权重
            min_similarity_threshold=0.5,           # 提高相似度阈值
            max_semantic_group_size=0,              # 完全禁止语义组
        )
    
    def apply_weights(self, priority_weights: Dict[str, int]) -> Dict[str, int]:
        """应用权重因子到优先级权重"""
        adjusted_weights = priority_weights.copy()
        
        # 应用语义权重因子
        for key in adjusted_weights:
            if 'semantic' in key:
                adjusted_weights[key] = int(adjusted_weights[key] * self.semantic_weight_factor)
            elif 'morphology' in key or 'prefix' in key or 'suffix' in key or 'root' in key:
                adjusted_weights[key] = int(adjusted_weights[key] * self.morphology_weight_factor)
            elif 'phonetic' in key or 'rhyme' in key or 'silent' in key:
                adjusted_weights[key] = int(adjusted_weights[key] * self.phonetic_weight_factor)
        
        return adjusted_weights
    
    def get_max_recommendations(self, context: str) -> int:
        """根据使用场景获取最大推荐数"""
        if context == 'testing':
            return self.testing_mode_max_recommendations
        else:
            return self.learning_mode_max_recommendations
    
    def should_enable_semantic(self, context: str) -> bool:
        """判断是否应该启用语义推荐"""
        if not self.enable_semantic_recommendations:
            return False
        
        if context == 'testing' and self.testing_mode_disable_semantic:
            return False
            
        return True


class RecommendationConfigManager:
    """推荐配置管理器"""
    
    def __init__(self):
        self._user_configs: Dict[int, RecommendationConfig] = {}
        self._default_config = RecommendationConfig.get_phonetic_focused_config()  # 默认使用语音优先配置
    
    def get_user_config(self, user_id: int) -> RecommendationConfig:
        """获取用户的推荐配置"""
        return self._user_configs.get(user_id, self._default_config)
    
    def set_user_config(self, user_id: int, config: RecommendationConfig):
        """设置用户的推荐配置"""
        self._user_configs[user_id] = config
        logger.info(f"已更新用户{user_id}的推荐配置")
    
    def set_user_preference(self, user_id: int, preference: str):
        """根据偏好设置用户配置
        
        Args:
            user_id: 用户ID
            preference: 偏好类型 'memory_focused', 'learning_focused', 'default'
        """
        if preference == 'memory_focused':
            config = RecommendationConfig.get_memory_focused_config()
        elif preference == 'learning_focused':
            config = RecommendationConfig.get_learning_focused_config()
        else:
            config = RecommendationConfig.get_default_config()
        
        self.set_user_config(user_id, config)
        logger.info(f"用户{user_id}设置推荐偏好为: {preference}")
    
    def reset_user_config(self, user_id: int):
        """重置用户配置为默认"""
        if user_id in self._user_configs:
            del self._user_configs[user_id]
            logger.info(f"已重置用户{user_id}的推荐配置")


# 全局配置管理器实例
recommendation_config_manager = RecommendationConfigManager()