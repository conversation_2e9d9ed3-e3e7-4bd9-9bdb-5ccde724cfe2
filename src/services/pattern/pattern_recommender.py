"""
Pattern推荐服务
基于单词pattern为用户推荐相似的已学单词，辅助记忆
现在集成了语言学专家推荐系统，提供更准确的推荐结果
"""

import sqlite3
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from src.core import get_logger
from .concept_integrator import ConceptIntegrator

logger = get_logger(__name__)


@dataclass
class SimilarWord:
    """相似单词推荐结果"""
    word_id: int
    english_word: str
    chinese_meaning: str
    similarity_score: float
    shared_patterns: List[Dict]
    learning_status: str  # 'new', 'review', 'attention'
    proficiency: float


@dataclass
class PatternRecommendation:
    """Pattern推荐结果"""
    pattern_info: Dict
    similar_words: List[SimilarWord]
    recommendation_reason: str


class PatternRecommenderService:
    """Pattern推荐服务 - 支持智能概念整合和语言学专家推荐"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._max_recommendations = 5  # 最多推荐5个相似单词
        self._min_similarity_score = 0.3  # 最低相似度阈值
        self._concept_integrator = ConceptIntegrator()  # 概念整合器
        
        # 集成语言学专家推荐器
        try:
            from ..linguistic_recommender import LinguisticRecommenderService
            self._linguistic_recommender = LinguisticRecommenderService(db_path)
            self._use_linguistic_recommendations = True
            logger.info("语言学专家推荐器已启用")
        except ImportError:
            self._linguistic_recommender = None
            self._use_linguistic_recommendations = False
            logger.warning("语言学专家推荐器不可用，将使用传统推荐算法")
    
    def get_similar_words(self, user_id: int, word_id: int, exclude_new_words: bool = False, 
                         context: str = 'learning') -> List[PatternRecommendation]:
        """获取与指定单词相似的已学单词推荐
        
        Args:
            user_id: 用户ID
            word_id: 目标单词ID
            exclude_new_words: 是否排除未学习的单词
            context: 使用场景 'learning'(学习) 或 'testing'(答题)
            
        Returns:
            按相似度排序的推荐列表
        """
        try:
            # 优先使用语言学专家推荐
            if self._use_linguistic_recommendations:
                return self._get_linguistic_recommendations(user_id, word_id, context)
            
            # 回退到传统推荐算法
            return self._get_traditional_recommendations(user_id, word_id, exclude_new_words, context)
            
        except Exception as e:
            logger.error(f"获取相似单词推荐失败: {e}")
            return []
    
    def _get_linguistic_recommendations(self, user_id: int, word_id: int, context: str = 'learning') -> List[PatternRecommendation]:
        """使用语言学专家推荐系统"""
        try:
            linguistic_recs = self._linguistic_recommender.get_linguistic_recommendations(user_id, word_id, context)
            
            # 转换为PatternRecommendation格式
            pattern_recommendations = []
            
            for ling_rec in linguistic_recs:
                # 转换相似词格式
                similar_words = []
                for ling_word in ling_rec.similar_words:
                    similar_word = SimilarWord(
                        word_id=ling_word.word_id,
                        english_word=ling_word.english_word,
                        chinese_meaning=ling_word.chinese_meaning,
                        similarity_score=ling_word.confidence,
                        shared_patterns=[{
                            'pattern_type': ling_word.similarity_type,
                            'pattern_name': ling_rec.category_name,
                            'match_reason': ling_word.similarity_reason
                        }],
                        learning_status=ling_word.learning_status,
                        proficiency=ling_word.proficiency
                    )
                    similar_words.append(similar_word)
                
                # 创建PatternRecommendation
                pattern_info = {
                    'pattern_type': ling_rec.category,
                    'pattern_name': ling_rec.category_name,
                    'linguistic_principle': ling_rec.linguistic_principle,
                    'educational_value': ling_rec.educational_value,
                    'is_linguistic_expert': True
                }
                
                pattern_rec = PatternRecommendation(
                    pattern_info=pattern_info,
                    similar_words=similar_words[:self._max_recommendations],
                    recommendation_reason=ling_rec.explanation
                )
                pattern_recommendations.append(pattern_rec)
            
            logger.info(f"语言学专家推荐返回{len(pattern_recommendations)}个推荐组")
            return pattern_recommendations
            
        except Exception as e:
            logger.error(f"语言学推荐失败，回退到传统算法: {e}")
            return self._get_traditional_recommendations(user_id, word_id, False)
    
    def _get_traditional_recommendations(self, user_id: int, word_id: int, exclude_new_words: bool, context: str = 'learning') -> List[PatternRecommendation]:
        """传统推荐算法（原有逻辑）"""
        try:
            # 获取目标单词信息
            target_word = self._get_word_info(word_id, user_id)
            if not target_word:
                logger.info(f"单词{word_id}不存在")
                return []
            
            # 获取目标单词的patterns
            target_patterns = self._get_word_patterns(word_id)
            
            # 获取用户已学习的单词
            learned_words = self._get_user_learned_words(user_id, exclude_new_words)
            if not learned_words:
                logger.info(f"用户{user_id}没有已学习的单词")
                return []
            
            # 动态检测更多关联模式 (根据场景过滤)
            dynamic_patterns = self._detect_dynamic_patterns(target_word, learned_words, context)
            
            # 使用智能概念整合器处理去重和整合
            user_avg_proficiency = sum(w['proficiency'] for w in learned_words) / len(learned_words) if learned_words else 0
            all_patterns = self._concept_integrator.integrate_patterns(
                target_patterns, dynamic_patterns, user_avg_proficiency
            )
            
            recommendations = []
            
            # patterns已经由概念整合器按认知层次排序，直接处理
            for pattern in all_patterns:
                similar_words = self._find_similar_words_by_pattern(
                    pattern, learned_words, word_id
                )
                
                if similar_words:
                    recommendation = PatternRecommendation(
                        pattern_info=pattern,
                        similar_words=similar_words[:self._max_recommendations],
                        recommendation_reason=self._generate_recommendation_reason(pattern)
                    )
                    recommendations.append(recommendation)
            
            # 限制返回的pattern数量（避免信息过载）
            # 概念整合后的patterns已经去重，可以直接返回前几个
            max_recommendations = 3
            if user_avg_proficiency >= 80:
                max_recommendations = 4  # 高级用户可以看更多
            elif user_avg_proficiency < 30:
                max_recommendations = 2  # 初学者信息简化
            
            return recommendations[:max_recommendations]
            
        except Exception as e:
            logger.error(f"传统推荐算法失败: {e}")
            return []
    
    # 原来的去重方法已被ConceptIntegrator替代
    
    def _get_word_info(self, word_id: int, user_id: int = None) -> Dict:
        """获取单词基本信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning, w.section,
                       uw.proficiency
                FROM word w
                LEFT JOIN user_word uw ON w.id = uw.word_id AND uw.user_id = ?
                WHERE w.id = ?
            """, (user_id or 1, word_id))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                # 导入proficiency计算器来计算difficulty_level
                from ..proficiency.calculator import ProficiencyCalculator
                
                proficiency = row[4] if row[4] is not None else 0.0
                difficulty_level = ProficiencyCalculator.calculate_difficulty_level(proficiency)
                
                return {
                    'word_id': row[0],
                    'english_word': row[1],
                    'chinese_meaning': row[2],
                    'section': row[3] or '',
                    'proficiency': proficiency,
                    'difficulty_level': difficulty_level
                }
            return {}
            
        except Exception as e:
            logger.error(f"获取单词信息失败: {e}")
            return {}
    
    def _detect_dynamic_patterns(self, target_word: Dict, learned_words: List[Dict], context: str = 'learning') -> List[Dict]:
        """动态检测多层次认知模式 - 按四维度框架重构"""
        patterns = []
        english_word = target_word['english_word'].lower()
        chinese_meaning = target_word['chinese_meaning']
        
        # 计算用户平均水平用于认知层次适配
        avg_proficiency = sum(w['proficiency'] for w in learned_words) / len(learned_words) if learned_words else 0
        
        # ===== 维度1: 拼写与发音 (Orthography & Phonetics) =====
        if avg_proficiency < 70:  # 基础和中级学习者重点检测
            # 1.1 字母-发音组合
            phonetic_patterns = self._detect_phonetic_patterns(english_word, learned_words)
            patterns.extend(phonetic_patterns)
            
            # 1.2 押韵组合
            rhyme_patterns = self._detect_rhyme_patterns(english_word, learned_words)
            patterns.extend(rhyme_patterns)
            
            # 1.3 特殊发音规则（不发音字母、开音节等）
            special_patterns = self._detect_special_phonetic_patterns(english_word, learned_words)
            patterns.extend(special_patterns)
        
        # ===== 维度2: 词义关联 (Semantic Association) =====
        # 答题模式下大幅减少语义推荐，避免泄露答案
        if context != 'testing' and len(learned_words) >= 10:  
            # 2.1 主题分组 (仅学习模式)
            semantic_patterns = self._detect_semantic_patterns(chinese_meaning, learned_words)
            patterns.extend(semantic_patterns)
        
        # 2.2 同义词反义词（保留，对学习有价值）
        if avg_proficiency >= 50:
            synonym_antonym_patterns = self._detect_synonym_antonym_patterns(target_word, learned_words)
            patterns.extend(synonym_antonym_patterns)
        
        # ===== 维度3: 构词与变形 (Morphology & Inflection) =====
        if avg_proficiency >= 40:  # 中级以上检测构词规律
            # 3.1 词根词缀
            morphology_patterns = self._detect_morphology_patterns(english_word, learned_words)
            patterns.extend(morphology_patterns)
            
            # 3.2 词形变化
            if avg_proficiency >= 60:
                adjective_patterns = self._detect_adjective_patterns(english_word, learned_words)
                patterns.extend(adjective_patterns)
                
                plural_patterns = self._detect_plural_patterns(english_word, learned_words)
                patterns.extend(plural_patterns)
        
        # ===== 维度4: 搭配与用法 (Collocation & Usage) =====
        if avg_proficiency >= 60:  # 高级学习者检测搭配
            collocation_patterns = self._detect_collocation_patterns(english_word, learned_words)
            patterns.extend(collocation_patterns)
        
        # 为所有动态patterns添加认知层次信息
        for pattern in patterns:
            if 'cognitive_level' not in pattern:
                pattern['cognitive_level'] = self._infer_cognitive_level(pattern, avg_proficiency)
            if 'dimension_category' not in pattern:
                pattern['dimension_category'] = self._infer_dimension_category(pattern['pattern_type'])
            if 'educational_value' not in pattern:
                pattern['educational_value'] = self._calculate_educational_value(pattern, learned_words)
        
        return patterns
    
    def _infer_cognitive_level(self, pattern: Dict, user_proficiency: float) -> str:
        """根据pattern类型和用户水平推断认知层次 - 基于认知科学理论"""
        pattern_type = pattern.get('pattern_type', '')
        
        # 基础感知层patterns - 直接视听觉识别，无需复杂认知处理
        basic_types = {
            'letter_combo', 'phonetic', 'rhyme', 'silent_letter', 'syllable',
            'grapheme_phoneme', 'vowel_pattern', 'plural_forms'
        }
        
        # 语义关联层patterns - 需要意义理解和概念连接  
        intermediate_types = {
            'semantic', 'theme', 'synonym', 'antonym', 'adjective_forms',
            'suffix', 'similar_spelling', 'contextual'
        }
        
        # 高级应用层patterns - 需要语法规律掌握和灵活应用
        advanced_types = {
            'morphology', 'root', 'prefix', 'collocation', 'usage_pattern',
            'verb_forms'
        }
        
        # 固定认知层次分类
        if pattern_type in basic_types:
            base_level = 'basic'
        elif pattern_type in intermediate_types:
            base_level = 'intermediate' 
        elif pattern_type in advanced_types:
            base_level = 'advanced'
        else:
            # 未知类型按用户水平动态判断
            if user_proficiency < 50:
                base_level = 'basic'
            elif user_proficiency < 80:
                base_level = 'intermediate'
            else:
                base_level = 'advanced'
        
        # 用户水平调节机制（在保持基本分类的前提下微调）
        if user_proficiency < 30:
            # 初学者：高级内容降级到中级，中级内容保持
            if base_level == 'advanced':
                return 'intermediate'
        elif user_proficiency > 85:
            # 高级学习者：基础内容可以升级，保持挑战性
            if base_level == 'basic' and pattern_type in {'letter_combo', 'phonetic'}:
                return 'intermediate'
        
        return base_level
    
    def _infer_dimension_category(self, pattern_type: str) -> str:
        """根据pattern_type推断维度分类 - 基于四维度认知框架"""
        
        # 拼写与发音维度 (Orthography & Phonetics) - 字形、音形对应
        orthography_types = {
            'letter_combo', 'phonetic', 'rhyme', 'silent_letter', 'syllable',
            'grapheme_phoneme', 'vowel_pattern', 'similar_spelling'
        }
        
        # 词义关联维度 (Semantic Association) - 意义、概念关系
        semantic_types = {
            'semantic', 'theme', 'synonym', 'antonym'
        }
        
        # 构词与变形维度 (Morphology & Inflection) - 词根词缀、语法变形
        morphology_types = {
            'morphology', 'root', 'prefix', 'suffix', 
            'adjective_forms', 'plural_forms', 'verb_forms'
        }
        
        # 搭配与用法维度 (Collocation & Usage) - 语境使用、搭配关系
        collocation_types = {
            'collocation', 'usage_pattern', 'contextual'
        }
        
        # 按四维度框架分类
        if pattern_type in orthography_types:
            return 'orthography'
        elif pattern_type in semantic_types:
            return 'semantic'
        elif pattern_type in morphology_types:
            return 'morphology'
        elif pattern_type in collocation_types:
            return 'collocation'
        else:
            # 未知类型默认归入语义关联维度（最通用）
            return 'semantic'
    
    def _calculate_educational_value(self, pattern: Dict, learned_words: List[Dict]) -> float:
        """计算pattern的教育价值 - 基于认知科学优化"""
        base_value = 0.5
        
        # 1. 相似单词数量权重（学习网络密度）
        similar_count = len(pattern.get('similar_words', []))
        if similar_count >= 8:
            base_value += 0.25  # 高密度学习网络
        elif similar_count >= 5:
            base_value += 0.2   # 中密度学习网络
        elif similar_count >= 3:
            base_value += 0.1   # 基础学习网络
        elif similar_count >= 2:
            base_value += 0.05  # 最小有效网络
        
        # 2. 匹配强度权重（模式确定性）
        match_strength = pattern.get('match_strength', 0.5)
        base_value += match_strength * 0.2
        
        # 3. 认知层次价值加权
        cognitive_level = pattern.get('cognitive_level', 'basic')
        cognitive_weights = {
            'basic': 0.1,       # 基础认知价值高（适合初学者）
            'intermediate': 0.15, # 中级认知价值最高（核心学习阶段）
            'advanced': 0.05    # 高级认知价值适中（精英化）
        }
        base_value += cognitive_weights.get(cognitive_level, 0.1)
        
        # 4. 维度分类加权（按重要性）
        dimension_category = pattern.get('dimension_category', 'semantic')
        dimension_weights = {
            'orthography': 0.15,  # 拼写发音基础重要
            'semantic': 0.2,      # 语义关联最重要
            'morphology': 0.1,    # 构词变形中等重要
            'collocation': 0.05   # 搭配用法较高级
        }
        base_value += dimension_weights.get(dimension_category, 0.1)
        
        # 5. 特殊模式加分
        if pattern.get('is_primary', False):
            base_value += 0.1
        
        # 6. 概念组整合奖励
        if pattern.get('concept_group'):
            base_value += 0.05  # 有概念归属的pattern更有价值
        
        # 7. 语言学习规律加分
        pattern_type = pattern.get('pattern_type', '')
        learning_priority_bonus = {
            # 高频基础模式
            'letter_combo': 0.1, 'theme': 0.1, 'phonetic': 0.1,
            # 中频进阶模式  
            'semantic': 0.08, 'prefix': 0.08, 'suffix': 0.08,
            # 高级专业模式
            'root': 0.05, 'collocation': 0.05, 'morphology': 0.05
        }
        base_value += learning_priority_bonus.get(pattern_type, 0.03)
        
        return min(base_value, 1.0)
    
    def _detect_special_phonetic_patterns(self, english_word: str, learned_words: List[Dict]) -> List[Dict]:
        """检测特殊发音规则（整合原来的不发音字母和开音节检测）"""
        patterns = []
        
        # 不发音字母检测
        silent_patterns = self._detect_silent_letters(english_word, learned_words)
        patterns.extend(silent_patterns)
        
        # 开音节检测
        open_syllable_pattern = self._detect_open_syllable(english_word, learned_words)
        if open_syllable_pattern:
            patterns.append(open_syllable_pattern)
        
        return patterns
    
    def _detect_semantic_patterns(self, chinese_meaning: str, learned_words: List[Dict]) -> List[Dict]:
        """检测语义关联模式"""
        patterns = []
        
        # 定义语义关键词映射 - 更灵活的形容词分类
        semantic_keywords = {
            # 形容词大类 - 使用更宽泛的匹配
            '描述性形容词': ['的', '色'],  # 匹配所有以"的"结尾的形容词和颜色词
            '体质外观': ['强壮', '虚弱', '健康', '瘦', '胖', '高', '矮', '大', '小', '美', '丑', '漂亮'],
            '情感性格': ['生气', '开心', '高兴', '快乐', '悲伤', '难过', '愤怒', '害怕', '勇敢', '聪明'],
            '颜色相关': ['红', '橙', '黄', '绿', '蓝', '紫', '黑', '白', '灰', '棕', '粉', '金', '色'],
            '尺寸方位': ['大', '小', '长', '短', '宽', '窄', '厚', '薄', '深', '浅', '左', '右', '上', '下'],
            
            # 名词类别
            '家庭成员': ['父亲', '母亲', '爸爸', '妈妈', '儿子', '女儿', '兄弟', '姐妹', '叔叔', '姑妈'],
            '身体部位': ['头', '手', '脚', '眼', '耳', '鼻', '嘴', '胳膊', '腿', '脸'],
            '动物': ['猫', '狗', '鸟', '鱼', '马', '牛', '猪', '羊', '鸡', '鸭'],
            '食物': ['苹果', '面包', '牛奶', '水', '米饭', '肉', '蛋', '蛋糕', '鱼', '菜'],
            '学校': ['老师', '学生', '书', '笔', '纸', '桌子', '教室', '学校', '课'],
            '运动': ['足球', '篮球', '网球', '游泳', '跑步', '跳跃', '运动'],
            
            # 动词类别  
            '运动动作': ['跑', '走', '跳', '游泳', '爬', '骑', '踢', '打', '投'],
            '日常动作': ['吃', '喝', '睡', '工作', '学习', '玩', '读', '写', '看'],
            '交流动作': ['说', '告诉', '讲', '谈', '听', '问', '回答']
        }
        
        for category, keywords in semantic_keywords.items():
            if any(keyword in chinese_meaning for keyword in keywords):
                # 找到相同语义类别的已学单词
                semantic_similar = []
                for word in learned_words:
                    if (any(keyword in word['chinese_meaning'] for keyword in keywords) and
                        # 过滤掉短语，只保留单个单词
                        ' ' not in word['english_word']):
                        semantic_similar.append(word)
                
                if len(semantic_similar) >= 2:  # 降低阈值，提高推荐几率
                    patterns.append({
                        'pattern_id': f'semantic_{category}',
                        'pattern_type': 'semantic',
                        'pattern_value': category,
                        'pattern_name': f'{category}类词汇',
                        'match_strength': 0.8,
                        'match_reason': f'都属于{category}相关概念',
                        'is_primary': True,
                        'similar_words': semantic_similar[:5]
                    })
                break
        
        return patterns
    
    def _detect_rhyme_patterns(self, english_word: str, learned_words: List[Dict]) -> List[Dict]:
        """检测有意义的音韵模式（押韵）- 重构版，只保留有教育价值的模式"""
        patterns = []
        
        # 定义有意义的押韵后缀（有真实音韵价值的）
        meaningful_endings = {
            'tion': 'tion结尾词',  # action, station, nation
            'sion': 'sion结尾词',  # decision, television  
            'ness': 'ness结尾词',  # kindness, happiness
            'ment': 'ment结尾词',  # development, government
            'able': 'able结尾词',  # comfortable, terrible
            'ible': 'ible结尾词',  # possible, terrible
            'ful': 'ful结尾词',    # beautiful, wonderful
            'less': 'less结尾词',  # careless, homeless
            'ly': 'ly结尾词',      # quickly, slowly (但要过滤掉单纯的副词)
            'ed': 'ed过去式',      # played, worked (规则动词过去式)
            'er': 'er结尾词',      # teacher, player
            'est': 'est最高级',    # biggest, fastest
            'ing': 'ing现在分词'   # playing, working
        }
        
        # 无意义的后缀，不应作为推荐依据
        meaningless_endings = {
            'ng', 'ong', 'ang', 'ung', 'st', 'nt', 'nd', 'rd', 'th', 'gh'
        }
        
        # 检测有意义的音韵模式
        for suffix, pattern_name in meaningful_endings.items():
            if english_word.lower().endswith(suffix):
                rhyme_similar = []
                for word in learned_words:
                    if (word['english_word'].lower().endswith(suffix) and
                        word['english_word'].lower() != english_word and
                        # 过滤掉短语
                        ' ' not in word['english_word']):
                        rhyme_similar.append(word)
                
                # 只有找到足够相似词才创建pattern
                if len(rhyme_similar) >= 2:
                    patterns.append({
                        'pattern_id': f'rhyme_{suffix}',
                        'pattern_type': 'rhyme',
                        'pattern_value': suffix,
                        'pattern_name': pattern_name,
                        'match_strength': 0.7,  # 提高有意义pattern的权重
                        'match_reason': f'都以{suffix}结尾，有相似的词缀规律',
                        'is_primary': False,
                        'similar_words': rhyme_similar[:4]
                    })
                    break  # 找到一个就够了，避免过度推荐
        
        return patterns
    
    def _detect_phonetic_patterns(self, english_word: str, learned_words: List[Dict]) -> List[Dict]:
        """检测发音规律模式"""
        patterns = []
        
        # 双元音和元音组合检测 - 扩展版
        diphthongs = {
            # 基础双元音
            'ai': {'sound': '/eɪ/', 'desc': '发音类似"诶"', 'examples': 'rain, wait, train, main, pain'},
            'ay': {'sound': '/eɪ/', 'desc': '发音类似"诶"', 'examples': 'say, play, day, may, way'},
            'ea': {'sound': '/iː/或/e/', 'desc': '发音类似"衣"或"诶"', 'examples': 'tea, read, eat (长音) / bread, head (短音)'},
            'ee': {'sound': '/iː/', 'desc': '发音类似"衣"', 'examples': 'see, tree, free, meet, need'},
            'oa': {'sound': '/əʊ/', 'desc': '发音类似"欧"', 'examples': 'boat, road, coat, goal, soap'},
            'oo': {'sound': '/uː/或/ʊ/', 'desc': '发音类似"乌"', 'examples': 'moon, food (长音) / book, good (短音)'},
            'ou': {'sound': '/aʊ/或/ɔː/', 'desc': '发音多样', 'examples': 'out, house (奥) / bought, thought (奥)'},
            'ow': {'sound': '/aʊ/或/əʊ/', 'desc': '发音多样', 'examples': 'now, how (奥) / show, know (欧)'},
            'oi': {'sound': '/ɔɪ/', 'desc': '发音类似"哦一"', 'examples': 'oil, coin, voice, choice, point'},
            'oy': {'sound': '/ɔɪ/', 'desc': '发音类似"哦一"', 'examples': 'boy, toy, enjoy, loyal, royal'},
            
            # R控制元音
            'ar': {'sound': '/ɑː/', 'desc': '发音类似"啊儿"', 'examples': 'car, star, far, hard, park'},
            'er': {'sound': '/ɜː/', 'desc': '发音类似"儿"', 'examples': 'her, teacher, water, sister, letter'},
            'ir': {'sound': '/ɜː/', 'desc': '发音类似"儿"', 'examples': 'bird, girl, first, third, shirt'},
            'or': {'sound': '/ɔː/', 'desc': '发音类似"奥儿"', 'examples': 'for, more, door, sport, short'},
            'ur': {'sound': '/ɜː/', 'desc': '发音类似"儿"', 'examples': 'turn, hurt, nurse, purple, Thursday'},
            
            # 新增重要组合
            'au': {'sound': '/ɔː/', 'desc': '发音类似"奥"', 'examples': 'caught, taught, daughter, August, autumn'},
            'aw': {'sound': '/ɔː/', 'desc': '发音类似"奥"', 'examples': 'saw, draw, law, strawberry, awful'},
            'ew': {'sound': '/juː/或/uː/', 'desc': '发音类似"优"或"乌"', 'examples': 'new, few (优) / flew, grew (乌)'},
            'ie': {'sound': '/iː/或/aɪ/', 'desc': '发音多样', 'examples': 'piece, field (衣) / pie, tie (爱)'},
            'igh': {'sound': '/aɪ/', 'desc': '发音类似"爱"', 'examples': 'high, light, night, right, sight'},
            'eigh': {'sound': '/eɪ/', 'desc': '发音类似"诶"', 'examples': 'eight, weight, neighbor, freight'},
            'ue': {'sound': '/juː/', 'desc': '发音类似"优"', 'examples': 'blue, true, value, rescue, continue'}
        }
        
        for combo, info in diphthongs.items():
            if combo in english_word:
                # 找到含有相同双元音的已学单词
                similar_words = []
                for word in learned_words:
                    if combo in word['english_word'].lower() and word['english_word'].lower() != english_word:
                        similar_words.append(word)
                
                if len(similar_words) >= 2:
                    patterns.append({
                        'pattern_id': f'diphthong_{combo}',
                        'pattern_type': 'phonetic',
                        'pattern_value': combo,
                        'pattern_name': f'{combo}元音组合',
                        'match_strength': 0.9,  # 提高到0.9
                        'match_reason': f'都含有{combo}组合，{info["desc"]}',
                        'is_primary': True,
                        'cognitive_level': 'basic',
                        'phonetic_info': info,
                        'similar_words': similar_words[:10]  # 增加到10个例子
                    })
        
        # 不发音字母检测
        silent_patterns = self._detect_silent_letters(english_word, learned_words)
        patterns.extend(silent_patterns)
        
        # 开音节检测
        open_syllable_pattern = self._detect_open_syllable(english_word, learned_words)
        if open_syllable_pattern:
            patterns.append(open_syllable_pattern)
        
        return patterns
    
    def _detect_silent_letters(self, english_word: str, learned_words: List[Dict]) -> List[Dict]:
        """检测不发音字母模式"""
        patterns = []
        
        # 常见不发音字母规则 - 大幅扩展gh模式
        silent_rules = {
            'gh': {
                'positions': [
                    # 原有模式
                    'igh',    # high, light, night, right, sight, flight
                    'augh',   # caught, daughter, taught, naughty, slaughter
                    'ough',   # thought, bought, brought, fought, sought, ought, through, though
                    # 新增模式
                    'eigh',   # weigh, neighbor, eight, weight
                    'aight',  # straight
                    'eight',  # height
                ],
                'desc': 'gh在这些组合中不发音（注意：laugh/cough/enough/rough中gh发/f/音）'
            },
            'k': {'positions': ['kn'], 'desc': 'k在n前不发音（如knife, know, knee）'},
            'w': {'positions': ['wr'], 'desc': 'w在r前不发音（如write, wrong, wrap）'},
            'b': {'positions': ['mb'], 'desc': 'b在词尾的mb组合中不发音（如lamb, thumb, comb）'},
            'l': {'positions': ['alk', 'olk', 'alf', 'alm', 'ould'], 'desc': 'l在这些组合中不发音（如walk, talk, half, calm, could）'},
            'h': {'positions': ['wh', 'rh', 'gh'], 'desc': 'h在这些组合中常不发音（如what, rhyme, ghost）'},
            'p': {'positions': ['ps', 'pt', 'pn'], 'desc': 'p在词首的这些组合中不发音（如psychology, pterodactyl, pneumonia）'},
            't': {'positions': ['stle', 'sten'], 'desc': 't在这些组合中不发音（如castle, listen, fasten）'},
            'g': {'positions': ['gn'], 'desc': 'g在gn组合中不发音（如sign, design, gnome）'}
        }
        
        for letter, rule in silent_rules.items():
            for position in rule['positions']:
                if position in english_word:
                    # 找到含有相同不发音模式的已学单词
                    similar_words = []
                    for word in learned_words:
                        if position in word['english_word'].lower() and word['english_word'].lower() != english_word:
                            similar_words.append(word)
                    
                    if len(similar_words) >= 2:
                        patterns.append({
                            'pattern_id': f'silent_{letter}_{position}',
                            'pattern_type': 'silent_letter',
                            'pattern_value': position,
                            'pattern_name': f'{position}不发音组合',
                            'match_strength': 0.95,  # 提高到0.95，因为这是最重要的规律
                            'match_reason': f'都含有{position}组合，其中{letter}{rule["desc"]}',
                            'is_primary': True,  # 设为主要模式
                            'cognitive_level': 'basic',  # 改为基础级别，因为这是拼写的基础
                            'similar_words': similar_words[:8]  # 增加到8个，提供更多例子
                        })
                        break
        
        return patterns
    
    def _detect_open_syllable(self, english_word: str, learned_words: List[Dict]) -> Optional[Dict]:
        """检测开音节模式"""
        import re
        
        # 开音节模式：辅音+元音+辅音+e (如 make, like, home)
        open_syllable_pattern = re.compile(r'[bcdfghjklmnpqrstvwxyz][aeiou][bcdfghjklmnpqrstvwxyz]e$')
        
        if open_syllable_pattern.search(english_word):
            # 找到其他开音节单词
            similar_words = []
            for word in learned_words:
                if (open_syllable_pattern.search(word['english_word'].lower()) and 
                    word['english_word'].lower() != english_word):
                    similar_words.append(word)
            
            if len(similar_words) >= 2:
                return {
                    'pattern_id': 'open_syllable',
                    'pattern_type': 'syllable',
                    'pattern_value': 'CVCe',
                    'pattern_name': '开音节结构',
                    'match_strength': 0.8,
                    'match_reason': '都是开音节单词（辅音+元音+辅音+e），元音发字母本音',
                    'is_primary': True,
                    'cognitive_level': 'intermediate',
                    'similar_words': similar_words[:5]
                }
        
        return None
    
    def _detect_morphology_patterns(self, english_word: str, learned_words: List[Dict]) -> List[Dict]:
        """检测词根词缀模式"""
        patterns = []
        
        # 常见前缀
        prefixes = {
            're': {'meaning': '重新、再次', 'examples': 'redo, return, repeat'},
            'un': {'meaning': '不、未', 'examples': 'unhappy, unknown, unable'},
            'dis': {'meaning': '不、分离', 'examples': 'disagree, disappear, disconnect'},
            'pre': {'meaning': '预先、提前', 'examples': 'preview, prepare, predict'},
            'mis': {'meaning': '错误、不当', 'examples': 'mistake, misunderstand, mislead'},
            'over': {'meaning': '超过、过度', 'examples': 'overeat, overtime, overcome'},
            'under': {'meaning': '不足、在下', 'examples': 'understand, underwater, underline'},
            'in': {'meaning': '不、向内', 'examples': 'incorrect, inside, include'},
            'im': {'meaning': '不、向内', 'examples': 'impossible, important, improve'},
            'ex': {'meaning': '向外、前', 'examples': 'exit, export, ex-wife'}
        }
        
        # 常见后缀
        suffixes = {
            'er': {'meaning': '做...的人/物', 'examples': 'teacher, worker, computer'},
            'or': {'meaning': '做...的人/物', 'examples': 'doctor, actor, director'},
            'ist': {'meaning': '从事...的人', 'examples': 'artist, scientist, pianist'},
            'tion': {'meaning': '动作/状态', 'examples': 'action, education, nation'},
            'sion': {'meaning': '动作/状态', 'examples': 'decision, revision, television'},
            'ment': {'meaning': '动作/结果', 'examples': 'movement, development, payment'},
            'ness': {'meaning': '状态/性质', 'examples': 'happiness, darkness, kindness'},
            'ful': {'meaning': '充满...的', 'examples': 'beautiful, helpful, careful'},
            'less': {'meaning': '没有...的', 'examples': 'helpless, homeless, careless'},
            'able': {'meaning': '可以...的', 'examples': 'readable, comfortable, valuable'},
            'ible': {'meaning': '可以...的', 'examples': 'possible, terrible, visible'},
            'ly': {'meaning': '...地（副词）', 'examples': 'quickly, slowly, happily'},
            'ing': {'meaning': '正在进行', 'examples': 'running, reading, thinking'},
            'ed': {'meaning': '过去/完成', 'examples': 'worked, played, finished'}
        }
        
        # 检测前缀
        for prefix, info in prefixes.items():
            if english_word.startswith(prefix):
                similar_words = []
                for word in learned_words:
                    if (word['english_word'].lower().startswith(prefix) and 
                        word['english_word'].lower() != english_word):
                        similar_words.append(word)
                
                if len(similar_words) >= 2:
                    patterns.append({
                        'pattern_id': f'prefix_{prefix}',
                        'pattern_type': 'prefix',
                        'pattern_value': prefix,
                        'pattern_name': f'{prefix}-前缀',
                        'match_strength': 0.85,
                        'match_reason': f'都含有前缀{prefix}，表示"{info["meaning"]}"',
                        'is_primary': True,
                        'cognitive_level': 'intermediate',
                        'morphology_info': info,
                        'similar_words': similar_words[:5]
                    })
        
        # 检测后缀
        for suffix, info in suffixes.items():
            if english_word.endswith(suffix):
                similar_words = []
                for word in learned_words:
                    if (word['english_word'].lower().endswith(suffix) and 
                        word['english_word'].lower() != english_word):
                        similar_words.append(word)
                
                if len(similar_words) >= 2:
                    patterns.append({
                        'pattern_id': f'suffix_{suffix}',
                        'pattern_type': 'suffix',
                        'pattern_value': suffix,
                        'pattern_name': f'-{suffix}后缀',
                        'match_strength': 0.85,
                        'match_reason': f'都含有后缀{suffix}，表示"{info["meaning"]}"',
                        'is_primary': True,
                        'cognitive_level': 'intermediate',
                        'morphology_info': info,
                        'similar_words': similar_words[:5]
                    })
        
        # 检测词根（简化版）
        common_roots = {
            'vis': {'meaning': '看', 'examples': 'visit, visual, television'},
            'aud': {'meaning': '听', 'examples': 'audio, audience, audible'},
            'port': {'meaning': '携带', 'examples': 'transport, import, export'},
            'form': {'meaning': '形式', 'examples': 'information, transform, uniform'},
            'struct': {'meaning': '建造', 'examples': 'structure, construct, instruct'},
            'spect': {'meaning': '看', 'examples': 'respect, inspect, spectacle'},
            'dict': {'meaning': '说', 'examples': 'dictionary, predict, dictate'}
        }
        
        for root, info in common_roots.items():
            if root in english_word:
                similar_words = []
                for word in learned_words:
                    if root in word['english_word'].lower() and word['english_word'].lower() != english_word:
                        similar_words.append(word)
                
                if len(similar_words) >= 2:
                    patterns.append({
                        'pattern_id': f'root_{root}',
                        'pattern_type': 'root',
                        'pattern_value': root,
                        'pattern_name': f'{root}词根',
                        'match_strength': 0.9,
                        'match_reason': f'都含有词根{root}，表示"{info["meaning"]}"的概念',
                        'is_primary': True,
                        'cognitive_level': 'advanced',
                        'morphology_info': info,
                        'similar_words': similar_words[:5]
                    })
        
        return patterns
    
    
    def _detect_collocation_patterns(self, english_word: str, learned_words: List[Dict]) -> List[Dict]:
        """检测搭配模式（简化版）"""
        patterns = []
        
        # 常见搭配类型
        collocation_types = {
            'time_words': {
                'keywords': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 
                           'january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 
                           'september', 'october', 'november', 'december', 'morning', 'afternoon', 
                           'evening', 'night', 'today', 'yesterday', 'tomorrow'],
                'name': '时间表达',
                'reason': '都是描述时间的词汇，常在日常对话中一起使用'
            },
            'school_words': {
                'keywords': ['school', 'teacher', 'student', 'class', 'lesson', 'homework', 'book', 
                           'pen', 'pencil', 'desk', 'chair', 'blackboard', 'classroom'],
                'name': '学校场景',
                'reason': '都是学校相关词汇，在描述学习场景时经常搭配使用'
            },
            'family_words': {
                'keywords': ['father', 'mother', 'brother', 'sister', 'son', 'daughter', 
                           'grandfather', 'grandmother', 'uncle', 'aunt'],
                'name': '家庭成员',
                'reason': '都是家庭成员词汇，在介绍家庭时常一起使用'
            }
        }
        
        # 检测搭配模式
        for coll_type, info in collocation_types.items():
            if english_word.lower() in info['keywords']:
                similar_words = []
                for word in learned_words:
                    if (word['english_word'].lower() in info['keywords'] and 
                        word['english_word'].lower() != english_word):
                        similar_words.append(word)
                
                if len(similar_words) >= 3:
                    patterns.append({
                        'pattern_id': f'collocation_{coll_type}',
                        'pattern_type': 'collocation',
                        'pattern_value': coll_type,
                        'pattern_name': info['name'],
                        'match_strength': 0.8,
                        'match_reason': info['reason'],
                        'is_primary': False,
                        'cognitive_level': 'advanced',
                        'similar_words': similar_words[:5]
                    })
        
        return patterns
    
    def get_pattern_learning_suggestions(self, user_id: int, pattern_type: str = None) -> List[Dict]:
        """获取基于pattern的学习建议
        
        Args:
            user_id: 用户ID
            pattern_type: 指定pattern类型，None表示所有类型
            
        Returns:
            学习建议列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 构建查询条件
            type_condition = ""
            params = [user_id]
            if pattern_type:
                type_condition = "AND wp.pattern_type = ?"
                params.append(pattern_type)
            
            # 查询用户在各个pattern上的学习情况
            cursor.execute(f"""
                SELECT 
                    wp.pattern_type,
                    wp.pattern_value,
                    wp.pattern_name,
                    COUNT(DISTINCT wpr.word_id) as pattern_total_words,
                    COUNT(DISTINCT CASE WHEN uw.user_id IS NOT NULL THEN wpr.word_id END) as learned_words,
                    AVG(CASE WHEN uw.user_id IS NOT NULL THEN uw.proficiency END) as avg_proficiency,
                    wp.word_count as expected_count
                FROM word_patterns wp
                JOIN word_pattern_relations wpr ON wp.id = wpr.pattern_id
                LEFT JOIN user_word uw ON wpr.word_id = uw.word_id AND uw.user_id = ?
                WHERE wp.is_active = 1 {type_condition}
                GROUP BY wp.id, wp.pattern_type, wp.pattern_value, wp.pattern_name, wp.word_count
                HAVING learned_words > 0  -- 至少学过一个单词
                ORDER BY learned_words DESC, avg_proficiency ASC
            """, params)
            
            suggestions = []
            for row in cursor.fetchall():
                pattern_type, pattern_value, pattern_name, pattern_total, learned, avg_prof, expected = row
                
                completion_rate = learned / expected if expected > 0 else 0
                avg_proficiency = avg_prof if avg_prof else 0
                
                suggestion = {
                    'pattern_type': pattern_type,
                    'pattern_value': pattern_value,
                    'pattern_name': pattern_name,
                    'total_words_in_pattern': expected,
                    'learned_words': learned,
                    'completion_rate': round(completion_rate, 2),
                    'avg_proficiency': round(avg_proficiency, 1),
                    'suggestion_type': self._determine_suggestion_type(completion_rate, avg_proficiency),
                    'next_words': self._get_next_words_to_learn(user_id, pattern_value, cursor)
                }
                suggestions.append(suggestion)
            
            conn.close()
            return suggestions
            
        except Exception as e:
            logger.error(f"获取学习建议失败: {e}")
            return []
    
    def get_word_pattern_context(self, word_id: int) -> Dict:
        """获取单词的pattern上下文信息，用于学习界面展示
        
        Args:
            word_id: 单词ID
            
        Returns:
            包含pattern信息和示例单词的字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取单词的主要patterns
            cursor.execute("""
                SELECT wp.pattern_type, wp.pattern_value, wp.pattern_name, 
                       wpr.match_reason, wpr.is_primary
                FROM word_pattern_relations wpr
                JOIN word_patterns wp ON wpr.pattern_id = wp.id
                WHERE wpr.word_id = ? AND wp.is_active = 1
                ORDER BY wpr.is_primary DESC, wpr.match_strength DESC
                LIMIT 3
            """, (word_id,))
            
            patterns = []
            for row in cursor.fetchall():
                pattern_type, pattern_value, pattern_name, match_reason, is_primary = row
                
                # 获取同pattern的其他单词示例
                cursor.execute("""
                    SELECT w.english_word
                    FROM word_pattern_relations wpr2
                    JOIN word w ON wpr2.word_id = w.id
                    JOIN word_patterns wp2 ON wpr2.pattern_id = wp2.id
                    WHERE wp2.pattern_value = ? AND wpr2.word_id != ?
                    ORDER BY wpr2.match_strength DESC
                    LIMIT 5
                """, (pattern_value, word_id))
                
                example_words = [row[0] for row in cursor.fetchall()]
                
                pattern_info = {
                    'pattern_type': pattern_type,
                    'pattern_value': pattern_value,
                    'pattern_name': pattern_name,
                    'match_reason': match_reason,
                    'is_primary': bool(is_primary),
                    'example_words': example_words
                }
                patterns.append(pattern_info)
            
            conn.close()
            
            return {
                'word_id': word_id,
                'patterns': patterns,
                'total_patterns': len(patterns)
            }
            
        except Exception as e:
            logger.error(f"获取单词pattern上下文失败: {e}")
            return {'word_id': word_id, 'patterns': [], 'total_patterns': 0}
    
    def _get_word_patterns(self, word_id: int) -> List[Dict]:
        """获取单词的pattern信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT wp.id, wp.pattern_type, wp.pattern_value, wp.pattern_name,
                       wpr.match_strength, wpr.match_reason, wpr.is_primary
                FROM word_pattern_relations wpr
                JOIN word_patterns wp ON wpr.pattern_id = wp.id
                WHERE wpr.word_id = ? AND wp.is_active = 1
                ORDER BY wpr.is_primary DESC, wpr.match_strength DESC
            """, (word_id,))
            
            patterns = []
            for row in cursor.fetchall():
                patterns.append({
                    'pattern_id': row[0],
                    'pattern_type': row[1],
                    'pattern_value': row[2],
                    'pattern_name': row[3],
                    'match_strength': row[4],
                    'match_reason': row[5],
                    'is_primary': bool(row[6])
                })
            
            conn.close()
            return patterns
            
        except Exception as e:
            logger.error(f"获取单词patterns失败: {e}")
            return []
    
    def _get_user_learned_words(self, user_id: int, exclude_new_words: bool) -> List[Dict]:
        """获取用户已学习的单词"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 构建查询条件
            status_condition = ""
            if exclude_new_words:
                status_condition = "AND uw.status != 'new'"
            
            cursor.execute(f"""
                SELECT w.id, w.english_word, w.chinese_meaning,
                       uw.status, uw.proficiency
                FROM user_word uw
                JOIN word w ON uw.word_id = w.id
                WHERE uw.user_id = ? {status_condition}
            """, (user_id,))
            
            words = []
            for row in cursor.fetchall():
                words.append({
                    'word_id': row[0],
                    'english_word': row[1],
                    'chinese_meaning': row[2],
                    'status': row[3],
                    'proficiency': row[4] or 0.0
                })
            
            conn.close()
            return words
            
        except Exception as e:
            logger.error(f"获取用户已学单词失败: {e}")
            return []
    
    def _find_similar_words_by_pattern(self, pattern: Dict, learned_words: List[Dict], 
                                     exclude_word_id: int) -> List[SimilarWord]:
        """根据pattern查找相似单词"""
        try:
            # 检查是否为动态pattern（已有similar_words字段）
            if 'similar_words' in pattern:
                # 动态pattern直接使用预计算的相似单词
                similar_words = []
                learned_word_map = {w['word_id']: w for w in learned_words}
                
                for word_info in pattern['similar_words']:
                    word_id = word_info['word_id']
                    if word_id != exclude_word_id and word_id in learned_word_map:
                        learned_info = learned_word_map[word_id]
                        
                        similar_word = SimilarWord(
                            word_id=word_id,
                            english_word=word_info['english_word'],
                            chinese_meaning=word_info['chinese_meaning'],
                            similarity_score=pattern['match_strength'],
                            shared_patterns=[pattern],
                            learning_status=learned_info['status'],
                            proficiency=learned_info['proficiency']
                        )
                        similar_words.append(similar_word)
                
                return similar_words
            
            # 静态pattern从数据库查询
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找具有相同pattern的单词
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning,
                       wpr.match_strength, wpr.match_reason
                FROM word_pattern_relations wpr
                JOIN word w ON wpr.word_id = w.id
                WHERE wpr.pattern_id = ? AND w.id != ?
                ORDER BY wpr.match_strength DESC
            """, (pattern['pattern_id'], exclude_word_id))
            
            pattern_words = {row[0]: row for row in cursor.fetchall()}
            conn.close()
            
            # 只返回用户已学习的单词
            similar_words = []
            learned_word_map = {w['word_id']: w for w in learned_words}
            
            for word_id, (_, english_word, chinese_meaning, match_strength, match_reason) in pattern_words.items():
                if word_id in learned_word_map:
                    learned_info = learned_word_map[word_id]
                    
                    similar_word = SimilarWord(
                        word_id=word_id,
                        english_word=english_word,
                        chinese_meaning=chinese_meaning,
                        similarity_score=match_strength,
                        shared_patterns=[pattern],
                        learning_status=learned_info['status'],
                        proficiency=learned_info['proficiency']
                    )
                    similar_words.append(similar_word)
            
            # 按相似度排序
            similar_words.sort(key=lambda w: w.similarity_score, reverse=True)
            return similar_words
            
        except Exception as e:
            logger.error(f"查找相似单词失败: {e}")
            return []
    
    def _generate_recommendation_reason(self, pattern: Dict) -> str:
        """生成具有教育价值的推荐理由"""
        pattern_type = pattern['pattern_type']
        pattern_value = pattern['pattern_value']
        pattern_name = pattern['pattern_name']
        
        if pattern_type == 'letter_combo':
            return self._generate_letter_combo_reason(pattern_value, pattern_name)
        elif pattern_type == 'theme':
            return self._generate_theme_reason(pattern_value, pattern_name)
        elif pattern_type == 'prefix':
            return self._generate_prefix_reason(pattern_value, pattern_name)
        elif pattern_type == 'suffix':
            return self._generate_suffix_reason(pattern_value, pattern_name)
        elif pattern_type == 'semantic':
            return self._generate_semantic_reason(pattern_value, pattern_name)
        elif pattern_type == 'rhyme':
            return self._generate_rhyme_reason(pattern_value, pattern_name)
        elif pattern_type == 'phonetic':
            return self._generate_phonetic_reason(pattern_value, pattern_name, pattern)
        elif pattern_type == 'silent_letter':
            return self._generate_silent_letter_reason(pattern_value, pattern_name, pattern)
        elif pattern_type == 'syllable':
            return self._generate_syllable_reason(pattern_value, pattern_name)
        elif pattern_type == 'root':
            return self._generate_root_reason(pattern_value, pattern_name, pattern)
        elif pattern_type == 'collocation':
            return self._generate_collocation_reason(pattern_value, pattern_name, pattern)
        elif pattern_type == 'synonym':
            return self._generate_synonym_reason(pattern_value, pattern_name)
        elif pattern_type == 'antonym':
            return self._generate_antonym_reason(pattern_value, pattern_name)
        elif pattern_type == 'adjective_forms':
            return self._generate_adjective_forms_reason(pattern_value, pattern_name, pattern)
        elif pattern_type == 'plural_forms':
            return self._generate_plural_forms_reason(pattern_value, pattern_name, pattern)
        else:
            return f"这些单词在{pattern_name}方面有共同特征，一起学习有助于加深记忆"
    
    def _generate_letter_combo_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成字母组合的详细推荐理由"""
        combo_insights = {
            'er': "💡 'er'组合通常表示'人'(如teacher老师)或'比较级'(如bigger更大)。掌握这个规律，你会发现很多职业词汇都有这个结尾！",
            'ar': "🎯 'ar'组合常出现在核心词汇中，发音稳定[ɑr]。这些单词多为基础词汇，是英语学习的重要基石。",
            'th': "🔤 'th'组合是英语特有的发音[θ]或[ð]，练习这些单词能显著改善你的英语发音！",
            'or': "⭐ 'or'组合多表示'状态'或'人'(如doctor医生)，掌握后能快速识别职业和状态词汇。",
            'ea': "🎵 'ea'组合发音多样，有[i:]、[e]等。通过这组单词练习，能掌握英语发音的复杂性。",
            'ou': "🔊 'ou'组合发音丰富[aʊ]、[ʌ]等，这些都是日常高频词汇，值得重点掌握。",
            'ch': "📢 'ch'组合发音清晰[tʃ]，多为具体名词，通过联想记忆效果更佳。",
            'ur': "🎪 'ur'组合发音为[ɜr]，这些词汇包含很多有趣的概念，容易形成深刻印象。",
            'oo': "👀 'oo'组合有长音[u:]和短音[ʊ]，掌握发音规律能提升整体语感。",
            'ai': "🌟 'ai'组合发音[eɪ]，多为积极正面词汇，学习时容易产生愉快联想。",
            'ir': "🔄 'ir'组合发音[ɜr]，包含很多动作和状态词，有助于表达能力提升。",
            'ly': "⚡ 'ly'后缀将形容词变为副词，掌握这个规律能快速扩展词汇量！"
        }
        
        return combo_insights.get(pattern_value, f"'{pattern_value}'组合的单词有共同的拼写规律，集中学习能提高拼写准确性。")
    
    def _generate_theme_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成主题分类的详细推荐理由"""
        theme_insights = {
            'time': "⏰ 时间词汇是日常交流的核心，掌握这些词能让你准确表达时间概念。建议按'年-月-日-时'的顺序记忆。",
            'colors': "🎨 颜色词汇具有强烈视觉联想，通过想象具体场景(如red apple红苹果)能加深记忆。",
            'food': "🍎 食物词汇贴近生活，学习时可结合味觉记忆，想象食物的味道和外观。",
            'clothes': "👕 服装词汇实用性强，学习时可以联想自己的衣柜，或者想象穿着场景。",
            'family': "👨‍👩‍👧‍👦 家庭成员词汇包含情感因素，容易形成长期记忆。可按关系远近分层记忆。",
            'animals': "🐱 动物词汇形象生动，学习时可结合动物的特征和叫声进行联想记忆。",
            'body': "👤 身体部位词汇可通过动作记忆法学习，边说边指对应部位效果更佳。",
            'nature': "🌳 自然词汇富有诗意，学习时可想象户外场景，结合感官体验记忆。"
        }
        
        return theme_insights.get(pattern_value, f"{pattern_name}类词汇有共同的语义特征，主题学习法能建立更强的记忆网络。")
    
    def _generate_prefix_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成前缀的详细推荐理由"""
        prefix_insights = {
            're': "🔄 're-'前缀表示'重新、再次'，掌握这个规律后，遇到新的're-'单词就能猜出大概意思！",
            'un': "❌ 'un-'前缀表示'不、未'，是构成反义词的重要方法。学会后能成倍扩展词汇量。",
            'pre': "⏪ 'pre-'前缀表示'预先、提前'，多用于正式场合的词汇。",
            'dis': "🚫 'dis-'前缀表示'不、分离'，理解词根变化规律很重要。",
            'mis': "⚠️ 'mis-'前缀表示'错误、不当'，掌握后能精确理解词汇含义。"
        }
        
        return prefix_insights.get(pattern_value, f"'{pattern_value}-'前缀有特定含义，掌握词缀规律是高效词汇学习的关键策略。")
    
    def _generate_suffix_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成后缀的详细推荐理由"""
        suffix_insights = {
            'ing': "🏃 '-ing'后缀表示'正在进行'，是英语时态的重要标志。掌握动词ing形式能提升语法准确性。",
            'ed': "⏮️ '-ed'后缀表示'过去时态'，规律动词都遵循这个变化。是英语语法的基础。",
            'ly': "🎯 '-ly'后缀将形容词变副词，是词性转换的重要规律。一个词缀能成倍扩展表达能力！",
            'tion': "🏢 '-tion'后缀多表示'动作结果'，常见于正式词汇中。",
            'ness': "💭 '-ness'后缀表示'状态、性质'，能将形容词变为抽象名词。"
        }
        
        return suffix_insights.get(pattern_value, f"'-{pattern_value}'后缀有固定语法功能，理解构词规律能举一反三。")
    
    def _generate_semantic_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成语义关联的推荐理由"""
        semantic_insights = {
            '动作': "🏃 动作类词汇学习建议：可以通过做动作来记忆单词，身体记忆比纯视觉记忆更持久。想象自己在做这些动作！",
            '情感': "💝 情感类词汇学习技巧：将情感词汇与个人经历结合，想起让你有相同感受的具体场景。情感记忆最深刻！",
            '方向': "🧭 方向类词汇记忆法：可以结合手势和空间想象，在脑海中构建一个3D方向图。边说边指方向效果更佳！",
            '颜色': "🌈 颜色词汇视觉记忆法：每个颜色都与具体物品关联(如red-apple)，利用大脑的视觉记忆优势。",
            '数量': "🔢 数量词汇规律学习：掌握基础数字后，其他数量概念就容易理解了。数字是逻辑思维的基础！",
            '学习': "📚 学习类词汇实用性强：这些都是描述学习过程的核心词汇，掌握后能更好地表达学习经历和感受。",
            '工作': "💼 工作类词汇职场必备：现代生活中使用频率很高的词汇类别，对职业发展很有帮助。"
        }
        
        return semantic_insights.get(pattern_value, f"🎯 {pattern_name}有共同的概念核心，主题学习法能建立强联想网络，提高记忆效率。")
    
    def _generate_rhyme_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成音韵模式的推荐理由"""
        return f"🎵 '{pattern_value}'尾音的单词有相似的音韵结构，通过押韵记忆法能显著提高记忆效果。可以尝试编成小诗或口诀来记忆！"
    
    def _generate_phonetic_reason(self, pattern_value: str, pattern_name: str, pattern: Dict) -> str:
        """生成发音规律的推荐理由"""
        phonetic_info = pattern.get('phonetic_info', {})
        sound = phonetic_info.get('sound', '')
        desc = phonetic_info.get('desc', '')
        examples = phonetic_info.get('examples', '')
        similar_words = pattern.get('similar_words', [])
        
        # 特殊处理重要的元音组合
        if pattern_value == 'au':
            return f"""🔊 【au组合】发音/ɔː/（奥）- 与caught的augh同音！
            
📝 核心规律：au = augh（去掉gh）= /ɔː/
🎯 记忆技巧：August(八月)的autumn(秋天)，daughter(女儿)被taught(教导)
📚 你的已学单词：{', '.join([w['english_word'] for w in similar_words[:8]])}
🔥 重要发现：au和augh发音完全相同，只是拼写不同！"""
        
        elif pattern_value == 'ou':
            return f"""🔊 【ou组合】英语中最复杂的元音组合之一
            
📝 主要发音：
1. /aʊ/（奥）：out, house, about, loud
2. /ɔː/（奥）：bought, thought（-ought结尾）
3. /ʌ/（啊）：trouble, young, touch
🎯 记忆规律：-ought结尾都发/ɔː/，其他多数发/aʊ/
📚 你的已学单词：{', '.join([w['english_word'] for w in similar_words[:8]])}"""
        
        elif pattern_value in ['igh', 'eigh']:
            # 这些在不发音字母中已处理，这里补充
            return f"""🔊 【{pattern_value}组合】{desc}
            
📝 发音规律：{pattern_value} = {sound}
🎯 相关组合：igh, eigh, aigh都发相同音/aɪ/
📚 你的已学单词：{', '.join([w['english_word'] for w in similar_words[:8]])}
💡 技巧：这些组合中的gh都不发音！"""
        
        # 通用处理
        return f"""🔊 【{pattern_value}组合】{desc}
        
📝 发音规律：{pattern_value} = {sound}
📚 参考单词：{examples}
📚 你的已学单词：{', '.join([w['english_word'] for w in similar_words[:6]])}
💡 掌握这个规律，看到{pattern_value}就知道怎么读！"""
    
    def _generate_silent_letter_reason(self, pattern_value: str, pattern_name: str, pattern: Dict) -> str:
        """生成不发音字母的推荐理由"""
        match_reason = pattern.get('match_reason', '')
        similar_words = pattern.get('similar_words', [])
        
        # 特殊处理gh组合
        if 'gh' in pattern_value:
            if pattern_value == 'igh':
                return f"""🔇 【igh组合】gh完全不发音，i发/aɪ/音（爱）
                
📝 发音规律：igh = /aɪ/（把gh当作隐形）
🎯 记忆口诀："igh看见光(light)，gh躲起来不发声"
📚 相似单词：{', '.join([w['english_word'] for w in similar_words[:6]])}
💡 拼写技巧：听到/aɪ/音的单词，很可能是igh结尾！"""
            
            elif pattern_value == 'augh':
                return f"""🔇 【augh组合】gh完全不发音，au发/ɔː/音（奥）
                
📝 发音规律：augh = /ɔː/（gh是装饰）
🎯 记忆技巧："女儿(daughter)被抓住(caught)，gh吓得不敢出声"
📚 相似单词：{', '.join([w['english_word'] for w in similar_words[:6]])}
⚠️ 易错提醒：不要写成aut或aught！"""
            
            elif pattern_value == 'ough':
                return f"""🔇 【ough组合】最复杂的组合，gh不发音，但ou有多种发音
                
📝 发音规律：
- thought类：ough = /ɔː/（奥）- thought, bought, brought
- through类：ough = /uː/（乌）- through
- though类：ough = /əʊ/（欧）- though, although
🎯 记忆分组：按发音相同的词一起记
📚 相似单词：{', '.join([w['english_word'] for w in similar_words[:8]])}
💡 核心技巧：ough中的gh永远不发音！"""
            
            elif pattern_value == 'eigh':
                return f"""🔇 【eigh组合】gh完全不发音，ei发/eɪ/音（诶）
                
📝 发音规律：eigh = /eɪ/（把gh忽略）
🎯 记忆技巧："八(eight)个邻居(neighbor)都很重(weight)"
📚 相似单词：{', '.join([w['english_word'] for w in similar_words[:6]])}
🔥 重要规律：eigh的发音和ay、ai完全相同！"""
        
        # 其他不发音字母的处理
        return f"""🔇 {match_reason}
        
📚 相似单词：{', '.join([w['english_word'] for w in similar_words[:6]])}
💡 记住这个规律，避免拼写错误！"""
    
    def _generate_syllable_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成音节结构的推荐理由"""
        if pattern_value == 'CVCe':
            return "✨ 这些都是开音节单词（辅音+元音+辅音+e结构），元音字母发其字母本身的音。例如：make中的a发/eɪ/，like中的i发/aɪ/。掌握这个规律能帮你准确发音！"
        return f"📖 {pattern_name}的单词有相似的音节结构，一起学习能帮助你理解英语的音节规律。"
    
    def _generate_root_reason(self, pattern_value: str, pattern_name: str, pattern: Dict) -> str:
        """生成词根的推荐理由"""
        morphology_info = pattern.get('morphology_info', {})
        meaning = morphology_info.get('meaning', '')
        examples = morphology_info.get('examples', '')
        
        return f"🌳 这些单词都含有词根'{pattern_value}'，表示'{meaning}'的核心概念。理解词根是快速扩展词汇量的秘诀！更多例子：{examples}"
    
    def _generate_collocation_reason(self, pattern_value: str, pattern_name: str, pattern: Dict) -> str:
        """生成搭配模式的推荐理由"""
        match_reason = pattern.get('match_reason', '')
        return f"🎯 {match_reason} 场景化记忆是最有效的方法之一，把相关词汇放在实际场景中一起记忆，效果会更好！"
    
    def _determine_suggestion_type(self, completion_rate: float, avg_proficiency: float) -> str:
        """确定学习建议类型"""
        if completion_rate < 0.3:
            return "explore"  # 探索更多同类单词
        elif completion_rate > 0.7 and avg_proficiency > 70:
            return "mastered"  # 已掌握该pattern
        elif avg_proficiency < 50:
            return "review"  # 需要复习
        else:
            return "continue"  # 继续学习
    
    def _get_next_words_to_learn(self, user_id: int, pattern_value: str, cursor) -> List[Dict]:
        """获取pattern中建议学习的下几个单词"""
        cursor.execute("""
            SELECT w.id, w.english_word, w.chinese_meaning
            FROM word_pattern_relations wpr
            JOIN word w ON wpr.word_id = w.id
            JOIN word_patterns wp ON wpr.pattern_id = wp.id
            LEFT JOIN user_word uw ON w.id = uw.word_id AND uw.user_id = ?
            WHERE wp.pattern_value = ? AND uw.user_id IS NULL
            ORDER BY wpr.match_strength DESC
            LIMIT 3
        """, (user_id, pattern_value))
        
        next_words = []
        for row in cursor.fetchall():
            next_words.append({
                'word_id': row[0],
                'english_word': row[1],
                'chinese_meaning': row[2]
            })
        
        return next_words
    
    def record_pattern_interaction(self, user_id: int, pattern_id: int, word_id: int,
                                 interaction_type: str, session_id: str = None) -> bool:
        """记录用户与pattern的交互"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO user_pattern_interactions 
                (user_id, pattern_id, word_id, interaction_type, session_id)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, pattern_id, word_id, interaction_type, session_id))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"记录pattern交互失败: {e}")
            return False
    
    def get_pattern_effectiveness(self, pattern_id: int) -> Dict:
        """获取pattern的学习效果统计"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计interaction数据
            cursor.execute("""
                SELECT 
                    interaction_type,
                    COUNT(*) as count
                FROM user_pattern_interactions
                WHERE pattern_id = ?
                GROUP BY interaction_type
            """, (pattern_id,))
            
            interactions = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 计算效果指标
            total_views = interactions.get('view', 0)
            helpful_count = interactions.get('helpful', 0)
            not_helpful_count = interactions.get('not_helpful', 0)
            
            effectiveness = 0.0
            if total_views > 0:
                effectiveness = (helpful_count - not_helpful_count) / total_views
            
            conn.close()
            
            return {
                'pattern_id': pattern_id,
                'total_views': total_views,
                'helpful_count': helpful_count,
                'not_helpful_count': not_helpful_count,
                'effectiveness_score': round(effectiveness, 2),
                'interactions': interactions
            }
            
        except Exception as e:
            logger.error(f"获取pattern效果统计失败: {e}")
            return {}
    
    def _detect_synonym_antonym_patterns(self, target_word: Dict, learned_words: List[Dict]) -> List[Dict]:
        """检测同义词反义词patterns"""
        patterns = []
        english_word = target_word['english_word'].lower()
        chinese_meaning = target_word['chinese_meaning']
        
        # 同义词反义词词典（简化版）
        synonym_antonym_dict = {
            # 大小对比
            'big': {'synonyms': ['large', 'huge', 'giant'], 'antonyms': ['small', 'little', 'tiny']},
            'small': {'synonyms': ['little', 'tiny', 'mini'], 'antonyms': ['big', 'large', 'huge']},
            'large': {'synonyms': ['big', 'huge', 'giant'], 'antonyms': ['small', 'little', 'tiny']},
            
            # 好坏对比
            'good': {'synonyms': ['great', 'excellent', 'wonderful', 'nice'], 'antonyms': ['bad', 'terrible', 'awful']},
            'bad': {'synonyms': ['terrible', 'awful', 'horrible'], 'antonyms': ['good', 'great', 'excellent']},
            'happy': {'synonyms': ['glad', 'joyful', 'pleased'], 'antonyms': ['sad', 'unhappy', 'upset']},
            'sad': {'synonyms': ['unhappy', 'upset', 'blue'], 'antonyms': ['happy', 'glad', 'joyful']},
            
            # 快慢对比
            'fast': {'synonyms': ['quick', 'rapid', 'speedy'], 'antonyms': ['slow', 'sluggish']},
            'slow': {'synonyms': ['sluggish', 'gradual'], 'antonyms': ['fast', 'quick', 'rapid']},
            'quick': {'synonyms': ['fast', 'rapid', 'speedy'], 'antonyms': ['slow', 'sluggish']},
            
            # 多少对比
            'many': {'synonyms': ['lots', 'numerous', 'plenty'], 'antonyms': ['few', 'little']},
            'few': {'synonyms': ['little', 'some'], 'antonyms': ['many', 'lots', 'numerous']},
            
            # 冷热对比
            'hot': {'synonyms': ['warm', 'burning'], 'antonyms': ['cold', 'cool', 'freezing']},
            'cold': {'synonyms': ['cool', 'freezing', 'chilly'], 'antonyms': ['hot', 'warm']},
            'warm': {'synonyms': ['hot', 'cozy'], 'antonyms': ['cold', 'cool']},
            
            # 新旧对比
            'new': {'synonyms': ['fresh', 'modern', 'recent'], 'antonyms': ['old', 'ancient', 'worn']},
            'old': {'synonyms': ['ancient', 'aged', 'worn'], 'antonyms': ['new', 'fresh', 'modern']},
            
            # 高低对比
            'high': {'synonyms': ['tall', 'elevated'], 'antonyms': ['low', 'short']},
            'low': {'synonyms': ['short', 'shallow'], 'antonyms': ['high', 'tall']},
            'tall': {'synonyms': ['high', 'elevated'], 'antonyms': ['short', 'low']},
            'short': {'synonyms': ['low', 'brief'], 'antonyms': ['tall', 'high', 'long']},
            
            # 长短对比
            'long': {'synonyms': ['lengthy', 'extended'], 'antonyms': ['short', 'brief']},
            'brief': {'synonyms': ['short', 'quick'], 'antonyms': ['long', 'lengthy']},
            
            # 厚薄/胖瘦对比
            'thick': {'synonyms': ['fat', 'heavy'], 'antonyms': ['thin', 'slim']},
            'thin': {'synonyms': ['slim', 'skinny'], 'antonyms': ['thick', 'fat']},
            'fat': {'synonyms': ['thick', 'heavy'], 'antonyms': ['thin', 'slim']},
            
            # 容易困难对比
            'easy': {'synonyms': ['simple', 'effortless'], 'antonyms': ['hard', 'difficult']},
            'hard': {'synonyms': ['difficult', 'tough'], 'antonyms': ['easy', 'simple']},
            'difficult': {'synonyms': ['hard', 'tough'], 'antonyms': ['easy', 'simple']},
            'simple': {'synonyms': ['easy', 'basic'], 'antonyms': ['hard', 'difficult', 'complex']},
            
            # 动作相关
            'run': {'synonyms': ['jog', 'sprint'], 'antonyms': ['walk', 'stop']},
            'walk': {'synonyms': ['stroll', 'march'], 'antonyms': ['run', 'sprint']},
            'start': {'synonyms': ['begin', 'commence'], 'antonyms': ['stop', 'end', 'finish']},
            'stop': {'synonyms': ['halt', 'cease'], 'antonyms': ['start', 'begin', 'continue']},
            'open': {'synonyms': ['unlock', 'unfold'], 'antonyms': ['close', 'shut']},
            'close': {'synonyms': ['shut', 'seal'], 'antonyms': ['open', 'unlock']},
            
            # 位置相关
            'up': {'synonyms': ['above', 'high'], 'antonyms': ['down', 'below']},
            'down': {'synonyms': ['below', 'under'], 'antonyms': ['up', 'above']},
            'left': {'synonyms': [], 'antonyms': ['right']},
            'right': {'synonyms': [], 'antonyms': ['left']},
            'inside': {'synonyms': ['within', 'interior'], 'antonyms': ['outside', 'exterior']},
            'outside': {'synonyms': ['exterior', 'outdoors'], 'antonyms': ['inside', 'interior']},
            
            # 时间相关
            'early': {'synonyms': ['soon', 'ahead'], 'antonyms': ['late', 'delayed']},
            'late': {'synonyms': ['delayed', 'tardy'], 'antonyms': ['early', 'soon']},
            'before': {'synonyms': ['prior', 'earlier'], 'antonyms': ['after', 'later']},
            'after': {'synonyms': ['later', 'following'], 'antonyms': ['before', 'prior']}
        }
        
        # 检查目标单词是否在词典中
        word_data = synonym_antonym_dict.get(english_word)
        if not word_data:
            return patterns
        
        # 查找同义词
        synonyms_found = []
        for word in learned_words:
            if word['english_word'].lower() in word_data['synonyms']:
                synonyms_found.append(word)
        
        if synonyms_found:
            patterns.append({
                'pattern_id': f'synonym_{english_word}',
                'pattern_type': 'synonym',
                'pattern_value': english_word,
                'pattern_name': f'{english_word}的同义词',
                'match_strength': 0.9,
                'match_reason': f'这些词汇与{english_word}意思相近，都表达相似的概念',
                'is_primary': True,
                'cognitive_level': 'intermediate',
                'similar_words': synonyms_found[:5]
            })
        
        # 查找反义词
        antonyms_found = []
        for word in learned_words:
            if word['english_word'].lower() in word_data['antonyms']:
                antonyms_found.append(word)
        
        if antonyms_found:
            patterns.append({
                'pattern_id': f'antonym_{english_word}',
                'pattern_type': 'antonym',
                'pattern_value': english_word,
                'pattern_name': f'{english_word}的反义词',
                'match_strength': 0.9,
                'match_reason': f'这些词汇与{english_word}意思相反，形成对比概念',
                'is_primary': True,
                'cognitive_level': 'intermediate',
                'similar_words': antonyms_found[:5]
            })
        
        return patterns
    
    def _detect_adjective_patterns(self, english_word: str, learned_words: List[Dict]) -> List[Dict]:
        """检测形容词比较级最高级patterns"""
        patterns = []
        
        # 形容词变化规则词典
        adjective_forms = {
            # 规则变化
            'big': {'comparative': 'bigger', 'superlative': 'biggest'},
            'small': {'comparative': 'smaller', 'superlative': 'smallest'},
            'tall': {'comparative': 'taller', 'superlative': 'tallest'},
            'short': {'comparative': 'shorter', 'superlative': 'shortest'},
            'long': {'comparative': 'longer', 'superlative': 'longest'},
            'fast': {'comparative': 'faster', 'superlative': 'fastest'},
            'slow': {'comparative': 'slower', 'superlative': 'slowest'},
            'new': {'comparative': 'newer', 'superlative': 'newest'},
            'old': {'comparative': 'older', 'superlative': 'oldest'},
            'young': {'comparative': 'younger', 'superlative': 'youngest'},
            'clean': {'comparative': 'cleaner', 'superlative': 'cleanest'},
            'dirty': {'comparative': 'dirtier', 'superlative': 'dirtiest'},
            'heavy': {'comparative': 'heavier', 'superlative': 'heaviest'},
            'light': {'comparative': 'lighter', 'superlative': 'lightest'},
            'hot': {'comparative': 'hotter', 'superlative': 'hottest'},
            'cold': {'comparative': 'colder', 'superlative': 'coldest'},
            'warm': {'comparative': 'warmer', 'superlative': 'warmest'},
            'cool': {'comparative': 'cooler', 'superlative': 'coolest'},
            'high': {'comparative': 'higher', 'superlative': 'highest'},
            'low': {'comparative': 'lower', 'superlative': 'lowest'},
            'thick': {'comparative': 'thicker', 'superlative': 'thickest'},
            'thin': {'comparative': 'thinner', 'superlative': 'thinnest'},
            'wide': {'comparative': 'wider', 'superlative': 'widest'},
            'narrow': {'comparative': 'narrower', 'superlative': 'narrowest'},
            'loud': {'comparative': 'louder', 'superlative': 'loudest'},
            'quiet': {'comparative': 'quieter', 'superlative': 'quietest'},
            'bright': {'comparative': 'brighter', 'superlative': 'brightest'},
            'dark': {'comparative': 'darker', 'superlative': 'darkest'},
            'rich': {'comparative': 'richer', 'superlative': 'richest'},
            'poor': {'comparative': 'poorer', 'superlative': 'poorest'},
            'strong': {'comparative': 'stronger', 'superlative': 'strongest'},
            'weak': {'comparative': 'weaker', 'superlative': 'weakest'},
            'hard': {'comparative': 'harder', 'superlative': 'hardest'},
            'soft': {'comparative': 'softer', 'superlative': 'softest'},
            'sharp': {'comparative': 'sharper', 'superlative': 'sharpest'},
            'safe': {'comparative': 'safer', 'superlative': 'safest'},
            'dangerous': {'comparative': 'more dangerous', 'superlative': 'most dangerous'},
            
            # 不规则变化
            'good': {'comparative': 'better', 'superlative': 'best'},
            'bad': {'comparative': 'worse', 'superlative': 'worst'},
            'many': {'comparative': 'more', 'superlative': 'most'},
            'much': {'comparative': 'more', 'superlative': 'most'},
            'little': {'comparative': 'less', 'superlative': 'least'},
            'far': {'comparative': 'farther', 'superlative': 'farthest'},
            
            # 双音节词more/most形式
            'beautiful': {'comparative': 'more beautiful', 'superlative': 'most beautiful'},
            'careful': {'comparative': 'more careful', 'superlative': 'most careful'},
            'helpful': {'comparative': 'more helpful', 'superlative': 'most helpful'},
            'useful': {'comparative': 'more useful', 'superlative': 'most useful'},
            'famous': {'comparative': 'more famous', 'superlative': 'most famous'},
            'important': {'comparative': 'more important', 'superlative': 'most important'},
            'interesting': {'comparative': 'more interesting', 'superlative': 'most interesting'},
            'difficult': {'comparative': 'more difficult', 'superlative': 'most difficult'},
            'expensive': {'comparative': 'more expensive', 'superlative': 'most expensive'},
            'comfortable': {'comparative': 'more comfortable', 'superlative': 'most comfortable'}
        }
        
        # 检查是否是形容词原级，查找对应的比较级和最高级
        adj_data = adjective_forms.get(english_word)
        if adj_data:
            related_forms = []
            for word in learned_words:
                word_lower = word['english_word'].lower()
                if (word_lower == adj_data['comparative'] or 
                    word_lower == adj_data['superlative']):
                    related_forms.append(word)
            
            if related_forms:
                patterns.append({
                    'pattern_id': f'adjective_forms_{english_word}',
                    'pattern_type': 'adjective_forms',
                    'pattern_value': english_word,
                    'pattern_name': f'{english_word}的变化形式',
                    'match_strength': 0.95,
                    'match_reason': f'形容词的比较级和最高级形式：{english_word} → {adj_data["comparative"]} → {adj_data["superlative"]}',
                    'is_primary': True,
                    'cognitive_level': 'intermediate',
                    'grammar_info': adj_data,
                    'similar_words': related_forms
                })
        
        # 检查是否是比较级或最高级，查找原级
        for base_form, forms in adjective_forms.items():
            if (english_word == forms['comparative'] or 
                english_word == forms['superlative']):
                related_forms = []
                for word in learned_words:
                    word_lower = word['english_word'].lower()
                    if (word_lower == base_form or 
                        word_lower == forms['comparative'] or 
                        word_lower == forms['superlative']):
                        if word_lower != english_word:  # 排除自己
                            related_forms.append(word)
                
                if related_forms:
                    patterns.append({
                        'pattern_id': f'adjective_forms_{base_form}',
                        'pattern_type': 'adjective_forms',
                        'pattern_value': base_form,
                        'pattern_name': f'{base_form}的变化形式',
                        'match_strength': 0.95,
                        'match_reason': f'形容词的原级、比较级和最高级：{base_form} → {forms["comparative"]} → {forms["superlative"]}',
                        'is_primary': True,
                        'cognitive_level': 'intermediate',
                        'grammar_info': forms,
                        'similar_words': related_forms
                    })
                break
        
        return patterns
    
    def _detect_plural_patterns(self, english_word: str, learned_words: List[Dict]) -> List[Dict]:
        """检测名词复数形式patterns"""
        patterns = []
        
        # 常见名词复数形式词典
        plural_forms = {
            # 规则复数（直接加s）
            'book': 'books', 'cat': 'cats', 'dog': 'dogs', 'car': 'cars', 'house': 'houses',
            'table': 'tables', 'chair': 'chairs', 'window': 'windows', 'door': 'doors',
            'apple': 'apples', 'orange': 'oranges', 'banana': 'bananas', 'cake': 'cakes',
            
            # 以s, x, z, ch, sh结尾加es
            'box': 'boxes', 'bus': 'buses', 'class': 'classes', 'glass': 'glasses',
            'watch': 'watches', 'dish': 'dishes', 'brush': 'brushes', 'lunch': 'lunches',
            
            # 以辅音字母+y结尾变ies
            'baby': 'babies', 'lady': 'ladies', 'story': 'stories', 'city': 'cities',
            'family': 'families', 'country': 'countries', 'party': 'parties',
            'library': 'libraries', 'dictionary': 'dictionaries',
            
            # 以f或fe结尾变ves
            'knife': 'knives', 'wife': 'wives', 'life': 'lives', 'leaf': 'leaves',
            'shelf': 'shelves', 'half': 'halves', 'wolf': 'wolves',
            
            # 以o结尾加es（某些情况）
            'potato': 'potatoes', 'tomato': 'tomatoes', 'hero': 'heroes',
            
            # 不规则复数
            'man': 'men', 'woman': 'women', 'child': 'children', 'foot': 'feet',
            'tooth': 'teeth', 'mouse': 'mice', 'goose': 'geese', 'person': 'people',
            
            # 单复数同形
            'sheep': 'sheep', 'fish': 'fish', 'deer': 'deer',
            
            # 外来词复数
            'child': 'children', 'ox': 'oxen'
        }
        
        # 检查是否是单数名词，查找对应复数
        plural_form = plural_forms.get(english_word)
        if plural_form:
            related_plurals = []
            for word in learned_words:
                if word['english_word'].lower() == plural_form:
                    related_plurals.append(word)
            
            if related_plurals:
                # 确定复数规则类型
                rule_type = self._determine_plural_rule(english_word, plural_form)
                
                patterns.append({
                    'pattern_id': f'plural_{english_word}',
                    'pattern_type': 'plural_forms',
                    'pattern_value': english_word,
                    'pattern_name': f'{english_word}的复数形式',
                    'match_strength': 0.95,
                    'match_reason': f'名词复数变化：{english_word} → {plural_form} ({rule_type})',
                    'is_primary': True,
                    'cognitive_level': 'basic',
                    'grammar_info': {'singular': english_word, 'plural': plural_form, 'rule': rule_type},
                    'similar_words': related_plurals
                })
        
        # 检查是否是复数名词，查找对应单数
        for singular, plural in plural_forms.items():
            if english_word == plural:
                related_singulars = []
                for word in learned_words:
                    if word['english_word'].lower() == singular:
                        related_singulars.append(word)
                
                if related_singulars:
                    rule_type = self._determine_plural_rule(singular, plural)
                    
                    patterns.append({
                        'pattern_id': f'plural_{singular}',
                        'pattern_type': 'plural_forms',
                        'pattern_value': singular,
                        'pattern_name': f'{singular}的复数形式',
                        'match_strength': 0.95,
                        'match_reason': f'名词复数变化：{singular} → {plural} ({rule_type})',
                        'is_primary': True,
                        'cognitive_level': 'basic',
                        'grammar_info': {'singular': singular, 'plural': plural, 'rule': rule_type},
                        'similar_words': related_singulars
                    })
                break
        
        return patterns
    
    def _determine_plural_rule(self, singular: str, plural: str) -> str:
        """确定复数变化规则类型"""
        if singular == plural:
            return '单复数同形'
        elif plural == singular + 's':
            return '直接加s'
        elif plural == singular + 'es':
            return '加es'
        elif singular.endswith('y') and plural == singular[:-1] + 'ies':
            return '辅音+y变ies'
        elif (singular.endswith('f') and plural == singular[:-1] + 'ves') or \
             (singular.endswith('fe') and plural == singular[:-2] + 'ves'):
            return 'f/fe变ves'
        elif singular in ['man', 'woman', 'child', 'foot', 'tooth', 'mouse', 'goose', 'person']:
            return '不规则变化'
        else:
            return '特殊变化'
    
    def _generate_synonym_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成同义词的推荐理由"""
        return f"💡 这些词汇与'{pattern_value}'意思相近，学习同义词能丰富表达方式，让你的英语更加地道。当想表达相同概念时，可以选择不同的词汇来避免重复！"
    
    def _generate_antonym_reason(self, pattern_value: str, pattern_name: str) -> str:
        """生成反义词的推荐理由"""
        return f"⚡ 这些词汇与'{pattern_value}'意思相反，对比学习反义词能加深理解。通过对比记忆法，一次学会两个相反的概念，记忆效果更佳！"
    
    def _generate_adjective_forms_reason(self, pattern_value: str, pattern_name: str, pattern: Dict) -> str:
        """生成形容词变形的推荐理由"""
        grammar_info = pattern.get('grammar_info', {})
        comparative = grammar_info.get('comparative', '')
        superlative = grammar_info.get('superlative', '')
        
        if 'more' in comparative:
            tip = "💼 多音节形容词用'more/most'变化，这是正式英语的重要特征。"
        elif pattern_value in ['good', 'bad', 'many', 'much', 'little', 'far']:
            tip = "⭐ 这是不规则变化，需要特别记忆，但它们都是高频词汇，值得重点掌握！"
        else:
            tip = "📐 这是规则变化，掌握规律后能举一反三。"
        
        return f"🎯 形容词三级变化是英语语法核心：{pattern_value} → {comparative} → {superlative}。{tip}掌握比较结构能让你准确表达程度差异！"
    
    def _generate_plural_forms_reason(self, pattern_value: str, pattern_name: str, pattern: Dict) -> str:
        """生成复数形式的推荐理由"""
        grammar_info = pattern.get('grammar_info', {})
        plural = grammar_info.get('plural', '')
        rule = grammar_info.get('rule', '')
        
        rule_tips = {
            '直接加s': '这是最基本的复数规则，适用于大多数名词。',
            '加es': '以s, x, z, ch, sh结尾的词要加es，发音更清晰。',
            '辅音+y变ies': '辅音字母+y结尾要变y为i再加es，记住这个规律！',
            'f/fe变ves': 'f/fe结尾变ves是特殊规律，生活中很实用。',
            '不规则变化': '这些是不规则变化，需要特别记忆，但都是常用词。',
            '单复数同形': '单复数相同的词不多，记住它们能避免错误。'
        }
        
        tip = rule_tips.get(rule, '掌握复数规律是英语基础。')
        
        return f"📚 名词复数变化：{pattern_value} → {plural} ({rule})。{tip} 正确使用单复数是英语准确性的重要标志！"