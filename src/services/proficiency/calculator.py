"""
熟练度计算器
基于多维度算法计算单词学习熟练度
"""
from datetime import date, datetime
from typing import Optional, Dict


class ProficiencyCalculator:
    """熟练度计算服务类"""
    
    @staticmethod
    def calculate_proficiency(learning_count: int, correct_count: int, 
                            avg_duration: float, last_learning_date: Optional[str]) -> float:
        """
        计算熟练度评分 (0-100)
        优化权重分配: 正确率(60%) + 学习间隔(25%) + 回答用时(15%)
        
        Args:
            learning_count: 学习次数
            correct_count: 正确次数
            avg_duration: 平均答题时间（秒）
            last_learning_date: 最后学习日期
            
        Returns:
            float: 熟练度评分 (0-100)
        """
        if learning_count == 0:
            return 0.0
        
        # 维度1: 正确率评分 (60%) - 最重要的指标
        accuracy_score = (correct_count / learning_count) * 100 * 0.6
        
        # 维度2: 学习间隔评分 (25%) - 记忆保持能力
        interval_score = ProficiencyCalculator._calculate_interval_score(last_learning_date) * 0.25
        
        # 维度3: 回答用时评分 (15%) - 熟练程度的体现，但权重较低
        duration_score = ProficiencyCalculator._calculate_duration_score(avg_duration) * 0.15
        
        total_score = accuracy_score + interval_score + duration_score
        return min(100.0, max(0.0, total_score))
    
    @staticmethod
    def _calculate_duration_score(avg_duration: float) -> float:
        """
        计算答题时间评分
        
        Args:
            avg_duration: 平均答题时间（秒）
            
        Returns:
            float: 时间评分 (0-100)
        """
        if avg_duration <= 0:
            return 0.0
        
        if 5 <= avg_duration <= 15:
            return 100.0  # 最优时间段
        elif avg_duration < 5:
            return 80.0   # 过快可能是猜测
        elif avg_duration <= 30:
            return max(0.0, 100 - (avg_duration - 15) * 3)  # 逐渐下降
        else:
            return 20.0   # 过慢
    
    @staticmethod
    def _calculate_interval_score(last_learning_date: Optional[str]) -> float:
        """
        计算学习间隔评分
        
        Args:
            last_learning_date: 最后学习日期字符串
            
        Returns:
            float: 间隔评分 (0-100)
        """
        if not last_learning_date:
            return 50.0  # 默认分数
        
        try:
            last_date = datetime.strptime(last_learning_date, '%Y-%m-%d').date()
            days_diff = (date.today() - last_date).days
            
            if days_diff == 0:
                return 100.0  # 当天学习
            elif days_diff <= 3:
                return 90.0   # 3天内
            elif days_diff <= 7:
                return 70.0   # 一周内
            elif days_diff <= 30:
                return 50.0   # 一月内
            else:
                return 20.0   # 超过一月
        except:
            return 50.0  # 解析失败时的默认分数
    
    @staticmethod
    def calculate_difficulty_level(proficiency: float) -> str:
        """
        根据熟练度计算难度等级
        
        Args:
            proficiency: 熟练度评分
            
        Returns:
            str: 难度等级
        """
        if proficiency >= 95:
            return 'expert'      # 专家级
        elif proficiency >= 80:
            return 'proficient'  # 熟练
        elif proficiency >= 65:
            return 'intermediate' # 中等
        elif proficiency >= 50:
            return 'basic'       # 基础
        elif proficiency >= 25:
            return 'beginner'    # 初学者
        else:
            return 'unfamiliar'  # 不熟悉
    
    @staticmethod
    def suggest_review_interval(proficiency: float) -> int:
        """
        根据熟练度建议复习间隔（天数）
        
        Args:
            proficiency: 熟练度评分
            
        Returns:
            int: 建议复习间隔天数
        """
        if proficiency >= 90:
            return 30    # 30天后复习
        elif proficiency >= 80:
            return 14    # 2周后复习
        elif proficiency >= 70:
            return 7     # 1周后复习
        elif proficiency >= 60:
            return 3     # 3天后复习
        elif proficiency >= 50:
            return 1     # 1天后复习
        else:
            return 0     # 立即复习
    
    @staticmethod
    def calculate_learning_efficiency(correct_count: int, learning_count: int, 
                                    total_duration: float) -> Dict[str, float]:
        """
        计算学习效率指标
        
        Args:
            correct_count: 正确次数
            learning_count: 学习次数
            total_duration: 总学习时间（秒）
            
        Returns:
            Dict[str, float]: 效率指标
        """
        if learning_count == 0:
            return {
                'accuracy_rate': 0.0,
                'avg_duration': 0.0,
                'efficiency_score': 0.0
            }
        
        accuracy_rate = correct_count / learning_count
        avg_duration = total_duration / learning_count
        
        # 效率评分：正确率高且用时短的效率高
        if avg_duration > 0:
            efficiency_score = (accuracy_rate * 100) / (avg_duration / 10)  # 标准化到10秒基准
        else:
            efficiency_score = 0.0
        
        return {
            'accuracy_rate': accuracy_rate * 100,
            'avg_duration': avg_duration,
            'efficiency_score': min(100.0, efficiency_score)
        }
    
    @staticmethod
    def predict_mastery_time(current_proficiency: float, target_proficiency: float = 80.0,
                           learning_rate: float = 5.0) -> int:
        """
        预测达到目标熟练度所需时间
        
        Args:
            current_proficiency: 当前熟练度
            target_proficiency: 目标熟练度
            learning_rate: 学习速率（每次学习提升的熟练度）
            
        Returns:
            int: 预计需要的学习次数
        """
        if current_proficiency >= target_proficiency:
            return 0
        
        proficiency_gap = target_proficiency - current_proficiency
        estimated_sessions = int(proficiency_gap / learning_rate) + 1
        
        return max(1, estimated_sessions)
