"""
API响应格式标准化工具
严格按照API_CONTRACT.md规范实现统一的响应格式
"""
from datetime import datetime
from typing import Any, Dict, Optional, Union
from functools import wraps
from flask import jsonify


class APIResponse:
    """API响应格式标准化类"""

    VERSION = "1.0.0"

    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """
        成功响应格式

        Args:
            data: 响应数据
            message: 成功消息

        Returns:
            Dict: 标准化的成功响应
        """
        return {
            "success": True,
            "data": data,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "version": APIResponse.VERSION
        }

    @staticmethod
    def error(error_code: str, message: str, details: Optional[Dict] = None) -> Dict[str, Any]:
        """
        错误响应格式

        Args:
            error_code: 错误代码
            message: 错误消息
            details: 错误详情

        Returns:
            Dict: 标准化的错误响应
        """
        return {
            "success": False,
            "error": {
                "code": error_code,
                "message": message,
                "details": details or {}
            },
            "timestamp": datetime.now().isoformat(),
            "version": APIResponse.VERSION
        }

    @staticmethod
    def json_success(data: Any = None, message: str = "操作成功", status_code: int = 200):
        """
        返回JSON格式的成功响应

        Args:
            data: 响应数据
            message: 成功消息
            status_code: HTTP状态码

        Returns:
            Flask Response对象
        """
        return jsonify(APIResponse.success(data, message)), status_code

    @staticmethod
    def json_error(error_code: str, message: str, details: Optional[Dict] = None,
                   status_code: int = 400):
        """
        返回JSON格式的错误响应

        Args:
            error_code: 错误代码
            message: 错误消息
            details: 错误详情
            status_code: HTTP状态码

        Returns:
            Flask Response对象
        """
        return jsonify(APIResponse.error(error_code, message, details)), status_code


class APIErrorCodes:
    """API错误代码常量"""

    # 认证相关
    AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED"
    INVALID_USER_ID = "INVALID_USER_ID"
    UNAUTHORIZED_ACCESS = "UNAUTHORIZED_ACCESS"

    # 数据验证相关
    VALIDATION_ERROR = "VALIDATION_ERROR"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    INVALID_DATA_FORMAT = "INVALID_DATA_FORMAT"
    INVALID_PARAMS = "INVALID_PARAMS"

    # 业务逻辑相关
    WORD_NOT_FOUND = "WORD_NOT_FOUND"
    PLAN_GENERATION_FAILED = "PLAN_GENERATION_FAILED"
    BUSINESS_LOGIC_ERROR = "BUSINESS_LOGIC_ERROR"
    INSUFFICIENT_POINTS = "INSUFFICIENT_POINTS"

    # 系统相关
    DATABASE_ERROR = "DATABASE_ERROR"
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"


def api_response(func):
    """
    API响应装饰器
    自动处理异常并返回标准化响应格式
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)

            # 如果函数返回的是标准化响应，直接返回
            if isinstance(result, tuple) and len(result) == 2:
                return result
            elif isinstance(result, dict) and 'success' in result:
                return jsonify(result)
            else:
                # 如果返回的是普通数据，包装成标准格式
                return APIResponse.json_success(result)

        except ValueError as e:
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                f"数据验证错误: {str(e)}",
                {"error_type": "ValueError"}
            )
        except KeyError as e:
            return APIResponse.json_error(
                APIErrorCodes.MISSING_REQUIRED_FIELD,
                f"缺少必需字段: {str(e)}",
                {"missing_field": str(e)}
            )
        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"服务器内部错误: {str(e)}",
                {"error_type": type(e).__name__}
            )

    return wrapper


def require_auth(func):
    """
    认证装饰器
    检查用户是否已登录
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        from flask import session

        if 'user_id' not in session:
            return APIResponse.json_error(
                APIErrorCodes.AUTHENTICATION_REQUIRED,
                "请先登录",
                status_code=401
            )

        return func(*args, **kwargs)

    return wrapper


def validate_json(required_fields: list = None):
    """
    JSON数据验证装饰器

    Args:
        required_fields: 必需字段列表
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            from flask import request

            # 检查Content-Type
            if not request.is_json:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_DATA_FORMAT,
                    "请求必须是JSON格式"
                )

            data = request.get_json()
            if not data:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_DATA_FORMAT,
                    "请求数据不能为空"
                )

            # 检查必需字段
            if required_fields:
                missing_fields = []
                for field in required_fields:
                    if field not in data:
                        missing_fields.append(field)

                if missing_fields:
                    return APIResponse.json_error(
                        APIErrorCodes.MISSING_REQUIRED_FIELD,
                        f"缺少必需字段: {', '.join(missing_fields)}",
                        {"missing_fields": missing_fields}
                    )

            return func(*args, **kwargs)

        return wrapper
    return decorator


# 常用的响应消息
class APIMessages:
    """API响应消息常量"""

    # 成功消息
    SUCCESS = "操作成功"
    PLAN_GENERATED = "学习计划生成成功"
    ANSWER_SUBMITTED = "答案提交成功"
    POINTS_UPDATED = "积分更新成功"
    VOUCHERS_UPDATED = "购物券更新成功"

    # 错误消息
    LOGIN_REQUIRED = "请先登录"
    USER_NOT_FOUND = "用户不存在"
    WORD_NOT_FOUND = "单词不存在"
    INVALID_INPUT = "输入数据无效"
    OPERATION_FAILED = "操作失败"

    # 业务消息
    DAILY_PLAN_EXISTS = "今日学习计划已存在"
    INSUFFICIENT_WORDS = "可用单词数量不足"
    LEARNING_COMPLETED = "学习任务已完成"


# 使用示例
if __name__ == "__main__":
    # 成功响应示例
    success_response = APIResponse.success(
        data={"words": [], "total": 25},
        message=APIMessages.PLAN_GENERATED
    )
    print("Success Response:", success_response)

    # 错误响应示例
    error_response = APIResponse.error(
        error_code=APIErrorCodes.WORD_NOT_FOUND,
        message=APIMessages.WORD_NOT_FOUND,
        details={"word_id": 123}
    )
    print("Error Response:", error_response)
