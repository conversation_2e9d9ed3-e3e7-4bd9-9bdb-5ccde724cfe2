#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化资源同步和完整性检查机制
确保单词与静态资源的双向MECE关系始终保持一致

功能：
1. 实时监控数据库变化
2. 自动生成缺失的静态资源
3. 定期完整性检查
4. 资源同步和修复
5. MECE合规性监控
"""

import os
import sys
import sqlite3
import json
import asyncio
import aiofiles
import httpx
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from contextlib import asynccontextmanager

# 添加项目根目录到路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

@dataclass
class ResourceStatus:
    """资源状态数据类"""
    word_id: int
    english_word: str
    chinese_meaning: str
    has_audio: bool
    has_text: bool
    audio_path: Optional[str] = None
    text_path: Optional[str] = None
    last_checked: datetime = None

@dataclass
class MECEReport:
    """MECE检查报告"""
    total_words: int
    perfect_matches: int
    missing_audio: List[str]
    missing_text: List[str]
    orphaned_audio: List[str]
    orphaned_text: List[str]
    mece_compliance_rate: float
    report_time: datetime

class ResourceSyncMonitor:
    """资源同步监控器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / "instance" / "words.db"
        self.audio_dir = self.project_root / "static" / "audio" / "words"
        self.text_dir = self.project_root / "static" / "cache" / "memory_help"
        self.mapping_file = self.project_root / "static" / "cache" / "resource_mapping.json"
        self.sync_log_file = self.project_root / "logs" / "resource_sync.log"
        self.mece_report_file = self.project_root / "logs" / "mece_compliance.json"
        
        # 确保日志目录存在
        self.sync_log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # TTS服务配置
        self.tts_api_url = "http://localhost:5005/api/generate_audio"
        self.ai_text_api_url = "http://localhost:5005/api/generate_memory_help"
        
    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        try:
            with open(self.sync_log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            print(f"❌ 写入日志失败: {e}")
        
        # 同时输出到控制台
        print(f"{log_entry.strip()}")
    
    def get_standardized_filename(self, word_id: int, file_type: str) -> str:
        """获取标准化文件名"""
        extension = '.mp3' if file_type == 'audio' else '.txt'
        return f"word_{word_id:04d}{extension}"
    
    async def get_all_words_async(self) -> List[Tuple[int, str, str]]:
        """异步获取所有单词"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, english_word, chinese_meaning 
                FROM word 
                ORDER BY id
            """)
            
            words = cursor.fetchall()
            conn.close()
            
            return words
            
        except Exception as e:
            self.log_message(f"数据库查询失败: {e}", "ERROR")
            return []
    
    async def check_resource_existence(self, word_id: int) -> Tuple[bool, bool]:
        """检查资源文件是否存在"""
        audio_file = self.audio_dir / self.get_standardized_filename(word_id, 'audio')
        text_file = self.text_dir / self.get_standardized_filename(word_id, 'text')
        
        return audio_file.exists(), text_file.exists()
    
    async def generate_audio_resource(self, word_id: int, english_word: str) -> bool:
        """生成音频资源"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.tts_api_url,
                    json={"word": english_word}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        # 重命名生成的文件为标准化名称
                        audio_filename = self.get_standardized_filename(word_id, 'audio')
                        target_path = self.audio_dir / audio_filename
                        
                        # 如果API返回了文件路径，移动到标准位置
                        if 'file_path' in result:
                            source_path = Path(result['file_path'])
                            if source_path.exists():
                                source_path.rename(target_path)
                                self.log_message(f"✅ 音频生成成功: {english_word} -> {audio_filename}")
                                return True
                
                self.log_message(f"❌ 音频生成失败: {english_word} - {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log_message(f"❌ 音频生成异常: {english_word} - {e}", "ERROR")
            return False
    
    async def generate_text_resource(self, word_id: int, english_word: str, chinese_meaning: str) -> bool:
        """生成文本资源"""
        try:
            # 生成基础记忆辅助内容
            memory_content = self.generate_basic_memory_help(english_word, chinese_meaning)
            
            text_filename = self.get_standardized_filename(word_id, 'text')
            text_path = self.text_dir / text_filename
            
            async with aiofiles.open(text_path, 'w', encoding='utf-8') as f:
                await f.write(memory_content)
            
            self.log_message(f"✅ 文本生成成功: {english_word} -> {text_filename}")
            return True
            
        except Exception as e:
            self.log_message(f"❌ 文本生成异常: {english_word} - {e}", "ERROR")
            return False
    
    def generate_basic_memory_help(self, english_word: str, chinese_meaning: str) -> str:
        """生成基础记忆辅助内容"""
        return f"""# {english_word}

## 基本信息
**中文释义**: {chinese_meaning}
**单词类型**: 待补充
**难度等级**: 待评估

## 记忆技巧
- **联想记忆**: 待完善
- **词根分析**: 待分析
- **例句记忆**: 待补充

## 使用场景
- 待补充具体使用场景

## 相关词汇
- 待补充相关词汇

---
*此内容由系统自动生成，需要人工完善*
*生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    async def sync_missing_resources(self) -> Dict[str, int]:
        """同步缺失的资源文件"""
        words = await self.get_all_words_async()
        results = {
            'audio_generated': 0,
            'text_generated': 0,
            'audio_failed': 0,
            'text_failed': 0
        }
        
        self.log_message(f"🚀 开始同步 {len(words)} 个单词的资源文件")
        
        # 批量处理，避免过大负载
        batch_size = 10
        for i in range(0, len(words), batch_size):
            batch = words[i:i + batch_size]
            
            # 并发处理批次
            tasks = []
            for word_id, english_word, chinese_meaning in batch:
                tasks.append(self.sync_single_word_resources(
                    word_id, english_word, chinese_meaning, results
                ))
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # 进度输出
            self.log_message(f"📊 已处理 {min(i + batch_size, len(words))}/{len(words)} 个单词")
            
            # 避免过快请求
            await asyncio.sleep(0.5)
        
        return results
    
    async def sync_single_word_resources(self, word_id: int, english_word: str, 
                                       chinese_meaning: str, results: Dict[str, int]):
        """同步单个单词的资源"""
        has_audio, has_text = await self.check_resource_existence(word_id)
        
        # 生成缺失的音频
        if not has_audio:
            if await self.generate_audio_resource(word_id, english_word):
                results['audio_generated'] += 1
            else:
                results['audio_failed'] += 1
        
        # 生成缺失的文本
        if not has_text:
            if await self.generate_text_resource(word_id, english_word, chinese_meaning):
                results['text_generated'] += 1
            else:
                results['text_failed'] += 1
    
    async def perform_mece_check(self) -> MECEReport:
        """执行MECE合规性检查"""
        words = await self.get_all_words_async()
        
        # 获取所有资源状态
        word_statuses = []
        for word_id, english_word, chinese_meaning in words:
            has_audio, has_text = await self.check_resource_existence(word_id)
            
            status = ResourceStatus(
                word_id=word_id,
                english_word=english_word,
                chinese_meaning=chinese_meaning,
                has_audio=has_audio,
                has_text=has_text,
                last_checked=datetime.now()
            )
            word_statuses.append(status)
        
        # 统计MECE合规性
        perfect_matches = sum(1 for s in word_statuses if s.has_audio and s.has_text)
        missing_audio = [s.english_word for s in word_statuses if not s.has_audio]
        missing_text = [s.english_word for s in word_statuses if not s.has_text]
        
        # 检查孤立文件
        orphaned_audio = await self.find_orphaned_files('audio')
        orphaned_text = await self.find_orphaned_files('text')
        
        # 计算合规率
        mece_compliance_rate = perfect_matches / len(words) * 100 if words else 0
        
        report = MECEReport(
            total_words=len(words),
            perfect_matches=perfect_matches,
            missing_audio=missing_audio,
            missing_text=missing_text,
            orphaned_audio=orphaned_audio,
            orphaned_text=orphaned_text,
            mece_compliance_rate=mece_compliance_rate,
            report_time=datetime.now()
        )
        
        return report
    
    async def find_orphaned_files(self, file_type: str) -> List[str]:
        """查找孤立的文件（没有对应单词的文件）"""
        directory = self.audio_dir if file_type == 'audio' else self.text_dir
        extension = '.mp3' if file_type == 'audio' else '.txt'
        
        if not directory.exists():
            return []
        
        # 获取所有标准化文件名
        words = await self.get_all_words_async()
        expected_files = {
            self.get_standardized_filename(word_id, file_type) 
            for word_id, _, _ in words
        }
        
        # 查找实际文件
        actual_files = {
            f.name for f in directory.glob(f"*{extension}")
        }
        
        # 返回孤立文件
        orphaned = actual_files - expected_files
        return list(orphaned)
    
    async def save_mece_report(self, report: MECEReport):
        """保存MECE检查报告"""
        report_data = {
            'total_words': report.total_words,
            'perfect_matches': report.perfect_matches,
            'missing_audio_count': len(report.missing_audio),
            'missing_text_count': len(report.missing_text),
            'missing_audio': report.missing_audio[:10],  # 只保存前10个
            'missing_text': report.missing_text[:10],    # 只保存前10个
            'orphaned_audio_count': len(report.orphaned_audio),
            'orphaned_text_count': len(report.orphaned_text),
            'orphaned_audio': report.orphaned_audio[:10],
            'orphaned_text': report.orphaned_text[:10],
            'mece_compliance_rate': report.mece_compliance_rate,
            'report_time': report.report_time.isoformat(),
            'status': 'COMPLIANT' if report.mece_compliance_rate >= 95 else 'NON_COMPLIANT'
        }
        
        try:
            async with aiofiles.open(self.mece_report_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(report_data, ensure_ascii=False, indent=2))
            
            self.log_message(f"📊 MECE报告已保存: 合规率 {report.mece_compliance_rate:.1f}%")
            
        except Exception as e:
            self.log_message(f"❌ 保存MECE报告失败: {e}", "ERROR")
    
    async def update_resource_mapping(self):
        """更新资源映射表"""
        words = await self.get_all_words_async()
        resource_map = {}
        
        for word_id, english_word, chinese_meaning in words:
            audio_file = self.get_standardized_filename(word_id, 'audio')
            text_file = self.get_standardized_filename(word_id, 'text')
            
            has_audio, has_text = await self.check_resource_existence(word_id)
            
            resource_map[english_word] = {
                'word_id': word_id,
                'chinese_meaning': chinese_meaning,
                'audio_file': audio_file if has_audio else None,
                'text_file': text_file if has_text else None,
                'audio_path': f"/static/audio/words/{audio_file}" if has_audio else None,
                'text_path': f"/static/cache/memory_help/{text_file}" if has_text else None,
                'last_updated': datetime.now().isoformat()
            }
        
        try:
            async with aiofiles.open(self.mapping_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(resource_map, ensure_ascii=False, indent=2))
            
            self.log_message(f"✅ 资源映射表已更新: {len(resource_map)} 个单词")
            
        except Exception as e:
            self.log_message(f"❌ 更新资源映射表失败: {e}", "ERROR")
    
    async def run_full_sync(self) -> Dict[str, any]:
        """执行完整同步流程"""
        self.log_message("🚀 开始执行完整资源同步...")
        
        # 1. 执行MECE检查
        report = await self.perform_mece_check()
        await self.save_mece_report(report)
        
        self.log_message(f"📊 MECE检查完成: 合规率 {report.mece_compliance_rate:.1f}%")
        
        # 2. 同步缺失资源
        sync_results = await self.sync_missing_resources()
        
        # 3. 更新资源映射表
        await self.update_resource_mapping()
        
        # 4. 再次检查MECE合规性
        final_report = await self.perform_mece_check()
        await self.save_mece_report(final_report)
        
        results = {
            'initial_compliance': report.mece_compliance_rate,
            'final_compliance': final_report.mece_compliance_rate,
            'sync_results': sync_results,
            'improvement': final_report.mece_compliance_rate - report.mece_compliance_rate
        }
        
        self.log_message(f"✅ 完整同步完成: 合规率提升 {results['improvement']:.1f}%")
        
        return results
    
    async def run_monitoring_loop(self, check_interval: int = 3600):
        """运行持续监控循环"""
        self.log_message(f"🔄 启动资源监控循环，检查间隔: {check_interval}秒")
        
        while True:
            try:
                # 执行定期检查
                report = await self.perform_mece_check()
                await self.save_mece_report(report)
                
                # 如果合规率低于95%，执行自动修复
                if report.mece_compliance_rate < 95:
                    self.log_message(f"⚠️ MECE合规率过低 ({report.mece_compliance_rate:.1f}%)，执行自动修复")
                    await self.sync_missing_resources()
                    await self.update_resource_mapping()
                
                self.log_message(f"💚 监控检查完成: 合规率 {report.mece_compliance_rate:.1f}%")
                
                # 等待下次检查
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                self.log_message(f"❌ 监控循环异常: {e}", "ERROR")
                await asyncio.sleep(60)  # 出错时短暂等待

async def main():
    """主函数"""
    print("🔧 资源同步监控器启动")
    
    project_root = Path(__file__).parent.parent
    monitor = ResourceSyncMonitor(str(project_root))
    
    # 检查必要条件
    if not monitor.db_path.exists():
        print(f"❌ 数据库文件不存在: {monitor.db_path}")
        return 1
    
    try:
        # 执行一次完整同步
        results = await monitor.run_full_sync()
        
        print("\n📊 同步结果摘要:")
        print(f"初始合规率: {results['initial_compliance']:.1f}%")
        print(f"最终合规率: {results['final_compliance']:.1f}%")
        print(f"合规率提升: {results['improvement']:.1f}%")
        print(f"音频生成: {results['sync_results']['audio_generated']} 个")
        print(f"文本生成: {results['sync_results']['text_generated']} 个")
        print(f"音频失败: {results['sync_results']['audio_failed']} 个")
        print(f"文本失败: {results['sync_results']['text_failed']} 个")
        
        print("\n✅ 资源同步监控完成!")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(asyncio.run(main()))