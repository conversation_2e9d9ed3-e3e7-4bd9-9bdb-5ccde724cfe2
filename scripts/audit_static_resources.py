#!/usr/bin/env python3
"""
静态资源完整性审计脚本
精确对比数据库单词与音频、图片文件，生成MECE分析报告
"""

import os
import sys
import sqlite3
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class StaticResourceAuditor:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / 'instance' / 'words.db'
        self.audio_dir = self.project_root / 'static' / 'audio' / 'words'
        self.image_dir = self.project_root / 'static' / 'images' / 'words'
        
    def normalize_word_for_filename(self, word):
        """标准化单词为文件名格式"""
        return word.lower().replace(' ', '_').replace("'", "").replace('"', '')
    
    def get_all_words_from_db(self):
        """从数据库获取所有单词"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT english_word FROM word ORDER BY english_word")
            words = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            return words
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return []
    
    def get_existing_audio_files(self):
        """获取现有音频文件列表"""
        if not self.audio_dir.exists():
            return set()
        
        audio_files = set()
        for mp3_file in self.audio_dir.glob('*.mp3'):
            # 移除.mp3扩展名
            audio_files.add(mp3_file.stem)
        
        return audio_files
    
    def get_existing_image_files(self):
        """获取现有图片文件列表"""
        if not self.image_dir.exists():
            return set()
        
        image_files = set()
        for img_file in self.image_dir.glob('*.jpg'):
            # 移除.jpg扩展名
            image_files.add(img_file.stem)
        
        return image_files
    
    def perform_mece_analysis(self):
        """执行MECE分析"""
        print("🔍 开始静态资源MECE分析...")
        
        # 获取数据
        db_words = self.get_all_words_from_db()
        audio_files = self.get_existing_audio_files()
        image_files = self.get_existing_image_files()
        
        # 标准化数据库单词
        normalized_db_words = {self.normalize_word_for_filename(word): word for word in db_words}
        normalized_word_set = set(normalized_db_words.keys())
        
        # 音频文件分析
        audio_match = normalized_word_set & audio_files
        audio_missing = normalized_word_set - audio_files
        audio_extra = audio_files - normalized_word_set
        
        # 图片文件分析
        image_match = normalized_word_set & image_files
        image_missing = normalized_word_set - image_files
        image_extra = image_files - normalized_word_set
        
        # 生成报告
        report = {
            'timestamp': datetime.now().isoformat(),
            'database': {
                'total_words': len(db_words),
                'words': db_words
            },
            'audio': {
                'total_files': len(audio_files),
                'matched': len(audio_match),
                'missing': len(audio_missing),
                'extra': len(audio_extra),
                'coverage_rate': f"{len(audio_match)/len(db_words)*100:.1f}%",
                'missing_words': [normalized_db_words[word] for word in audio_missing],
                'extra_files': list(audio_extra)
            },
            'images': {
                'total_files': len(image_files),
                'matched': len(image_match),
                'missing': len(image_missing),
                'extra': len(image_extra),
                'coverage_rate': f"{len(image_match)/len(db_words)*100:.1f}%",
                'missing_words': [normalized_db_words[word] for word in image_missing],
                'extra_files': list(image_extra)
            }
        }
        
        return report
    
    def generate_missing_lists(self, report):
        """生成缺失资源的详细列表"""
        # 按优先级排序缺失的图片（这里可以根据词频等信息调整）
        missing_images = report['images']['missing_words']
        missing_audio = report['audio']['missing_words']
        
        # 生成批次处理列表
        if missing_images:
            # 分为3批处理
            batch_size = max(1, len(missing_images) // 3)
            batches = [
                missing_images[i:i + batch_size] 
                for i in range(0, len(missing_images), batch_size)
            ]
            
            report['image_generation_plan'] = {
                'total_needed': len(missing_images),
                'batch1': batches[0] if len(batches) > 0 else [],
                'batch2': batches[1] if len(batches) > 1 else [],
                'batch3': batches[2] if len(batches) > 2 else [],
                'remaining': sum(batches[3:], []) if len(batches) > 3 else []
            }
        
        if missing_audio:
            report['audio_generation_plan'] = {
                'total_needed': len(missing_audio),
                'words': missing_audio
            }
        
        return report
    
    def save_report(self, report, output_file='static_resources_audit.json'):
        """保存报告到文件"""
        output_path = self.project_root / output_file
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📊 报告已保存到: {output_path}")
        return output_path
    
    def print_summary(self, report):
        """打印总结报告"""
        print("\n" + "="*80)
        print("📋 Word Learning App 静态资源MECE分析报告")
        print("="*80)
        
        print(f"\n🗄️  数据库状况:")
        print(f"   总单词数: {report['database']['total_words']}")
        
        print(f"\n🎵 音频资源状况:")
        print(f"   文件总数: {report['audio']['total_files']}")
        print(f"   完全匹配: {report['audio']['matched']} ({report['audio']['coverage_rate']})")
        print(f"   缺失数量: {report['audio']['missing']}")
        print(f"   多余文件: {report['audio']['extra']}")
        
        print(f"\n🖼️  图片资源状况:")
        print(f"   文件总数: {report['images']['total_files']}")
        print(f"   完全匹配: {report['images']['matched']} ({report['images']['coverage_rate']})")
        print(f"   缺失数量: {report['images']['missing']}")
        print(f"   多余文件: {report['images']['extra']}")
        
        # 缺失资源详情
        if report['audio']['missing'] > 0:
            print(f"\n🔴 缺失音频文件的前10个单词:")
            for word in report['audio']['missing_words'][:10]:
                print(f"   - {word}")
            if len(report['audio']['missing_words']) > 10:
                print(f"   ... 还有 {len(report['audio']['missing_words']) - 10} 个")
        
        if report['images']['missing'] > 0:
            print(f"\n🔴 缺失图片文件的前10个单词:")
            for word in report['images']['missing_words'][:10]:
                print(f"   - {word}")
            if len(report['images']['missing_words']) > 10:
                print(f"   ... 还有 {len(report['images']['missing_words']) - 10} 个")
        
        # 生成计划
        if 'image_generation_plan' in report:
            plan = report['image_generation_plan']
            print(f"\n📋 图片生成计划:")
            print(f"   第一批: {len(plan['batch1'])} 个图片")
            print(f"   第二批: {len(plan['batch2'])} 个图片")
            print(f"   第三批: {len(plan['batch3'])} 个图片")
            if plan['remaining']:
                print(f"   剩余: {len(plan['remaining'])} 个图片")
        
        print("\n" + "="*80)

def main():
    """主函数"""
    # 项目根目录
    project_root = Path(__file__).parent.parent
    
    print("🚀 Word Learning App 静态资源审计工具")
    print(f"📁 项目路径: {project_root}")
    
    # 创建审计器
    auditor = StaticResourceAuditor(project_root)
    
    # 执行分析
    report = auditor.perform_mece_analysis()
    
    # 生成缺失列表
    report = auditor.generate_missing_lists(report)
    
    # 保存报告
    auditor.save_report(report)
    
    # 打印总结
    auditor.print_summary(report)
    
    # 返回关键信息
    return {
        'total_missing_images': report['images']['missing'],
        'total_missing_audio': report['audio']['missing'],
        'image_coverage': report['images']['coverage_rate'],
        'audio_coverage': report['audio']['coverage_rate']
    }

if __name__ == '__main__':
    main()