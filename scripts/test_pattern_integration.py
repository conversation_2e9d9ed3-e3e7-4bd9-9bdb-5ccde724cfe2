#!/usr/bin/env python3
"""
Pattern推荐系统整合测试脚本
验证动态检测算法与概念整合器的协作效果
"""

import sys
import os
from typing import Dict, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.pattern.pattern_recommender import PatternRecommenderService
from src.services.pattern.concept_integrator import ConceptIntegrator
from src.core import get_logger

logger = get_logger(__name__)


class PatternIntegrationTester:
    """Pattern系统整合测试器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.recommender = PatternRecommenderService(db_path)
        self.integrator = ConceptIntegrator()
    
    def test_aunt_case_resolution(self):
        """测试aunt案例的语义冗余解决效果"""
        print("🔍 测试aunt案例的语义冗余解决...")
        
        # 模拟aunt单词和用户已学单词
        aunt_word = {
            'word_id': 1,
            'english_word': 'aunt',
            'chinese_meaning': '阿姨',
            'proficiency': 100.0,
            'difficulty_level': 'easy'
        }
        
        # 模拟用户已学单词
        learned_words = [
            {'word_id': 2, 'english_word': 'sister', 'chinese_meaning': '姐妹', 'status': 'review', 'proficiency': 100.0},
            {'word_id': 3, 'english_word': 'brother', 'chinese_meaning': '兄弟', 'status': 'review', 'proficiency': 100.0},
            {'word_id': 4, 'english_word': 'uncle', 'chinese_meaning': '叔叔', 'status': 'review', 'proficiency': 100.0},
            {'word_id': 5, 'english_word': 'farmer', 'chinese_meaning': '农场主', 'status': 'new', 'proficiency': 0.0},
            {'word_id': 6, 'english_word': 'teacher', 'chinese_meaning': '老师', 'status': 'review', 'proficiency': 80.0},
            {'word_id': 7, 'english_word': 'naughty', 'chinese_meaning': '淘气的', 'status': 'review', 'proficiency': 87.0},
            {'word_id': 8, 'english_word': 'granddaughter', 'chinese_meaning': '孙女', 'status': 'review', 'proficiency': 100.0}
        ]
        
        # 获取patterns（这会触发动态检测和概念整合）
        recommendations = self.recommender.get_similar_words(
            user_id=1, 
            word_id=1, 
            exclude_new_words=False
        )
        
        print(f"📊 检测到 {len(recommendations)} 个推荐patterns")
        
        # 分析结果
        detected_concepts = set()
        for i, rec in enumerate(recommendations, 1):
            pattern_info = rec.pattern_info
            print(f"\n🎯 Pattern {i}: {pattern_info['pattern_name']}")
            print(f"   类型: {pattern_info.get('pattern_type', 'unknown')}")
            print(f"   维度: {pattern_info.get('dimension_category', 'unknown')}")
            print(f"   认知层次: {pattern_info.get('cognitive_level', 'unknown')}")
            print(f"   概念组: {pattern_info.get('concept_group', 'None')}")
            print(f"   教育价值: {pattern_info.get('educational_value', 0):.2f}")
            print(f"   匹配强度: {pattern_info.get('match_strength', 0):.2f}")
            print(f"   推荐理由: {rec.recommendation_reason[:100]}...")
            print(f"   相似单词数量: {len(rec.similar_words)}")
            
            if pattern_info.get('concept_group'):
                detected_concepts.add(pattern_info['concept_group'])
        
        print(f"\n🔗 检测到的概念组: {detected_concepts}")
        
        # 验证关键期望
        expected_improvements = [
            "应该只有1个家庭成员相关的pattern（不是多个重复）",
            "应该只有1个ER相关的pattern（概念整合）", 
            "应该只有1个不发音字母相关的pattern（整合相似规律）",
            "patterns应该按认知层次和教育价值排序",
            "低水平用户应该优先看到基础认知层次patterns"
        ]
        
        print(f"\n✅ 关键改进验证:")
        for improvement in expected_improvements:
            print(f"   • {improvement}")
        
        # 检查是否解决了badcase中的问题
        family_patterns = [r for r in recommendations 
                          if 'family' in r.pattern_info.get('concept_group', '').lower() 
                          or 'family' in r.pattern_info.get('pattern_name', '').lower()]
        
        er_patterns = [r for r in recommendations 
                      if 'er' in r.pattern_info.get('concept_group', '').lower()
                      or ('er' in r.pattern_info.get('pattern_value', '') 
                          and r.pattern_info.get('pattern_type') in ['letter_combo', 'suffix', 'phonetic'])]
        
        silent_patterns = [r for r in recommendations 
                          if 'silent' in r.pattern_info.get('pattern_type', '') 
                          or 'augh' in r.pattern_info.get('pattern_value', '')
                          or 'gh' in r.pattern_info.get('pattern_value', '')]
        
        print(f"\n🎯 BadCase解决验证:")
        print(f"   家庭成员patterns数量: {len(family_patterns)} (期望: 1)")
        print(f"   ER相关patterns数量: {len(er_patterns)} (期望: 1)")  
        print(f"   不发音字母patterns数量: {len(silent_patterns)} (期望: ≤2)")
        
        success_metrics = {
            'total_patterns_reduced': len(recommendations) <= 4,  # 原来可能有6-8个
            'family_consolidated': len(family_patterns) <= 1,
            'er_consolidated': len(er_patterns) <= 1,
            'silent_consolidated': len(silent_patterns) <= 2,
            'cognitive_ordered': self._check_cognitive_ordering(recommendations)
        }
        
        success_rate = sum(success_metrics.values()) / len(success_metrics)
        print(f"\n📈 整合成功率: {success_rate:.1%}")
        
        return success_metrics
    
    def _check_cognitive_ordering(self, recommendations: List) -> bool:
        """检查认知层次排序是否合理"""
        levels = [r.pattern_info.get('cognitive_level', 'basic') for r in recommendations]
        level_order = {'basic': 1, 'intermediate': 2, 'advanced': 3}
        
        # 检查是否大致按认知层次排序（允许一定灵活性）
        order_scores = [level_order.get(level, 2) for level in levels]
        
        # 如果前面的总体分数不高于后面的，说明排序合理
        for i in range(len(order_scores) - 1):
            if order_scores[i] > order_scores[i + 1] + 1:  # 允许1级差异
                return False
        return True
    
    def test_concept_integrator_statistics(self):
        """测试概念整合器的统计信息"""
        print("\n📊 概念整合器统计信息:")
        
        stats = self.integrator.get_concept_statistics()
        print(f"总概念组数量: {stats['total_concept_groups']}")
        
        for group_id, info in stats['concept_groups'].items():
            print(f"  📁 {group_id}: {info['name']}")
            print(f"     - pattern数量: {info['pattern_count']}")
            print(f"     - 主要类型: {info['primary_type']}")
            print(f"     - 维度: {info['dimension']}")
            print(f"     - 认知层次: {info['cognitive_level']}")
        
        return stats
    
    def test_dynamic_pattern_detection(self):
        """测试动态pattern检测的四维度分类"""
        print("\n🔬 测试动态pattern检测...")
        
        # 测试不同proficiency用户的检测结果
        test_cases = [
            {'proficiency': 25, 'description': '初学者'},
            {'proficiency': 55, 'description': '中级学习者'},
            {'proficiency': 85, 'description': '高级学习者'}
        ]
        
        target_word = {
            'english_word': 'teacher',
            'chinese_meaning': '老师'
        }
        
        learned_words = [
            {'word_id': 1, 'english_word': 'worker', 'chinese_meaning': '工人', 'proficiency': 80.0},
            {'word_id': 2, 'english_word': 'bigger', 'chinese_meaning': '更大', 'proficiency': 60.0},
            {'word_id': 3, 'english_word': 'farmer', 'chinese_meaning': '农民', 'proficiency': 70.0}
        ]
        
        for case in test_cases:
            # 模拟用户平均水平
            for word in learned_words:
                word['proficiency'] = case['proficiency']
            
            patterns = self.recommender._detect_dynamic_patterns(target_word, learned_words)
            
            print(f"\n👤 {case['description']} (proficiency: {case['proficiency']}):")
            print(f"   检测到patterns数量: {len(patterns)}")
            
            by_dimension = {}
            by_cognitive = {}
            
            for pattern in patterns:
                dim = pattern.get('dimension_category', 'unknown')
                cog = pattern.get('cognitive_level', 'unknown')
                
                by_dimension[dim] = by_dimension.get(dim, 0) + 1
                by_cognitive[cog] = by_cognitive.get(cog, 0) + 1
            
            print(f"   维度分布: {by_dimension}")
            print(f"   认知层次分布: {by_cognitive}")
    
    def run_comprehensive_test(self):
        """运行完整的集成测试"""
        print("🚀 Pattern推荐系统整合测试")
        print("=" * 50)
        
        try:
            # 测试1: aunt案例解决
            success_metrics = self.test_aunt_case_resolution()
            
            # 测试2: 概念整合器统计
            concept_stats = self.test_concept_integrator_statistics()
            
            # 测试3: 动态检测算法
            self.test_dynamic_pattern_detection()
            
            # 总结
            print("\n" + "=" * 50)
            print("🎯 测试总结:")
            print(f"✅ 概念组整合: {concept_stats['total_concept_groups']}个概念组")
            print(f"✅ BadCase解决: {sum(success_metrics.values())}/{len(success_metrics)}项改进")
            print(f"✅ 四维度框架: 动态检测算法已适配")
            print(f"✅ 认知层次: 用户水平自适应推荐")
            
            overall_success = sum(success_metrics.values()) / len(success_metrics) >= 0.8
            print(f"\n🏆 整体测试结果: {'通过' if overall_success else '需要改进'}")
            
            return overall_success
            
        except Exception as e:
            logger.error(f"集成测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    db_path = "instance/words.db"
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return 1
    
    tester = PatternIntegrationTester(db_path)
    success = tester.run_comprehensive_test()
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())