#!/usr/bin/env python3
"""
批量图片生成脚本
基于静态资源审计结果，为缺失的单词批量生成AI图片
"""

import os
import sys
import json
import time
import sqlite3
import requests
import base64
from pathlib import Path
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class BatchImageGenerator:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / 'instance' / 'words.db'
        self.image_dir = self.project_root / 'static' / 'images' / 'words'
        self.audit_file = self.project_root / 'static_resources_audit.json'
        
        # API配置（从现有代码复制）
        self.api_base_url = "https://api.365api.shop/v1"
        self.api_key = "sk-QBBtTxLIJ4FUK62i6d2c6fAb4c9f4c3aBbF6A7073022099f"
        
        # 生成统计
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': None,
            'errors': []
        }
        
        # 锁用于线程安全的统计更新
        self.stats_lock = threading.Lock()
    
    def normalize_word_for_filename(self, word):
        """标准化单词为文件名格式"""
        return word.lower().replace(' ', '_').replace("'", "").replace('"', '')
    
    def get_word_meaning(self, english_word):
        """从数据库获取单词的中文含义"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT chinese_meaning FROM word WHERE english_word = ?",
                (english_word,)
            )
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else "相关概念"
            
        except Exception as e:
            print(f"⚠️  获取单词含义失败 {english_word}: {e}")
            return "相关概念"
    
    def generate_ai_image(self, english_word, chinese_meaning, output_path):
        """
        调用AI API生成单词学习图片
        (基于现有 _generate_ai_image 函数)
        """
        try:
            # 为少儿学习优化的图片生成提示词
            prompt = f"""Create a colorful, child-friendly, cartoon-style illustration of "{english_word}" ({chinese_meaning}).

Requirements:
- Bright and cheerful colors suitable for children aged 6-12
- Simple, clear visual elements that are easy to understand
- Educational and engaging content
- NO TEXT OR LETTERS visible in the image
- High contrast and appealing to kids
- Safe and appropriate content for children
- Cartoon/illustration style (not photographic)
- Clear visual representation of the word's meaning"""

            # API请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # API请求数据
            data = {
                "model": "dall-e-2",
                "prompt": prompt,
                "n": 1,
                "size": "512x512",
                "response_format": "b64_json"
            }

            print(f"🎨 生成图片: {english_word} ({chinese_meaning})")

            # 调用API
            response = requests.post(
                f"{self.api_base_url}/images/generations",
                headers=headers,
                json=data,
                timeout=120
            )

            if response.status_code == 200:
                response_data = response.json()

                if 'data' in response_data and len(response_data['data']) > 0:
                    image_data = response_data['data'][0]

                    if 'b64_json' in image_data:
                        # 解码base64图片数据
                        b64_data = image_data['b64_json']

                        # 清理base64数据
                        if b64_data.startswith('data:image'):
                            b64_data = b64_data.split(',')[1]

                        image_bytes = base64.b64decode(b64_data)

                        # 验证图片有效性
                        if len(image_bytes) > 5120:  # 大于5KB
                            is_valid_image = (
                                image_bytes.startswith(b'\\xff\\xd8\\xff') or  # JPEG
                                image_bytes.startswith(b'\\x89PNG') or       # PNG
                                image_bytes.startswith(b'GIF87a') or        # GIF87a
                                image_bytes.startswith(b'GIF89a') or        # GIF89a
                                image_bytes.startswith(b'RIFF')             # WebP
                            )

                            if is_valid_image:
                                # 确保目录存在
                                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                                # 保存图片文件
                                with open(output_path, 'wb') as f:
                                    f.write(image_bytes)

                                file_size = len(image_bytes)
                                print(f"✅ 生成成功: {os.path.basename(output_path)} ({file_size} 字节)")
                                return True
                            else:
                                print(f"❌ 生成的图片格式无效: {english_word}")
                                return False
                        else:
                            print(f"❌ 生成的图片文件太小: {english_word}")
                            return False
                    else:
                        print(f"❌ API响应中没有图片数据: {english_word}")
                        return False
                else:
                    print(f"❌ API响应格式错误: {english_word}")
                    return False
            else:
                print(f"❌ API调用失败 ({response.status_code}): {english_word}")
                if response.text:
                    print(f"   错误详情: {response.text[:200]}")
                return False

        except requests.exceptions.Timeout:
            print(f"⏰ API调用超时: {english_word}")
            return False
        except Exception as e:
            print(f"❌ 生成图片异常 {english_word}: {e}")
            return False

    def generate_single_image(self, word):
        """生成单个单词的图片"""
        try:
            # 获取中文含义
            chinese_meaning = self.get_word_meaning(word)
            
            # 生成文件名和路径
            filename = self.normalize_word_for_filename(word)
            output_path = self.image_dir / f"{filename}.jpg"
            
            # 检查文件是否已存在
            if output_path.exists():
                with self.stats_lock:
                    self.stats['skipped'] += 1
                print(f"⏭️  跳过已存在: {word}")
                return True
            
            # 生成图片
            success = self.generate_ai_image(word, chinese_meaning, str(output_path))
            
            # 更新统计
            with self.stats_lock:
                if success:
                    self.stats['success'] += 1
                else:
                    self.stats['failed'] += 1
                    self.stats['errors'].append(word)
            
            # API调用间隔（避免频率限制）
            time.sleep(1)
            
            return success
            
        except Exception as e:
            with self.stats_lock:
                self.stats['failed'] += 1
                self.stats['errors'].append(f"{word}: {str(e)}")
            print(f"❌ 处理单词失败 {word}: {e}")
            return False
    
    def load_missing_words(self, batch_name='batch1'):
        """从审计报告加载缺失的单词列表"""
        try:
            with open(self.audit_file, 'r', encoding='utf-8') as f:
                audit_data = json.load(f)
            
            if 'image_generation_plan' in audit_data:
                plan = audit_data['image_generation_plan']
                
                if batch_name in plan:
                    return plan[batch_name]
                else:
                    print(f"❌ 审计报告中没有找到批次: {batch_name}")
                    return []
            else:
                print("❌ 审计报告中没有图片生成计划")
                return []
                
        except FileNotFoundError:
            print(f"❌ 审计报告文件不存在: {self.audit_file}")
            print("请先运行 python scripts/audit_static_resources.py")
            return []
        except Exception as e:
            print(f"❌ 读取审计报告失败: {e}")
            return []
    
    def batch_generate(self, batch_name='batch1', max_workers=3):
        """批量生成图片"""
        print(f"🚀 开始批量生成图片 - {batch_name}")
        
        # 加载待生成的单词列表
        words = self.load_missing_words(batch_name)
        
        if not words:
            print("❌ 没有找到需要生成的单词")
            return
        
        print(f"📋 计划生成 {len(words)} 个单词的图片")
        print(f"🔧 并发线程数: {max_workers}")
        
        # 初始化统计
        self.stats['total'] = len(words)
        self.stats['start_time'] = datetime.now()
        
        # 确保输出目录存在
        self.image_dir.mkdir(parents=True, exist_ok=True)
        
        # 使用线程池并发生成
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_word = {
                executor.submit(self.generate_single_image, word): word 
                for word in words
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_word):
                word = future_to_word[future]
                try:
                    success = future.result()
                    if success:
                        print(f"✅ 完成: {word}")
                    else:
                        print(f"❌ 失败: {word}")
                except Exception as e:
                    print(f"❌ 异常: {word} - {e}")
                    with self.stats_lock:
                        self.stats['failed'] += 1
                        self.stats['errors'].append(f"{word}: {str(e)}")
                
                # 实时显示进度
                completed = self.stats['success'] + self.stats['failed'] + self.stats['skipped']
                print(f"📊 进度: {completed}/{self.stats['total']} "
                      f"(成功:{self.stats['success']}, 失败:{self.stats['failed']}, 跳过:{self.stats['skipped']})")
        
        # 生成完成报告
        self.print_summary()
        self.save_generation_log(batch_name)
    
    def print_summary(self):
        """打印生成总结"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print("\\n" + "="*80)
        print("📊 批量图片生成完成报告")
        print("="*80)
        print(f"⏱️  开始时间: {self.stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  总耗时: {duration}")
        print(f"📋 计划生成: {self.stats['total']} 个图片")
        print(f"✅ 生成成功: {self.stats['success']} 个")
        print(f"❌ 生成失败: {self.stats['failed']} 个")
        print(f"⏭️  跳过已有: {self.stats['skipped']} 个")
        
        if self.stats['total'] > 0:
            success_rate = (self.stats['success'] / self.stats['total']) * 100
            print(f"📈 成功率: {success_rate:.1f}%")
        
        if self.stats['errors']:
            print(f"\\n❌ 失败的单词:")
            for error in self.stats['errors'][:10]:  # 只显示前10个错误
                print(f"   - {error}")
            if len(self.stats['errors']) > 10:
                print(f"   ... 还有 {len(self.stats['errors']) - 10} 个错误")
        
        print("="*80)
    
    def save_generation_log(self, batch_name):
        """保存生成日志"""
        # 创建可序列化的统计数据副本
        serializable_stats = self.stats.copy()
        if 'start_time' in serializable_stats:
            serializable_stats['start_time'] = serializable_stats['start_time'].isoformat()
        
        log_data = {
            'batch_name': batch_name,
            'timestamp': datetime.now().isoformat(),
            'stats': serializable_stats,
            'duration': str(datetime.now() - self.stats['start_time'])
        }
        
        log_file = self.project_root / f'image_generation_{batch_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)
        
        print(f"📊 生成日志已保存: {log_file}")

def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    
    print("🎨 Word Learning App 批量图片生成工具")
    print(f"📁 项目路径: {project_root}")
    
    # 创建生成器
    generator = BatchImageGenerator(project_root)
    
    # 支持命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='批量生成单词图片')
    parser.add_argument('--batch', default='batch1', choices=['batch1', 'batch2', 'batch3'],
                       help='选择要生成的批次 (默认: batch1)')
    parser.add_argument('--workers', type=int, default=3,
                       help='并发线程数 (默认: 3)')
    parser.add_argument('--preview', action='store_true',
                       help='预览模式：只显示要生成的单词列表，不实际生成')
    
    args = parser.parse_args()
    
    if args.preview:
        # 预览模式
        words = generator.load_missing_words(args.batch)
        print(f"\\n📋 {args.batch} 包含 {len(words)} 个单词:")
        for i, word in enumerate(words[:20], 1):
            print(f"   {i:2d}. {word}")
        if len(words) > 20:
            print(f"   ... 还有 {len(words) - 20} 个单词")
    else:
        # 实际生成
        generator.batch_generate(args.batch, args.workers)

if __name__ == '__main__':
    main()