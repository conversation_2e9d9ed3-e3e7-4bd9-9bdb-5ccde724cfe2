#!/usr/bin/env python3
"""
Pattern推荐系统性能测试
测试推荐算法的响应时间和资源消耗
"""

import os
import sys
import time
import json
import sqlite3
import statistics
from datetime import datetime
from memory_profiler import profile
import tracemalloc

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.services.pattern.pattern_recommender import PatternRecommenderService
from src.services.pattern.concept_integrator import ConceptIntegrator
from src.config import Config

class PatternPerformanceTester:
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self.recommender = PatternRecommenderService(self.db_path)
        self.integrator = ConceptIntegrator()
        self.test_results = {}
        
    def get_test_word_ids(self, count=50):
        """获取测试用的单词ID列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id FROM word 
            ORDER BY RANDOM() 
            LIMIT ?
        """, (count,))
        
        word_ids = [row[0] for row in cursor.fetchall()]
        conn.close()
        return word_ids
    
    def test_single_recommendation_performance(self, word_id, user_proficiency=50):
        """测试单个推荐请求的性能"""
        # 开始内存跟踪
        tracemalloc.start()
        start_time = time.time()
        
        try:
            recommendations = self.recommender.get_recommendations_for_word(
                word_id, user_proficiency
            )
            
            end_time = time.time()
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            return {
                'success': True,
                'response_time': end_time - start_time,
                'memory_current': current / 1024 / 1024,  # MB
                'memory_peak': peak / 1024 / 1024,        # MB
                'recommendation_count': len(recommendations),
                'has_concept_integration': any(
                    rec.get('pattern_info', {}).get('is_concept_integrated', False)
                    for rec in recommendations
                )
            }
        except Exception as e:
            tracemalloc.stop()
            return {
                'success': False,
                'error': str(e),
                'response_time': time.time() - start_time,
                'memory_current': 0,
                'memory_peak': 0,
                'recommendation_count': 0,
                'has_concept_integration': False
            }
    
    def test_batch_performance(self, word_ids, user_proficiency=50):
        """测试批量推荐的性能"""
        print(f"🚀 测试批量推荐性能 ({len(word_ids)}个单词)...")
        
        results = []
        response_times = []
        memory_usage = []
        success_count = 0
        total_recommendations = 0
        concept_integration_count = 0
        
        for i, word_id in enumerate(word_ids):
            if i % 10 == 0:
                print(f"  处理进度: {i}/{len(word_ids)}")
            
            result = self.test_single_recommendation_performance(word_id, user_proficiency)
            results.append(result)
            
            if result['success']:
                success_count += 1
                response_times.append(result['response_time'])
                memory_usage.append(result['memory_peak'])
                total_recommendations += result['recommendation_count']
                
                if result['has_concept_integration']:
                    concept_integration_count += 1
        
        # 计算统计指标
        stats = {
            'total_tests': len(word_ids),
            'success_count': success_count,
            'success_rate': success_count / len(word_ids) * 100,
            'total_recommendations': total_recommendations,
            'avg_recommendations_per_word': total_recommendations / success_count if success_count > 0 else 0,
            'concept_integration_rate': concept_integration_count / success_count * 100 if success_count > 0 else 0
        }
        
        if response_times:
            stats.update({
                'response_time': {
                    'min': min(response_times),
                    'max': max(response_times),
                    'mean': statistics.mean(response_times),
                    'median': statistics.median(response_times),
                    'p95': sorted(response_times)[int(len(response_times) * 0.95)]
                },
                'memory_usage': {
                    'min': min(memory_usage),
                    'max': max(memory_usage),
                    'mean': statistics.mean(memory_usage),
                    'median': statistics.median(memory_usage)
                }
            })
        
        return stats, results
    
    def test_concept_integrator_performance(self):
        """测试概念整合器的性能"""
        print("🧠 测试概念整合器性能...")
        
        start_time = time.time()
        tracemalloc.start()
        
        # 获取概念统计
        concept_stats = self.integrator.get_concept_statistics()
        
        # 测试概念整合性能 - 模拟整合过程
        integration_times = []
        test_iterations = 5
        
        for i in range(test_iterations):
            pattern_start = time.time()
            
            # 模拟一次完整的概念整合过程
            static_patterns = [
                {'pattern_type': 'family_member', 'pattern_name': '家庭成员'},
                {'pattern_type': 'letter_combo_er', 'pattern_name': 'ER字母组合'}
            ]
            dynamic_patterns = [
                {'pattern_type': 'theme_family', 'pattern_name': '家庭主题'}
            ]
            
            # 调用实际的整合方法
            result = self.integrator.integrate_patterns(static_patterns, dynamic_patterns, 50.0)
            
            pattern_time = time.time() - pattern_start
            integration_times.append(pattern_time)
        
        total_time = time.time() - start_time
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        return {
            'total_time': total_time,
            'integration_times': integration_times,
            'avg_integration_time': statistics.mean(integration_times) if integration_times else 0,
            'concept_groups': concept_stats['total_concept_groups'],
            'memory_usage': {
                'current': current / 1024 / 1024,
                'peak': peak / 1024 / 1024
            }
        }
    
    def test_different_user_levels(self):
        """测试不同用户水平下的性能"""
        print("📊 测试不同用户水平的性能差异...")
        
        test_word_ids = self.get_test_word_ids(20)
        user_levels = [
            (25, "初学者"),
            (50, "中级"),
            (75, "高级")
        ]
        
        level_results = {}
        
        for proficiency, level_name in user_levels:
            print(f"  测试{level_name}水平 (proficiency: {proficiency})...")
            
            stats, _ = self.test_batch_performance(test_word_ids, proficiency)
            level_results[level_name] = stats
        
        return level_results
    
    def run_comprehensive_performance_test(self):
        """运行综合性能测试"""
        print("🎯 Pattern推荐系统性能测试")
        print("=" * 60)
        
        # 基础性能测试
        print("\n1️⃣ 基础批量推荐性能测试")
        test_word_ids = self.get_test_word_ids(30)
        basic_stats, _ = self.test_batch_performance(test_word_ids)
        
        # 概念整合器性能测试
        print("\n2️⃣ 概念整合器性能测试")
        integrator_stats = self.test_concept_integrator_performance()
        
        # 不同用户水平性能测试
        print("\n3️⃣ 不同用户水平性能测试")
        level_stats = self.test_different_user_levels()
        
        # 汇总结果
        performance_report = {
            'test_timestamp': datetime.now().isoformat(),
            'basic_performance': basic_stats,
            'integrator_performance': integrator_stats,
            'user_level_performance': level_stats
        }
        
        # 打印性能报告
        self.print_performance_report(performance_report)
        
        # 保存详细报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(project_root, f"pattern_performance_report_{timestamp}.json")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(performance_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细性能报告已保存: {report_file}")
        
        return performance_report
    
    def print_performance_report(self, report):
        """打印性能报告摘要"""
        print("\n" + "=" * 60)
        print("📈 性能测试结果摘要")
        print("=" * 60)
        
        # 基础性能
        basic = report['basic_performance']
        print(f"\n🎯 基础推荐性能:")
        print(f"  成功率: {basic['success_rate']:.1f}%")
        print(f"  平均响应时间: {basic['response_time']['mean']*1000:.1f}ms")
        print(f"  95%响应时间: {basic['response_time']['p95']*1000:.1f}ms")
        print(f"  平均内存使用: {basic['memory_usage']['mean']:.2f}MB")
        print(f"  概念整合率: {basic['concept_integration_rate']:.1f}%")
        
        # 概念整合器性能
        integrator = report['integrator_performance']
        print(f"\n🧠 概念整合器性能:")
        print(f"  概念组数量: {integrator['concept_groups']}")
        print(f"  平均整合时间: {integrator['avg_integration_time']*1000:.1f}ms")
        print(f"  内存使用: {integrator['memory_usage']['peak']:.2f}MB")
        
        # 不同用户水平对比
        print(f"\n👥 用户水平性能对比:")
        for level_name, stats in report['user_level_performance'].items():
            print(f"  {level_name}: {stats['response_time']['mean']*1000:.1f}ms (平均)")
        
        # 性能评估
        avg_response_time = basic['response_time']['mean'] * 1000
        if avg_response_time < 100:
            performance_grade = "优秀 🏆"
        elif avg_response_time < 200:
            performance_grade = "良好 ✅"
        elif avg_response_time < 500:
            performance_grade = "一般 ⚠️"
        else:
            performance_grade = "需要优化 ❌"
        
        print(f"\n🏆 整体性能评级: {performance_grade}")
        print(f"   (平均响应时间: {avg_response_time:.1f}ms)")

def main():
    """主测试函数"""
    tester = PatternPerformanceTester()
    tester.run_comprehensive_performance_test()

if __name__ == "__main__":
    main()