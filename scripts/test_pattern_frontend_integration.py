#!/usr/bin/env python3
"""
Pattern推荐系统前端集成测试
验证Phase 3的前端优化效果
"""

import os
import sys
import json
import requests
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_api_data_structure():
    """测试API返回的数据结构是否包含新字段"""
    print("🔍 测试API数据结构...")
    
    # 这里需要先启动应用然后进行测试
    # 由于没有运行的服务器，我们先检查代码结构
    
    from src.services.pattern.pattern_recommender import PatternRecommenderService
    from src.config import Config
    
    try:
        recommender = PatternRecommenderService(Config.DATABASE_PATH)
        print("✅ PatternRecommenderService初始化成功")
        
        # 检查概念整合器是否正常工作
        from src.services.pattern.concept_integrator import ConceptIntegrator
        integrator = ConceptIntegrator()
        stats = integrator.get_concept_statistics()
        
        print(f"✅ 概念整合器统计: {stats['total_concept_groups']}个概念组")
        
        return True
        
    except Exception as e:
        print(f"❌ API结构测试失败: {e}")
        return False

def test_javascript_functions():
    """测试JavaScript分组函数的逻辑"""
    print("🧪 测试JavaScript分组逻辑...")
    
    # 模拟API返回的数据
    mock_data = {
        "recommendations": [
            {
                "pattern_info": {
                    "pattern_type": "letter_combo",
                    "pattern_name": "字母组合模式",
                    "cognitive_level": "basic",
                    "dimension_category": "orthography",
                    "concept_group": "AU组合整合",
                    "is_concept_integrated": True
                },
                "recommendation_reason": "基于AU字母组合的相似性",
                "similar_words": [
                    {"word_id": 1, "english_word": "aunt", "chinese_meaning": "姑妈"}
                ]
            },
            {
                "pattern_info": {
                    "pattern_type": "theme",
                    "pattern_name": "家庭主题",
                    "cognitive_level": "intermediate", 
                    "dimension_category": "semantic",
                    "concept_group": "家庭关系整合",
                    "is_concept_integrated": True
                },
                "recommendation_reason": "基于家庭关系主题的关联",
                "similar_words": [
                    {"word_id": 2, "english_word": "uncle", "chinese_meaning": "叔叔"}
                ]
            }
        ]
    }
    
    # 验证数据结构
    expected_levels = ['basic', 'intermediate']
    expected_dimensions = ['orthography', 'semantic']
    
    actual_levels = set()
    actual_dimensions = set()
    
    for rec in mock_data['recommendations']:
        actual_levels.add(rec['pattern_info']['cognitive_level'])
        actual_dimensions.add(rec['pattern_info']['dimension_category'])
    
    if actual_levels.issubset(expected_levels):
        print("✅ 认知层次数据结构正确")
    else:
        print(f"❌ 认知层次数据结构错误: {actual_levels}")
    
    if actual_dimensions.issubset(expected_dimensions):
        print("✅ 维度分类数据结构正确")
    else:
        print(f"❌ 维度分类数据结构错误: {actual_dimensions}")
    
    return True

def test_css_classes():
    """验证CSS类是否正确定义"""
    print("🎨 测试CSS样式类...")
    
    css_file = os.path.join(project_root, 'static/css/pattern_helper.css')
    
    if not os.path.exists(css_file):
        print("❌ CSS文件不存在")
        return False
    
    with open(css_file, 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    required_classes = [
        '.cognitive-level-section',
        '.level-header',
        '.dimension-section', 
        '.dimension-header',
        '.concept-tag'
    ]
    
    missing_classes = []
    for cls in required_classes:
        if cls not in css_content:
            missing_classes.append(cls)
    
    if missing_classes:
        print(f"❌ 缺失CSS类: {missing_classes}")
        return False
    else:
        print("✅ 所有必需的CSS类都已定义")
        return True

def test_javascript_integration():
    """验证JavaScript集成"""
    print("📜 测试JavaScript集成...")
    
    js_file = os.path.join(project_root, 'static/js/pattern_helper.js')
    
    if not os.path.exists(js_file):
        print("❌ JavaScript文件不存在")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    required_functions = [
        'groupRecommendationsByLevel',
        'groupRecommendationsByDimension',
        'getCognitiveLevelIcon',
        'getDimensionIcon'
    ]
    
    missing_functions = []
    for func in required_functions:
        if func not in js_content:
            missing_functions.append(func)
    
    if missing_functions:
        print(f"❌ 缺失JavaScript函数: {missing_functions}")
        return False
    else:
        print("✅ 所有必需的JavaScript函数都已定义")
        return True

def generate_test_report():
    """生成测试报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        "test_timestamp": timestamp,
        "test_name": "Pattern推荐系统前端集成测试",
        "phase": "Phase 3 - 前端层次化优化",
        "test_results": []
    }
    
    tests = [
        ("API数据结构测试", test_api_data_structure),
        ("JavaScript逻辑测试", test_javascript_functions),
        ("CSS样式测试", test_css_classes),
        ("JavaScript集成测试", test_javascript_integration)
    ]
    
    total_tests = len(tests)
    passed_tests = 0
    
    print("🚀 开始Pattern推荐系统前端集成测试...")
    print("=" * 60)
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        try:
            result = test_func()
            if result:
                passed_tests += 1
                status = "PASS"
                print(f"✅ {test_name} - 通过")
            else:
                status = "FAIL"
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            status = "ERROR"
            print(f"💥 {test_name} - 错误: {e}")
        
        report["test_results"].append({
            "test_name": test_name,
            "status": status,
            "timestamp": datetime.now().isoformat()
        })
    
    # 计算总体结果
    success_rate = (passed_tests / total_tests) * 100
    report["summary"] = {
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "failed_tests": total_tests - passed_tests,
        "success_rate": f"{success_rate:.1f}%"
    }
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print(f"总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {total_tests - passed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    # 保存测试报告
    report_file = os.path.join(project_root, f"pattern_frontend_test_report_{timestamp}.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 测试报告已保存: {report_file}")
    
    if success_rate >= 80:
        print("🎉 测试整体通过！前端优化效果良好")
        return True
    else:
        print("⚠️ 测试存在问题，需要进一步调试")
        return False

def main():
    """主测试函数"""
    print("🧪 Pattern推荐系统 - Phase 3前端集成测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"项目路径: {project_root}")
    
    success = generate_test_report()
    
    if success:
        print("\n✅ Phase 3前端优化验证完成！")
        print("🔄 可以进入Phase 4性能测试阶段")
    else:
        print("\n❌ 测试发现问题，需要修复后重新测试")
    
    return success

if __name__ == "__main__":
    main()