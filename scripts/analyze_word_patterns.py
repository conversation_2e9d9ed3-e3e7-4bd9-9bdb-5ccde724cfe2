#!/usr/bin/env python3
"""
单词Pattern分析脚本
分析现有959个单词的各种pattern特征，生成完整统计报告
"""

import sqlite3
import re
from collections import defaultdict, Counter
from pathlib import Path
import json


class WordPatternAnalyzer:
    """单词Pattern分析器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.words = []
        self.patterns = {
            'letter_combos': defaultdict(list),
            'prefixes': defaultdict(list),
            'suffixes': defaultdict(list),
            'themes': defaultdict(list),
            'word_lengths': defaultdict(list),
            'vowel_patterns': defaultdict(list),
            'similar_words': defaultdict(list)
        }
    
    def load_words(self):
        """从数据库加载所有单词"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, english_word FROM word ORDER BY english_word")
        self.words = [(row[0], row[1].lower().strip()) for row in cursor.fetchall()]
        
        conn.close()
        print(f"已加载 {len(self.words)} 个单词")
    
    def analyze_letter_combinations(self):
        """分析字母组合pattern"""
        # 定义常见字母组合
        combos = [
            'ir', 'oo', 'ea', 'er', 'ing', 'tion', 'ly', 'ed', 
            'th', 'ch', 'sh', 'ph', 'ck', 'qu', 'ai', 'ou', 
            'igh', 'ar', 'or', 'ur', 'aw', 'oy', 'ew'
        ]
        
        for word_id, word in self.words:
            for combo in combos:
                if combo in word:
                    self.patterns['letter_combos'][combo].append((word_id, word))
        
        # 只保留包含2个以上单词的组合
        self.patterns['letter_combos'] = {
            combo: words for combo, words in self.patterns['letter_combos'].items()
            if len(words) >= 2
        }
    
    def analyze_prefixes_suffixes(self):
        """分析前缀后缀pattern"""
        # 常见前缀
        prefixes = ['un', 're', 'pre', 'dis', 'mis', 'over', 'under', 'out', 'up']
        # 常见后缀  
        suffixes = ['er', 'ly', 'ing', 'ed', 'tion', 'ness', 'ful', 'less', 'ment', 'able', 'ible']
        
        for word_id, word in self.words:
            # 检查前缀
            for prefix in prefixes:
                if word.startswith(prefix) and len(word) > len(prefix) + 2:
                    self.patterns['prefixes'][prefix].append((word_id, word))
            
            # 检查后缀
            for suffix in suffixes:
                if word.endswith(suffix) and len(word) > len(suffix) + 2:
                    self.patterns['suffixes'][suffix].append((word_id, word))
        
        # 只保留包含2个以上单词的前缀后缀
        self.patterns['prefixes'] = {
            prefix: words for prefix, words in self.patterns['prefixes'].items()
            if len(words) >= 2
        }
        self.patterns['suffixes'] = {
            suffix: words for suffix, words in self.patterns['suffixes'].items()
            if len(words) >= 2
        }
    
    def analyze_themes(self):
        """分析主题分组pattern"""
        theme_keywords = {
            'time': [
                'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday',
                'january', 'february', 'march', 'april', 'may', 'june', 
                'july', 'august', 'september', 'october', 'november', 'december',
                'morning', 'afternoon', 'evening', 'night', 'today', 'tomorrow', 'yesterday'
            ],
            'colors': [
                'red', 'blue', 'green', 'yellow', 'black', 'white', 'brown', 'pink', 
                'purple', 'orange', 'grey', 'gray'
            ],
            'body_parts': [
                'head', 'hair', 'eye', 'nose', 'mouth', 'ear', 'hand', 'arm', 
                'leg', 'foot', 'back', 'finger', 'toe'
            ],
            'clothes': [
                'shirt', 't-shirt', 'skirt', 'dress', 'jacket', 'coat', 'hat', 
                'shoes', 'socks', 'pants', 'jeans'
            ],
            'family': [
                'father', 'mother', 'brother', 'sister', 'family', 'parent', 
                'child', 'baby', 'grandfather', 'grandmother'
            ],
            'jobs': [
                'teacher', 'doctor', 'driver', 'worker', 'player', 'singer', 
                'writer', 'hairdresser', 'actor'
            ],
            'food': [
                'apple', 'banana', 'bread', 'milk', 'water', 'rice', 'meat', 
                'fish', 'cake', 'tea', 'coffee'
            ],
            'animals': [
                'cat', 'dog', 'bird', 'fish', 'horse', 'cow', 'pig', 'sheep', 
                'lion', 'tiger', 'elephant'
            ]
        }
        
        word_set = {word.lower() for _, word in self.words}
        
        for theme, keywords in theme_keywords.items():
            for word_id, word in self.words:
                if word in keywords and word in word_set:
                    self.patterns['themes'][theme].append((word_id, word))
    
    def analyze_vowel_patterns(self):
        """分析元音pattern"""
        vowel_patterns = {
            'a_e': r'\w*a\w*e\w*',  # 如: make, take, came
            'i_e': r'\w*i\w*e\w*',  # 如: like, time, five
            'o_e': r'\w*o\w*e\w*',  # 如: home, note, hope
            'double_vowels': r'\w*(aa|ee|ii|oo|uu)\w*',  # 双元音
            'vowel_clusters': r'\w*(ai|au|ea|ei|eu|ie|oa|ou|ue)\w*'  # 元音集群
        }
        
        for pattern_name, regex in vowel_patterns.items():
            pattern = re.compile(regex, re.IGNORECASE)
            for word_id, word in self.words:
                if pattern.match(word):
                    self.patterns['vowel_patterns'][pattern_name].append((word_id, word))
    
    def analyze_word_lengths(self):
        """分析单词长度分布"""
        for word_id, word in self.words:
            length = len(word)
            self.patterns['word_lengths'][length].append((word_id, word))
    
    def find_similar_words(self):
        """查找拼写相似的单词（编辑距离<=2）"""
        def levenshtein_distance(s1, s2):
            if len(s1) < len(s2):
                return levenshtein_distance(s2, s1)
            
            if len(s2) == 0:
                return len(s1)
            
            previous_row = range(len(s2) + 1)
            for i, c1 in enumerate(s1):
                current_row = [i + 1]
                for j, c2 in enumerate(s2):
                    insertions = previous_row[j + 1] + 1
                    deletions = current_row[j] + 1
                    substitutions = previous_row[j] + (c1 != c2)
                    current_row.append(min(insertions, deletions, substitutions))
                previous_row = current_row
            
            return previous_row[-1]
        
        # 查找编辑距离<=2的单词对
        for i, (id1, word1) in enumerate(self.words):
            similar_group = []
            for j, (id2, word2) in enumerate(self.words[i+1:], i+1):
                if abs(len(word1) - len(word2)) <= 2:  # 长度差异不超过2
                    distance = levenshtein_distance(word1, word2)
                    if 1 <= distance <= 2:  # 编辑距离1-2
                        similar_group.append((id2, word2, distance))
            
            if similar_group:
                self.patterns['similar_words'][word1] = similar_group
    
    def generate_report(self):
        """生成分析报告"""
        report = {
            'summary': {
                'total_words': len(self.words),
                'analysis_date': str(Path(__file__).stat().st_mtime)
            },
            'letter_combinations': {},
            'prefixes': {},
            'suffixes': {},
            'themes': {},
            'vowel_patterns': {},
            'word_length_distribution': {},
            'similar_word_pairs': {}
        }
        
        # 字母组合统计
        for combo, words in sorted(self.patterns['letter_combos'].items(), 
                                  key=lambda x: len(x[1]), reverse=True):
            report['letter_combinations'][combo] = {
                'count': len(words),
                'words': [word for _, word in words[:10]]  # 只显示前10个
            }
        
        # 前缀统计
        for prefix, words in sorted(self.patterns['prefixes'].items(), 
                                   key=lambda x: len(x[1]), reverse=True):
            report['prefixes'][prefix] = {
                'count': len(words),
                'words': [word for _, word in words]
            }
        
        # 后缀统计
        for suffix, words in sorted(self.patterns['suffixes'].items(), 
                                   key=lambda x: len(x[1]), reverse=True):
            report['suffixes'][suffix] = {
                'count': len(words),
                'words': [word for _, word in words]
            }
        
        # 主题分组统计
        for theme, words in self.patterns['themes'].items():
            if words:  # 只包含有单词的主题
                report['themes'][theme] = {
                    'count': len(words),
                    'words': [word for _, word in words]
                }
        
        # 元音pattern统计
        for pattern, words in self.patterns['vowel_patterns'].items():
            if len(words) >= 2:
                report['vowel_patterns'][pattern] = {
                    'count': len(words),
                    'words': [word for _, word in words[:10]]
                }
        
        # 单词长度分布
        for length, words in sorted(self.patterns['word_lengths'].items()):
            report['word_length_distribution'][str(length)] = len(words)
        
        # 相似单词对
        similar_pairs = 0
        for word1, similar_list in self.patterns['similar_words'].items():
            if similar_list:
                report['similar_word_pairs'][word1] = [
                    {'word': word2, 'distance': dist} 
                    for _, word2, dist in similar_list[:5]
                ]
                similar_pairs += len(similar_list)
        
        report['summary']['similar_pairs_found'] = similar_pairs
        
        return report
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始分析单词patterns...")
        
        self.load_words()
        
        print("分析字母组合...")
        self.analyze_letter_combinations()
        
        print("分析前缀后缀...")
        self.analyze_prefixes_suffixes()
        
        print("分析主题分组...")
        self.analyze_themes()
        
        print("分析元音patterns...")
        self.analyze_vowel_patterns()
        
        print("分析单词长度...")
        self.analyze_word_lengths()
        
        print("查找相似单词...")
        self.find_similar_words()
        
        print("生成报告...")
        return self.generate_report()


def main():
    """主函数"""
    db_path = "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/instance/words.db"
    
    analyzer = WordPatternAnalyzer(db_path)
    report = analyzer.run_analysis()
    
    # 保存报告
    report_path = Path(__file__).parent / "word_pattern_analysis_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析完成！报告已保存到: {report_path}")
    
    # 打印关键统计
    print(f"\n=== 分析摘要 ===")
    print(f"总单词数: {report['summary']['total_words']}")
    print(f"发现字母组合pattern: {len(report['letter_combinations'])}")
    print(f"发现前缀pattern: {len(report['prefixes'])}")
    print(f"发现后缀pattern: {len(report['suffixes'])}")
    print(f"发现主题分组: {len(report['themes'])}")
    print(f"发现相似单词对: {report['summary']['similar_pairs_found']}")
    
    # 显示top patterns
    print(f"\n=== Top 10 字母组合 ===")
    for combo, data in list(report['letter_combinations'].items())[:10]:
        print(f"{combo}: {data['count']}个单词 - {data['words'][:5]}")
    
    print(f"\n=== 主题分组 ===")
    for theme, data in report['themes'].items():
        print(f"{theme}: {data['count']}个单词 - {data['words']}")


if __name__ == "__main__":
    main()