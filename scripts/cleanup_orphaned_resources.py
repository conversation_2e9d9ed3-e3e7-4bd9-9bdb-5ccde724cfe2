#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
孤立资源文件清理脚本
清理473个多余资源文件，完善MECE合规性

功能：
1. 识别所有孤立的资源文件
2. 安全备份后删除多余文件
3. 智能保留可能有用的资源
4. 生成清理报告和恢复指令
5. 支持干运行模式预览清理结果
"""

import os
import sys
import sqlite3
import shutil
import json
import asyncio
import aiofiles
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Set
import hashlib

# 添加项目根目录到路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

class OrphanedResourceCleaner:
    """孤立资源清理器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / "instance" / "words.db"
        self.audio_dir = self.project_root / "static" / "audio" / "words"
        self.text_dir = self.project_root / "static" / "cache" / "memory_help"
        
        # 备份和日志目录
        self.backup_dir = self.project_root / "backups" / f"orphaned_cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.log_file = self.project_root / "logs" / "orphaned_cleanup.log"
        self.report_file = self.project_root / "logs" / "cleanup_report.json"
        
        # 确保目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 清理统计
        self.cleanup_stats = {
            'start_time': None,
            'end_time': None,
            'orphaned_audio_found': 0,
            'orphaned_text_found': 0,
            'audio_backed_up': 0,
            'text_backed_up': 0,
            'audio_deleted': 0,
            'text_deleted': 0,
            'audio_preserved': 0,
            'text_preserved': 0,
            'space_saved_mb': 0,
            'errors': []
        }
        
        # 保留规则 - 可能有用的文件模式
        self.preserve_patterns = {
            'audio': [
                r'.*_backup_.*',      # 备份文件
                r'.*_original_.*',    # 原始文件
                r'.*_high_quality_.*', # 高质量版本
                r'.*_alternative_.*', # 替代版本
            ],
            'text': [
                r'.*_template_.*',    # 模板文件
                r'.*_backup_.*',      # 备份文件
                r'.*_draft_.*',       # 草稿文件
                r'.*_enhanced_.*',    # 增强版本
            ]
        }
    
    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception:
            pass
        
        print(f"{log_entry.strip()}")
    
    def get_standardized_filename(self, word_id: int, file_type: str) -> str:
        """获取标准化文件名"""
        extension = '.mp3' if file_type == 'audio' else '.txt'
        return f"word_{word_id:04d}{extension}"
    
    async def get_expected_files(self) -> Tuple[Set[str], Set[str]]:
        """获取预期的文件集合"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT id FROM word ORDER BY id")
            word_ids = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            expected_audio = {self.get_standardized_filename(word_id, 'audio') for word_id in word_ids}
            expected_text = {self.get_standardized_filename(word_id, 'text') for word_id in word_ids}
            
            self.log_message(f"✅ 预期文件: 音频 {len(expected_audio)} 个，文本 {len(expected_text)} 个")
            
            return expected_audio, expected_text
            
        except Exception as e:
            self.log_message(f"❌ 获取预期文件失败: {e}", "ERROR")
            return set(), set()
    
    async def scan_actual_files(self) -> Tuple[Set[str], Set[str]]:
        """扫描实际存在的文件"""
        actual_audio = set()
        actual_text = set()
        
        # 扫描音频文件
        if self.audio_dir.exists():
            for file_path in self.audio_dir.glob("*.mp3"):
                actual_audio.add(file_path.name)
        
        # 扫描文本文件
        if self.text_dir.exists():
            for file_path in self.text_dir.glob("*.txt"):
                actual_text.add(file_path.name)
        
        self.log_message(f"✅ 实际文件: 音频 {len(actual_audio)} 个，文本 {len(actual_text)} 个")
        
        return actual_audio, actual_text
    
    def should_preserve_file(self, filename: str, file_type: str) -> Tuple[bool, str]:
        """判断文件是否应该保留"""
        import re
        
        patterns = self.preserve_patterns.get(file_type, [])
        
        for pattern in patterns:
            if re.match(pattern, filename, re.IGNORECASE):
                return True, f"匹配保留规则: {pattern}"
        
        # 检查文件是否包含有价值的内容标识
        valuable_indicators = {
            'audio': ['high', 'quality', 'original', 'master', 'enhanced'],
            'text': ['template', 'enhanced', 'detailed', 'complete', 'master']
        }
        
        indicators = valuable_indicators.get(file_type, [])
        filename_lower = filename.lower()
        
        for indicator in indicators:
            if indicator in filename_lower:
                return True, f"包含有价值标识: {indicator}"
        
        return False, "无特殊价值"
    
    async def analyze_file_content(self, file_path: Path) -> Dict[str, any]:
        """分析文件内容以判断价值"""
        analysis = {
            'size_bytes': 0,
            'is_empty': True,
            'content_hash': '',
            'estimated_value': 'low'
        }
        
        try:
            if not file_path.exists():
                return analysis
            
            file_stat = file_path.stat()
            analysis['size_bytes'] = file_stat.st_size
            
            if file_stat.st_size == 0:
                analysis['is_empty'] = True
                analysis['estimated_value'] = 'none'
                return analysis
            
            analysis['is_empty'] = False
            
            # 生成文件内容哈希
            hasher = hashlib.md5()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            analysis['content_hash'] = hasher.hexdigest()
            
            # 根据文件大小和类型估算价值
            if file_path.suffix == '.mp3':
                if file_stat.st_size > 50000:  # 大于50KB的音频可能有价值
                    analysis['estimated_value'] = 'medium'
                elif file_stat.st_size > 10000:  # 大于10KB的音频基本价值
                    analysis['estimated_value'] = 'low'
                else:
                    analysis['estimated_value'] = 'none'
            else:  # 文本文件
                if file_stat.st_size > 1000:  # 大于1KB的文本可能有价值
                    analysis['estimated_value'] = 'medium'
                elif file_stat.st_size > 100:  # 大于100字节的文本基本价值
                    analysis['estimated_value'] = 'low'
                else:
                    analysis['estimated_value'] = 'none'
                    
        except Exception as e:
            self.log_message(f"⚠️ 分析文件失败 {file_path}: {e}", "WARN")
            
        return analysis
    
    async def identify_orphaned_files(self) -> Tuple[List[Dict], List[Dict]]:
        """识别孤立文件并分析其价值"""
        expected_audio, expected_text = await self.get_expected_files()
        actual_audio, actual_text = await self.scan_actual_files()
        
        # 找出孤立文件
        orphaned_audio_names = actual_audio - expected_audio
        orphaned_text_names = actual_text - expected_text
        
        self.cleanup_stats['orphaned_audio_found'] = len(orphaned_audio_names)
        self.cleanup_stats['orphaned_text_found'] = len(orphaned_text_names)
        
        self.log_message(f"🔍 发现孤立文件: 音频 {len(orphaned_audio_names)} 个，文本 {len(orphaned_text_names)} 个")
        
        # 分析孤立的音频文件
        orphaned_audio = []
        for filename in orphaned_audio_names:
            file_path = self.audio_dir / filename
            should_preserve, preserve_reason = self.should_preserve_file(filename, 'audio')
            content_analysis = await self.analyze_file_content(file_path)
            
            orphaned_audio.append({
                'filename': filename,
                'path': str(file_path),
                'should_preserve': should_preserve,
                'preserve_reason': preserve_reason,
                'content_analysis': content_analysis,
                'recommended_action': 'preserve' if should_preserve or content_analysis['estimated_value'] in ['medium', 'high'] else 'delete'
            })
        
        # 分析孤立的文本文件
        orphaned_text = []
        for filename in orphaned_text_names:
            file_path = self.text_dir / filename
            should_preserve, preserve_reason = self.should_preserve_file(filename, 'text')
            content_analysis = await self.analyze_file_content(file_path)
            
            orphaned_text.append({
                'filename': filename,
                'path': str(file_path),
                'should_preserve': should_preserve,
                'preserve_reason': preserve_reason,
                'content_analysis': content_analysis,
                'recommended_action': 'preserve' if should_preserve or content_analysis['estimated_value'] in ['medium', 'high'] else 'delete'
            })
        
        return orphaned_audio, orphaned_text
    
    async def backup_file(self, source_path: str, relative_path: str) -> bool:
        """备份文件到备份目录"""
        try:
            source = Path(source_path)
            backup_target = self.backup_dir / relative_path
            backup_target.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(source, backup_target)
            return True
            
        except Exception as e:
            self.log_message(f"❌ 备份文件失败 {source_path}: {e}", "ERROR")
            self.cleanup_stats['errors'].append(f"备份失败: {source_path}")
            return False
    
    async def safe_delete_file(self, file_path: str, backup_relative_path: str) -> bool:
        """安全删除文件（先备份再删除）"""
        try:
            # 先备份
            if not await self.backup_file(file_path, backup_relative_path):
                return False
            
            # 删除原文件
            Path(file_path).unlink()
            return True
            
        except Exception as e:
            self.log_message(f"❌ 删除文件失败 {file_path}: {e}", "ERROR")
            self.cleanup_stats['errors'].append(f"删除失败: {file_path}")
            return False
    
    async def preserve_valuable_file(self, file_path: str, backup_relative_path: str) -> bool:
        """保留有价值的文件（仅备份，不删除）"""
        try:
            # 备份到preserved目录
            preserved_path = f"preserved/{backup_relative_path}"
            if await self.backup_file(file_path, preserved_path):
                self.log_message(f"💎 保留有价值文件: {Path(file_path).name}")
                return True
            return False
            
        except Exception as e:
            self.log_message(f"❌ 保留文件失败 {file_path}: {e}", "ERROR")
            return False
    
    async def execute_cleanup(self, orphaned_audio: List[Dict], orphaned_text: List[Dict], dry_run: bool = False) -> Dict[str, int]:
        """执行清理操作"""
        if dry_run:
            self.log_message("🔍 执行干运行模式 - 仅预览清理结果，不实际删除文件")
        else:
            self.log_message("🚀 开始执行实际清理操作")
        
        results = {
            'audio_deleted': 0,
            'text_deleted': 0,
            'audio_preserved': 0,
            'text_preserved': 0,
            'space_saved_bytes': 0
        }
        
        # 处理孤立音频文件
        for item in orphaned_audio:
            filename = item['filename']
            file_path = item['path']
            action = item['recommended_action']
            size_bytes = item['content_analysis']['size_bytes']
            
            if action == 'preserve':
                if not dry_run:
                    if await self.preserve_valuable_file(file_path, f"audio/{filename}"):
                        results['audio_preserved'] += 1
                        self.cleanup_stats['audio_preserved'] += 1
                else:
                    results['audio_preserved'] += 1
                    self.log_message(f"📋 [预览] 将保留音频: {filename}")
            else:
                if not dry_run:
                    if await self.safe_delete_file(file_path, f"audio/{filename}"):
                        results['audio_deleted'] += 1
                        results['space_saved_bytes'] += size_bytes
                        self.cleanup_stats['audio_deleted'] += 1
                        self.cleanup_stats['audio_backed_up'] += 1
                        self.log_message(f"🗑️ 删除音频: {filename} ({size_bytes/1024:.1f} KB)")
                else:
                    results['audio_deleted'] += 1
                    results['space_saved_bytes'] += size_bytes
                    self.log_message(f"📋 [预览] 将删除音频: {filename} ({size_bytes/1024:.1f} KB)")
        
        # 处理孤立文本文件
        for item in orphaned_text:
            filename = item['filename']
            file_path = item['path']
            action = item['recommended_action']
            size_bytes = item['content_analysis']['size_bytes']
            
            if action == 'preserve':
                if not dry_run:
                    if await self.preserve_valuable_file(file_path, f"text/{filename}"):
                        results['text_preserved'] += 1
                        self.cleanup_stats['text_preserved'] += 1
                else:
                    results['text_preserved'] += 1
                    self.log_message(f"📋 [预览] 将保留文本: {filename}")
            else:
                if not dry_run:
                    if await self.safe_delete_file(file_path, f"text/{filename}"):
                        results['text_deleted'] += 1
                        results['space_saved_bytes'] += size_bytes
                        self.cleanup_stats['text_deleted'] += 1
                        self.cleanup_stats['text_backed_up'] += 1
                        self.log_message(f"🗑️ 删除文本: {filename} ({size_bytes} bytes)")
                else:
                    results['text_deleted'] += 1
                    results['space_saved_bytes'] += size_bytes
                    self.log_message(f"📋 [预览] 将删除文本: {filename} ({size_bytes} bytes)")
        
        if not dry_run:
            self.cleanup_stats['space_saved_mb'] = results['space_saved_bytes'] / (1024 * 1024)
        
        return results
    
    async def generate_cleanup_report(self, orphaned_audio: List[Dict], orphaned_text: List[Dict], 
                                    cleanup_results: Dict[str, int], dry_run: bool = False):
        """生成清理报告"""
        report = {
            'cleanup_session': {
                'timestamp': datetime.now().isoformat(),
                'mode': 'dry_run' if dry_run else 'execution',
                'backup_directory': str(self.backup_dir) if not dry_run else None
            },
            'discovery': {
                'orphaned_audio_found': len(orphaned_audio),
                'orphaned_text_found': len(orphaned_text),
                'total_orphaned_files': len(orphaned_audio) + len(orphaned_text)
            },
            'analysis': {
                'audio_by_action': {
                    'delete': len([f for f in orphaned_audio if f['recommended_action'] == 'delete']),
                    'preserve': len([f for f in orphaned_audio if f['recommended_action'] == 'preserve'])
                },
                'text_by_action': {
                    'delete': len([f for f in orphaned_text if f['recommended_action'] == 'delete']),
                    'preserve': len([f for f in orphaned_text if f['recommended_action'] == 'preserve'])
                }
            },
            'execution_results': cleanup_results,
            'detailed_files': {
                'orphaned_audio': orphaned_audio,
                'orphaned_text': orphaned_text
            },
            'recovery_instructions': self._generate_recovery_instructions() if not dry_run else None,
            'statistics': self.cleanup_stats
        }
        
        try:
            async with aiofiles.open(self.report_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(report, ensure_ascii=False, indent=2))
            
            self.log_message(f"📊 清理报告已生成: {self.report_file}")
            
        except Exception as e:
            self.log_message(f"❌ 生成报告失败: {e}", "ERROR")
    
    def _generate_recovery_instructions(self) -> Dict[str, any]:
        """生成恢复指令"""
        return {
            'backup_location': str(self.backup_dir),
            'restore_commands': {
                'restore_all_audio': f"cp -r {self.backup_dir}/audio/* {self.audio_dir}/",
                'restore_all_text': f"cp -r {self.backup_dir}/text/* {self.text_dir}/",
                'restore_preserved_only': f"cp -r {self.backup_dir}/preserved/* {self.project_root}/static/"
            },
            'verification_command': f"python {script_dir}/resource_audit_monitor.py",
            'notes': [
                "备份文件将保留30天，之后可能被自动清理",
                "恢复操作前建议先运行审计脚本检查当前状态",
                "preserved目录包含被判定为有价值的文件"
            ]
        }
    
    async def print_summary(self, orphaned_audio: List[Dict], orphaned_text: List[Dict], 
                          cleanup_results: Dict[str, int], dry_run: bool = False):
        """打印清理摘要"""
        mode_text = "预览模式" if dry_run else "执行模式"
        print(f"\n" + "="*60)
        print(f"🧹 孤立资源清理报告 - {mode_text}")
        print(f"="*60)
        
        print(f"\n🔍 发现统计:")
        print(f"  孤立音频文件: {len(orphaned_audio)}")
        print(f"  孤立文本文件: {len(orphaned_text)}")
        print(f"  总计孤立文件: {len(orphaned_audio) + len(orphaned_text)}")
        
        print(f"\n📊 处理结果:")
        print(f"  音频文件删除: {cleanup_results['audio_deleted']}")
        print(f"  文本文件删除: {cleanup_results['text_deleted']}")
        print(f"  音频文件保留: {cleanup_results['audio_preserved']}")
        print(f"  文本文件保留: {cleanup_results['text_preserved']}")
        
        space_saved_mb = cleanup_results['space_saved_bytes'] / (1024 * 1024)
        print(f"  节省空间: {space_saved_mb:.2f} MB")
        
        if not dry_run:
            print(f"\n💾 备份信息:")
            print(f"  备份目录: {self.backup_dir}")
            print(f"  备份文件数: {self.cleanup_stats['audio_backed_up'] + self.cleanup_stats['text_backed_up']}")
        
        print(f"\n📁 输出文件:")
        print(f"  清理日志: {self.log_file}")
        print(f"  详细报告: {self.report_file}")
        
        if self.cleanup_stats['errors']:
            print(f"\n❌ 错误 ({len(self.cleanup_stats['errors'])}):")
            for error in self.cleanup_stats['errors'][:5]:  # 只显示前5个错误
                print(f"  {error}")

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="清理孤立的静态资源文件")
    parser.add_argument('--dry-run', action='store_true', help='仅预览清理结果，不实际删除文件')
    parser.add_argument('--force', action='store_true', help='强制执行清理，不询问确认')
    
    args = parser.parse_args()
    
    print("🧹 孤立资源清理脚本启动")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    project_root = Path(__file__).parent.parent
    cleaner = OrphanedResourceCleaner(str(project_root))
    
    # 检查必要条件
    if not cleaner.db_path.exists():
        print(f"❌ 数据库文件不存在: {cleaner.db_path}")
        return 1
    
    try:
        cleaner.cleanup_stats['start_time'] = datetime.now().isoformat()
        
        # 识别孤立文件
        orphaned_audio, orphaned_text = await cleaner.identify_orphaned_files()
        
        if not orphaned_audio and not orphaned_text:
            print("✅ 没有发现孤立文件，无需清理")
            return 0
        
        # 如果不是干运行且不是强制模式，询问确认
        if not args.dry_run and not args.force:
            total_files = len(orphaned_audio) + len(orphaned_text)
            to_delete = len([f for f in orphaned_audio if f['recommended_action'] == 'delete']) + \
                       len([f for f in orphaned_text if f['recommended_action'] == 'delete'])
            
            print(f"\n⚠️ 将要删除 {to_delete} 个文件（共发现 {total_files} 个孤立文件）")
            print(f"备份目录: {cleaner.backup_dir}")
            
            confirm = input("确认执行清理操作吗？(y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ 用户取消操作")
                return 0
        
        # 执行清理
        cleanup_results = await cleaner.execute_cleanup(orphaned_audio, orphaned_text, args.dry_run)
        
        # 生成报告
        await cleaner.generate_cleanup_report(orphaned_audio, orphaned_text, cleanup_results, args.dry_run)
        
        # 打印摘要
        await cleaner.print_summary(orphaned_audio, orphaned_text, cleanup_results, args.dry_run)
        
        cleaner.cleanup_stats['end_time'] = datetime.now().isoformat()
        
        if args.dry_run:
            print(f"\n✅ 干运行完成! 使用 --force 参数执行实际清理")
        else:
            print(f"\n✅ 孤立资源清理完成!")
        
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))