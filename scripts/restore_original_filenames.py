#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恢复原始英文文件名脚本
将所有标准化的 word_XXXX 格式文件名恢复为原始英文文件名
修复因标准化命名导致的应用访问问题
"""
import os
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple


class FilenameRestoreService:
    """文件名恢复服务"""

    def __init__(self, project_root: str, dry_run: bool = True):
        self.project_root = Path(project_root)
        self.dry_run = dry_run
        self.backup_root = self.project_root / "backups"
        
        # 关键目录
        self.audio_dir = self.project_root / "static" / "audio" / "words"
        self.text_dir = self.project_root / "static" / "cache" / "memory_help"
        self.images_dir = self.project_root / "static" / "images" / "words"
        
        # 日志文件路径
        self.log_files = {
            'resource_mapping': self.backup_root / "resource_backup_20250725_215322" / "resource_mapping_log.json",
            'image_cleanup': None  # 需要查找最新的图片清理日志
        }
        
        self._find_image_cleanup_log()
        
    def _find_image_cleanup_log(self):
        """查找最新的图片清理日志文件"""
        possible_dirs = [
            self.backup_root / "image_cleanup_20250725_220159",
            self.backup_root / "image_cleanup_20250725_220216"
        ]
        
        for backup_dir in possible_dirs:
            log_file = backup_dir / "rename_log.json"
            if log_file.exists():
                self.log_files['image_cleanup'] = log_file
                print(f"✅ 找到图片清理日志: {log_file}")
                break
        
        if not self.log_files['image_cleanup']:
            print("⚠️ 未找到图片清理日志，将尝试从文件系统推断映射关系")
    
    def load_restoration_mappings(self) -> Dict[str, List[Tuple[str, str, str]]]:
        """
        加载恢复映射关系
        
        Returns:
            Dict: {'audio': [(old_path, new_path, word)], 'text': [...], 'images': [...]}
        """
        mappings = {
            'audio': [],
            'text': [],
            'images': []
        }
        
        # 1. 从资源映射日志加载音频和文本文件映射
        if self.log_files['resource_mapping'].exists():
            with open(self.log_files['resource_mapping'], 'r', encoding='utf-8') as f:
                resource_log = json.load(f)
            
            for entry in resource_log:
                file_type = entry['file_type']
                if file_type in ['audio', 'text']:
                    # 反向映射：从 new_filename 恢复到 old_filename
                    old_path = entry['old_path']
                    new_path = entry['new_path']
                    word = entry['word']
                    
                    # 当前文件应该是 new_path，需要恢复为 old_path
                    mappings[file_type].append((new_path, old_path, word))
        
        # 2. 从图片清理日志加载图片文件映射
        if self.log_files['image_cleanup'] and self.log_files['image_cleanup'].exists():
            with open(self.log_files['image_cleanup'], 'r', encoding='utf-8') as f:
                image_log = json.load(f)
            
            for entry in image_log:
                old_path = entry['old_path']
                new_path = entry['new_path'] 
                word = entry.get('word', 'unknown')
                
                # 当前文件应该是 new_path，需要恢复为 old_path
                mappings['images'].append((new_path, old_path, word))
        else:
            # 如果没有图片清理日志，尝试从文件系统推断
            mappings['images'] = self._infer_image_mappings()
        
        return mappings
    
    def _infer_image_mappings(self) -> List[Tuple[str, str, str]]:
        """从文件系统推断图片映射关系"""
        mappings = []
        
        if not self.images_dir.exists():
            return mappings
        
        # 查找所有 word_XXXX.jpg 格式的文件
        for image_file in self.images_dir.glob("word_*.jpg"):
            # 尝试从数据库获取对应的英文单词
            try:
                # 从文件名提取word_id
                word_id = int(image_file.stem.split('_')[1])
                
                # 这里需要查询数据库获取英文单词，简化处理
                # 实际应用中需要数据库连接
                english_word = f"word_{word_id}"  # 临时占位符
                
                old_path = str(self.images_dir / f"{english_word}.jpg")
                new_path = str(image_file)
                
                mappings.append((new_path, old_path, english_word))
                
            except (ValueError, IndexError):
                continue
        
        return mappings
    
    def execute_restoration(self) -> Dict[str, any]:
        """执行文件名恢复操作"""
        print(f"{'🔍 DRY RUN: ' if self.dry_run else '🔄 '}开始文件名恢复操作...")
        
        # 加载映射关系
        mappings = self.load_restoration_mappings()
        
        results = {
            'audio_restored': 0,
            'text_restored': 0,
            'images_restored': 0,
            'errors': [],
            'skipped': [],
            'total_operations': 0
        }
        
        # 执行恢复操作
        for file_type, file_mappings in mappings.items():
            for current_path, target_path, word in file_mappings:
                results['total_operations'] += 1
                
                try:
                    self._restore_single_file(current_path, target_path, word, file_type, results)
                except Exception as e:
                    error_msg = f"恢复失败 {current_path} -> {target_path}: {str(e)}"
                    results['errors'].append(error_msg)
                    print(f"❌ {error_msg}")
        
        # 打印结果摘要
        self._print_results_summary(results)
        
        return results
    
    def _restore_single_file(self, current_path: str, target_path: str, word: str, 
                           file_type: str, results: Dict):
        """恢复单个文件"""
        current_file = Path(current_path)
        target_file = Path(target_path)
        
        # 检查当前文件是否存在
        if not current_file.exists():
            skip_msg = f"当前文件不存在: {current_path}"
            results['skipped'].append(skip_msg)
            print(f"⚠️ {skip_msg}")
            return
        
        # 检查目标文件是否已存在
        if target_file.exists():
            skip_msg = f"目标文件已存在: {target_path}"
            results['skipped'].append(skip_msg)
            print(f"⚠️ {skip_msg}")
            return
        
        # 确保目标目录存在
        target_file.parent.mkdir(parents=True, exist_ok=True)
        
        if self.dry_run:
            print(f"🔍 DRY RUN: 将恢复 {current_file.name} -> {target_file.name} (单词: {word})")
        else:
            # 执行重命名
            shutil.move(str(current_file), str(target_file))
            print(f"✅ 已恢复: {current_file.name} -> {target_file.name} (单词: {word})")
        
        # 更新计数
        results[f'{file_type}_restored'] += 1
    
    def _print_results_summary(self, results: Dict):
        """打印结果摘要"""
        print("\n" + "="*60)
        print(f"{'DRY RUN ' if self.dry_run else ''}文件名恢复操作完成")
        print("="*60)
        print(f"📊 总操作数: {results['total_operations']}")
        print(f"🎵 音频文件恢复: {results['audio_restored']}")
        print(f"📝 文本文件恢复: {results['text_restored']}")
        print(f"🖼️ 图片文件恢复: {results['images_restored']}")
        print(f"⚠️ 跳过的文件: {len(results['skipped'])}")
        print(f"❌ 错误数量: {len(results['errors'])}")
        
        if results['errors']:
            print("\n❌ 错误详情:")
            for error in results['errors']:
                print(f"  - {error}")
        
        if results['skipped']:
            print(f"\n⚠️ 跳过的文件 (前10个):")
            for skip in results['skipped'][:10]:
                print(f"  - {skip}")
            if len(results['skipped']) > 10:
                print(f"  - ... 还有 {len(results['skipped']) - 10} 个文件被跳过")
    
    def create_restoration_backup(self) -> str:
        """在恢复前创建备份"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.backup_root / f"pre_restoration_backup_{timestamp}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份当前状态
        current_files = []
        
        for directory in [self.audio_dir, self.text_dir, self.images_dir]:
            if directory.exists():
                for file_path in directory.rglob("word_*.mp3"):
                    current_files.append(str(file_path))
                for file_path in directory.rglob("word_*.txt"):
                    current_files.append(str(file_path))
                for file_path in directory.rglob("word_*.jpg"):
                    current_files.append(str(file_path))
        
        # 保存当前文件列表
        backup_log = backup_dir / "current_files_log.json"
        with open(backup_log, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': timestamp,
                'total_files': len(current_files),
                'files': current_files
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已创建恢复前备份: {backup_dir}")
        return str(backup_dir)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='恢复原始英文文件名')
    parser.add_argument('--force', action='store_true', 
                       help='执行实际恢复操作（默认为dry run）')
    parser.add_argument('--project-root', type=str, 
                       default='/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod',
                       help='项目根目录路径')
    
    args = parser.parse_args()
    
    # 初始化恢复服务
    restore_service = FilenameRestoreService(
        project_root=args.project_root,
        dry_run=not args.force
    )
    
    try:
        # 创建恢复前备份
        if not restore_service.dry_run:
            restore_service.create_restoration_backup()
        
        # 执行恢复操作
        results = restore_service.execute_restoration()
        
        if restore_service.dry_run:
            print(f"\n🔍 这是DRY RUN模式。如要执行实际恢复，请使用 --force 参数")
        else:
            print(f"\n✅ 文件名恢复完成！应用现在应该能够正常访问资源文件。")
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"\n❌ 恢复操作失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()