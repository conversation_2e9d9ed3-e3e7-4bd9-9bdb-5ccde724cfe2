#!/usr/bin/env python3
"""
最终验证脚本：确认不重不漏状态
"""

import json
import sqlite3
from pathlib import Path
import sys

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.resource_finder import ResourceFinder

def get_database_words():
    """从数据库获取所有单词"""
    db_path = project_root / 'instance' / 'words.db'
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT english_word FROM word ORDER BY english_word")
        words = [row[0] for row in cursor.fetchall()]
        conn.close()
        return words
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return []

def normalize_word_for_filename(word: str) -> str:
    """标准化单词用于文件名 - 独立版本"""
    if not word:
        return ""
    
    normalized = word.lower()
    
    # 特殊处理一些包含特殊字符的单词
    if 'a.m.' in normalized or 'p.m.' in normalized:
        normalized = normalized.replace('a.m./p.m.', 'a_m_p_m')
        normalized = normalized.replace('a.m.', 'a_m')
        normalized = normalized.replace('p.m.', 'p_m')
    
    # 替换空格为下划线
    normalized = normalized.replace(' ', '_')
    
    # 移除单引号
    normalized = normalized.replace("'", "")
    
    # 替换斜杠为下划线
    normalized = normalized.replace('/', '_')
    
    # 替换连字符为下划线
    normalized = normalized.replace('-', '_')
    
    # 移除圆括号
    normalized = normalized.replace('(', '').replace(')', '')
    
    # 移除点号
    normalized = normalized.replace('.', '')
    
    # 确保不以下划线开头或结尾，并合并多个下划线
    normalized = '_'.join(filter(None, normalized.split('_')))
    
    return normalized

def final_verification():
    """最终验证不重不漏状态"""
    print("🔍 最终验证：不重不漏状态检查")
    print("="*60)
    
    # 获取数据库单词
    db_words = get_database_words()
    if not db_words:
        print("❌ 无法获取数据库单词")
        return False
    
    print(f"📚 数据库单词总数: {len(db_words)}")
    
    # 检查资源目录
    audio_dir = project_root / 'static' / 'audio' / 'words'
    image_dir = project_root / 'static' / 'images' / 'words'
    
    print(f"📁 音频目录: {audio_dir.exists()} | 文件数: {len(list(audio_dir.glob('*.mp3'))) if audio_dir.exists() else 0}")
    print(f"📁 图片目录: {image_dir.exists()} | 文件数: {len(list(image_dir.glob('*.jpg'))) if image_dir.exists() else 0}")
    
    # 使用直接文件检查方法
    missing_audio = []
    missing_images = []
    found_audio = []
    found_images = []
    
    print(f"\n🔍 逐个检查所有 {len(db_words)} 个单词...")
    
    for i, word in enumerate(db_words):
        if i % 100 == 0:
            print(f"   进度: {i}/{len(db_words)} ({i/len(db_words)*100:.1f}%)")
        
        # 检查音频 - 使用直接文件检查
        normalized = normalize_word_for_filename(word)
        audio_file = audio_dir / f"{normalized}.mp3"
        if audio_file.exists():
            found_audio.append(word)
        else:
            missing_audio.append(word)
        
        # 检查图片 - 使用直接文件检查
        image_file = image_dir / f"{normalized}.jpg"
        if image_file.exists():
            found_images.append(word)
        else:
            missing_images.append(word)
    
    # 统计结果
    audio_coverage = len(found_audio) / len(db_words) * 100
    image_coverage = len(found_images) / len(db_words) * 100
    
    print(f"\n📊 最终统计:")
    print(f"   数据库单词: {len(db_words)}")
    print(f"   音频覆盖: {len(found_audio)}/{len(db_words)} ({audio_coverage:.1f}%)")
    print(f"   图片覆盖: {len(found_images)}/{len(db_words)} ({image_coverage:.1f}%)")
    print(f"   缺失音频: {len(missing_audio)}")
    print(f"   缺失图片: {len(missing_images)}")
    
    # 显示缺失项目
    if missing_audio:
        print(f"\n❌ 缺失音频文件 ({len(missing_audio)} 个):")
        for word in missing_audio[:20]:
            print(f"   - {word}")
        if len(missing_audio) > 20:
            print(f"   ... 还有 {len(missing_audio) - 20} 个")
    
    if missing_images:
        print(f"\n❌ 缺失图片文件 ({len(missing_images)} 个):")
        for word in missing_images[:20]:
            print(f"   - {word}")
        if len(missing_images) > 20:
            print(f"   ... 还有 {len(missing_images) - 20} 个")
    
    # 不重不漏判定
    is_complete = len(missing_audio) == 0 and len(missing_images) == 0
    
    print(f"\n🎯 不重不漏状态: ", end="")
    if is_complete:
        print("🎉 完美达成!")
        print("✅ 所有数据库单词都有对应的音频和图片文件")
    else:
        print("❌ 仍有遗漏")
        print(f"⚠️  需要补充: {len(missing_audio)} 个音频, {len(missing_images)} 个图片")
    
    # 保存详细报告
    report = {
        "timestamp": "2025-07-26",
        "database_words": len(db_words),
        "audio_found": len(found_audio),
        "image_found": len(found_images),
        "audio_missing": len(missing_audio),
        "image_missing": len(missing_images),
        "audio_coverage": f"{audio_coverage:.1f}%",
        "image_coverage": f"{image_coverage:.1f}%",
        "is_complete": is_complete,
        "missing_audio_words": missing_audio,
        "missing_image_words": missing_images
    }
    
    report_file = project_root / 'final_verification_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存: {report_file}")
    
    return is_complete

if __name__ == '__main__':
    final_verification()