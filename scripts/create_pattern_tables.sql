-- 单词Pattern功能数据表创建脚本
-- 支持基于现有单词库的pattern关联功能

-- 1. word_patterns表 - 存储pattern规则定义
CREATE TABLE IF NOT EXISTS word_patterns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pattern_type VARCHAR(50) NOT NULL,     -- pattern类型: 'letter_combo', 'prefix', 'suffix', 'theme'
    pattern_value VARCHAR(100) NOT NULL,   -- pattern值: 'ir', 'un-', '-er', 'colors'
    pattern_name VARCHAR(100),             -- pattern友好名称: '含ir字母组合', '颜色类'
    description TEXT,                      -- pattern描述和学习价值说明
    word_count INTEGER DEFAULT 0,         -- 包含此pattern的单词数量
    priority_level INTEGER DEFAULT 1,     -- 优先级(1-5, 5最高): 用于推荐排序
    is_active BOOLEAN DEFAULT 1,          -- 是否激活: 支持动态开关
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    UNIQUE(pattern_type, pattern_value),   -- 防止重复pattern
    CHECK(priority_level >= 1 AND priority_level <= 5),
    CHECK(pattern_type IN ('letter_combo', 'prefix', 'suffix', 'theme', 'vowel_pattern', 'similar_spelling'))
);

-- 2. word_pattern_relations表 - 单词与pattern的关联关系
CREATE TABLE IF NOT EXISTS word_pattern_relations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word_id INTEGER NOT NULL,             -- 关联word表的id
    pattern_id INTEGER NOT NULL,          -- 关联word_patterns表的id
    match_strength REAL DEFAULT 1.0,     -- 匹配强度(0.0-1.0): 用于推荐排序
    match_position VARCHAR(20),           -- 匹配位置: 'start', 'middle', 'end', 'whole'
    match_reason TEXT,                    -- 匹配原因说明: "包含'ir'字母组合"
    is_primary BOOLEAN DEFAULT 0,        -- 是否为主要pattern: 每个单词可标记1-2个主要pattern
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (word_id) REFERENCES word(id) ON DELETE CASCADE,
    FOREIGN KEY (pattern_id) REFERENCES word_patterns(id) ON DELETE CASCADE,
    
    -- 复合唯一约束
    UNIQUE(word_id, pattern_id),
    
    -- 数值约束
    CHECK(match_strength >= 0.0 AND match_strength <= 1.0),
    CHECK(match_position IN ('start', 'middle', 'end', 'whole', 'multiple'))
);

-- 3. user_pattern_interactions表 - 用户与pattern的交互记录（可选）
CREATE TABLE IF NOT EXISTS user_pattern_interactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,             -- 关联user表的id
    pattern_id INTEGER NOT NULL,          -- 关联word_patterns表的id
    word_id INTEGER NOT NULL,             -- 触发的单词id
    interaction_type VARCHAR(20) NOT NULL, -- 交互类型: 'view', 'click', 'helpful', 'not_helpful'
    session_id VARCHAR(50),               -- 学习会话id
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (pattern_id) REFERENCES word_patterns(id) ON DELETE CASCADE,
    FOREIGN KEY (word_id) REFERENCES word(id) ON DELETE CASCADE,
    
    -- 约束
    CHECK(interaction_type IN ('view', 'click', 'helpful', 'not_helpful', 'ignore'))
);

-- 4. 创建索引优化查询性能
-- word_patterns表索引
CREATE INDEX IF NOT EXISTS idx_word_patterns_type ON word_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_word_patterns_priority ON word_patterns(priority_level DESC, word_count DESC);
CREATE INDEX IF NOT EXISTS idx_word_patterns_active ON word_patterns(is_active, priority_level DESC);

-- word_pattern_relations表索引
CREATE INDEX IF NOT EXISTS idx_word_pattern_relations_word ON word_pattern_relations(word_id);
CREATE INDEX IF NOT EXISTS idx_word_pattern_relations_pattern ON word_pattern_relations(pattern_id);
CREATE INDEX IF NOT EXISTS idx_word_pattern_relations_strength ON word_pattern_relations(match_strength DESC);
CREATE INDEX IF NOT EXISTS idx_word_pattern_relations_primary ON word_pattern_relations(is_primary, match_strength DESC);

-- 组合索引：支持"给定单词找相关pattern"查询
CREATE INDEX IF NOT EXISTS idx_word_pattern_lookup ON word_pattern_relations(word_id, match_strength DESC, is_primary DESC);

-- 组合索引：支持"给定pattern找相关单词"查询  
CREATE INDEX IF NOT EXISTS idx_pattern_word_lookup ON word_pattern_relations(pattern_id, match_strength DESC);

-- user_pattern_interactions表索引
CREATE INDEX IF NOT EXISTS idx_user_pattern_interactions_user ON user_pattern_interactions(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_pattern_interactions_pattern ON user_pattern_interactions(pattern_id, interaction_type);

-- 5. 插入初始pattern数据（基于分析报告的top patterns）

-- 5.1 高价值字母组合patterns
INSERT OR IGNORE INTO word_patterns (pattern_type, pattern_value, pattern_name, description, priority_level, word_count) VALUES
('letter_combo', 'er', '含er字母组合', '包含er字母组合的单词，如teacher, player, sister等，多为职业或比较级', 5, 110),
('letter_combo', 'ar', '含ar字母组合', '包含ar字母组合的单词，如march, area, art等，常见于月份和名词', 5, 76),
('letter_combo', 'th', '含th字母组合', '包含th字母组合的单词，如thursday, bath, birthday等', 5, 63),
('letter_combo', 'ea', '含ea字母组合', '包含ea字母组合的单词，如beach, bread, breakfast等', 5, 55),
('letter_combo', 'or', '含or字母组合', '包含or字母组合的单词，如history, actor, doctor等', 5, 54),
('letter_combo', 'ou', '含ou字母组合', '包含ou字母组合的单词，如cloud, country, cousin等', 4, 44),
('letter_combo', 'ch', '含ch字母组合', '包含ch字母组合的单词，如chinese, lunch, much等', 4, 41),
('letter_combo', 'ur', '含ur字母组合', '包含ur字母组合的单词，如saturday, adventure, burger等', 4, 39),
('letter_combo', 'ai', '含ai字母组合', '包含ai字母组合的单词，如air, train, captain等', 4, 37),
('letter_combo', 'oo', '含oo字母组合', '包含oo字母组合的单词，如book, afternoon, bedroom等', 4, 37),
('letter_combo', 'ir', '含ir字母组合', '包含ir字母组合的单词，如girl, hair, shirt, chair等', 4, 36),
('letter_combo', 'ly', '含ly字母组合', '包含ly字母组合的单词，多为副词如usually, really等', 4, 31);

-- 5.2 主题分组patterns  
INSERT OR IGNORE INTO word_patterns (pattern_type, pattern_value, pattern_name, description, priority_level, word_count) VALUES
('theme', 'time', '时间相关', '时间相关词汇：星期、月份、时间概念等', 5, 26),
('theme', 'clothes', '衣物类', '衣物相关词汇：shirt, dress, shoes等', 4, 12),
('theme', 'colors', '颜色类', '颜色相关词汇：red, blue, green等', 4, 11),
('theme', 'food', '食物类', '食物相关词汇：apple, bread, coffee等', 4, 11),
('theme', 'jobs', '职业类', '职业相关词汇：teacher, doctor, driver等', 4, 10),
('theme', 'body_parts', '身体部位', '身体部位词汇：head, hand, hair等', 3, 9),
('theme', 'family', '家庭关系', '家庭关系词汇：family, brother, sister等', 3, 7),
('theme', 'animals', '动物类', '动物相关词汇：fish, lion, pig等', 3, 3);

-- 5.3 语法patterns
INSERT OR IGNORE INTO word_patterns (pattern_type, pattern_value, pattern_name, description, priority_level, word_count) VALUES
('suffix', 'ing', 'ing后缀', '以ing结尾的单词，如something, interesting, swimming等', 3, 13),
('suffix', 'ed', 'ed后缀', '以ed结尾的单词，如surprised, tired, excited等', 3, 10),
('prefix', 'un', 'un前缀', '以un开头的单词，表示否定，如unhappy', 2, 2),
('prefix', 're', 're前缀', '以re开头的单词，表示重复，如repair', 2, 2);

-- 6. 创建视图简化常用查询

-- 6.1 单词pattern汇总视图
CREATE VIEW IF NOT EXISTS v_word_patterns_summary AS
SELECT 
    w.id as word_id,
    w.english_word,
    w.chinese_meaning,
    COUNT(wpr.pattern_id) as pattern_count,
    GROUP_CONCAT(
        wp.pattern_name || ' (' || wp.pattern_value || ')', 
        '; '
    ) as patterns,
    MAX(wpr.match_strength) as max_match_strength
FROM word w
LEFT JOIN word_pattern_relations wpr ON w.id = wpr.word_id
LEFT JOIN word_patterns wp ON wpr.pattern_id = wp.id AND wp.is_active = 1
GROUP BY w.id, w.english_word, w.chinese_meaning;

-- 6.2 pattern统计视图
CREATE VIEW IF NOT EXISTS v_pattern_statistics AS
SELECT 
    wp.id as pattern_id,
    wp.pattern_type,
    wp.pattern_value,
    wp.pattern_name,
    wp.priority_level,
    COUNT(wpr.word_id) as actual_word_count,
    wp.word_count as expected_word_count,
    AVG(wpr.match_strength) as avg_match_strength,
    COUNT(CASE WHEN wpr.is_primary = 1 THEN 1 END) as primary_matches
FROM word_patterns wp
LEFT JOIN word_pattern_relations wpr ON wp.id = wpr.pattern_id
WHERE wp.is_active = 1
GROUP BY wp.id, wp.pattern_type, wp.pattern_value, wp.pattern_name, wp.priority_level, wp.word_count;

-- 7. 创建触发器维护数据一致性

-- 更新word_count字段的触发器
CREATE TRIGGER IF NOT EXISTS update_pattern_word_count_insert
AFTER INSERT ON word_pattern_relations
BEGIN
    UPDATE word_patterns 
    SET word_count = (
        SELECT COUNT(*) 
        FROM word_pattern_relations 
        WHERE pattern_id = NEW.pattern_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.pattern_id;
END;

CREATE TRIGGER IF NOT EXISTS update_pattern_word_count_delete
AFTER DELETE ON word_pattern_relations
BEGIN
    UPDATE word_patterns 
    SET word_count = (
        SELECT COUNT(*) 
        FROM word_pattern_relations 
        WHERE pattern_id = OLD.pattern_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.pattern_id;
END;

-- 更新updated_at字段的触发器
CREATE TRIGGER IF NOT EXISTS update_pattern_timestamp
AFTER UPDATE ON word_patterns
BEGIN
    UPDATE word_patterns 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;