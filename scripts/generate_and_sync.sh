#!/bin/bash

# Word Learning App v5 批量生成图片并自动同步脚本
# 完整的工作流：生成 -> 审计 -> 同步

set -e

echo "🎨 Word Learning App v5 图片生成与同步工具"
echo "============================================="

# 参数解析
BATCH=${1:-batch2}
WORKERS=${2:-3}
AUTO_SYNC=${3:-yes}

echo "📋 配置信息:"
echo "   批次: $BATCH"
echo "   并发数: $WORKERS"
echo "   自动同步: $AUTO_SYNC"

# 1. 执行批量图片生成
echo ""
echo "🎨 步骤1: 批量生成图片..."
python scripts/batch_generate_images.py --batch $BATCH --workers $WORKERS

# 检查生成结果
if [ $? -eq 0 ]; then
    echo "✅ 图片生成完成"
else
    echo "❌ 图片生成失败"
    exit 1
fi

# 2. 重新运行静态资源审计
echo ""
echo "📊 步骤2: 更新资源审计..."
python scripts/audit_static_resources.py

# 3. 自动同步到QNAP（可选）
if [ "$AUTO_SYNC" = "yes" ]; then
    echo ""
    echo "🔄 步骤3: 同步到QNAP服务器..."
    ./scripts/sync_static_resources.sh
else
    echo ""
    echo "💡 跳过自动同步。稍后可运行："
    echo "   ./scripts/sync_static_resources.sh"
fi

echo ""
echo "🎉 批量生成和同步完成!"
echo "📊 建议检查最新的审计报告: static_resources_audit.json"