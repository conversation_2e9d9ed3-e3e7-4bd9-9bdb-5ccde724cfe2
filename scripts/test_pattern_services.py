#!/usr/bin/env python3
"""
Pattern服务测试脚本
测试PatternDetectorService和PatternRecommenderService
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.pattern.pattern_detector import PatternDetectorService
from src.services.pattern.pattern_recommender import PatternRecommenderService
import sqlite3


def test_pattern_detection():
    """测试pattern检测功能"""
    print("=== 测试Pattern检测功能 ===")
    
    db_path = "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/instance/words.db"
    detector = PatternDetectorService(db_path)
    
    # 测试几个单词
    test_words = [
        (1, "girl"),
        (2, "teacher"), 
        (3, "monday"),
        (4, "unhappy")
    ]
    
    for word_id, word in test_words:
        print(f"\n单词: {word}")
        patterns = detector.get_word_patterns(word_id)
        
        if patterns:
            for pattern in patterns:
                primary_mark = "⭐" if pattern['is_primary'] else ""
                print(f"  {primary_mark}{pattern['pattern_name']}: {pattern['match_reason']} (强度:{pattern['match_strength']:.2f})")
        else:
            print("  未找到pattern")


def test_pattern_recommendation():
    """测试pattern推荐功能"""
    print("\n=== 测试Pattern推荐功能 ===")
    
    db_path = "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/instance/words.db"
    recommender = PatternRecommenderService(db_path)
    
    # 首先创建一些测试用户数据
    setup_test_user_data(db_path)
    
    # 测试推荐功能
    user_id = 999  # 测试用户
    test_word_id = get_word_id_by_name(db_path, "girl")
    
    if test_word_id:
        print(f"\n为单词'girl'(id:{test_word_id})推荐相似单词:")
        recommendations = recommender.get_similar_words(user_id, test_word_id)
        
        for rec in recommendations:
            print(f"\nPattern: {rec.pattern_info['pattern_name']}")
            print(f"推荐理由: {rec.recommendation_reason}")
            print("相似单词:")
            for word in rec.similar_words:
                print(f"  - {word.english_word} ({word.chinese_meaning}) - 相似度:{word.similarity_score:.2f}")
    
    # 测试学习建议
    print(f"\n用户{user_id}的学习建议:")
    suggestions = recommender.get_pattern_learning_suggestions(user_id)
    
    for suggestion in suggestions[:3]:  # 只显示前3个
        print(f"\nPattern: {suggestion['pattern_name']}")
        print(f"完成率: {suggestion['completion_rate']*100:.1f}% ({suggestion['learned_words']}/{suggestion['total_words_in_pattern']})")
        print(f"平均熟练度: {suggestion['avg_proficiency']}")
        print(f"建议类型: {suggestion['suggestion_type']}")


def setup_test_user_data(db_path: str):
    """设置测试用户数据"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建测试用户
        cursor.execute("INSERT OR IGNORE INTO user (id, username, password) VALUES (999, 'test_user', 'test')")
        
        # 为测试用户添加一些学习过的单词（ir pattern相关）
        test_words = ['hair', 'shirt', 'chair', 'fire', 'air']
        
        for word in test_words:
            cursor.execute("SELECT id FROM word WHERE english_word = ?", (word,))
            result = cursor.fetchone()
            if result:
                word_id = result[0]
                # 添加到user_word表
                cursor.execute("""
                    INSERT OR REPLACE INTO user_word 
                    (user_id, word_id, learning_count, correct_count, status, proficiency, last_learning_date)
                    VALUES (999, ?, 5, 4, 'review', 75.0, datetime('now'))
                """, (word_id,))
        
        conn.commit()
        conn.close()
        print("测试用户数据设置完成")
        
    except Exception as e:
        print(f"设置测试数据失败: {e}")


def get_word_id_by_name(db_path: str, word: str) -> int:
    """根据单词名获取ID"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM word WHERE english_word = ?", (word,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    except:
        return None


def test_pattern_context():
    """测试pattern上下文功能"""
    print("\n=== 测试Pattern上下文功能 ===")
    
    db_path = "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/instance/words.db"
    recommender = PatternRecommenderService(db_path)
    
    word_id = get_word_id_by_name(db_path, "teacher")
    if word_id:
        context = recommender.get_word_pattern_context(word_id)
        
        print(f"单词'teacher'的pattern上下文:")
        print(f"总共{context['total_patterns']}个patterns:")
        
        for pattern in context['patterns']:
            primary_mark = "⭐" if pattern['is_primary'] else ""
            print(f"\n{primary_mark}{pattern['pattern_name']}: {pattern['match_reason']}")
            if pattern['example_words']:
                print(f"  同类单词: {', '.join(pattern['example_words'][:5])}")


def main():
    """主函数"""
    try:
        test_pattern_detection()
        test_pattern_recommendation()
        test_pattern_context()
        print("\n所有测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()