#!/usr/bin/env python3
"""
智能资源查找集成脚本
将智能资源查找逻辑集成到现有应用代码中
"""

import os
import sys
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class ResourceFinderIntegrator:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.integration_log = []
    
    def update_learning_routes(self):
        """更新学习路由中的资源查找逻辑"""
        routes_file = self.project_root / 'src' / 'routes' / 'learning_routes.py'
        
        if not routes_file.exists():
            print(f"❌ 路由文件不存在: {routes_file}")
            return False
        
        print(f"🔧 更新学习路由文件...")
        
        # 读取原文件
        with open(routes_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加导入语句
        import_pattern = r'(from src\.core import get_logger)'
        replacement = r'\1\nfrom src.utils.resource_finder import ResourceFinder'
        if 'from src.utils.resource_finder import ResourceFinder' not in content:
            content = re.sub(import_pattern, replacement, content)
        
        # 替换音频文件查找逻辑
        audio_pattern = r'audio_filename = f"{word}\.mp3".*?audio_url = f"/static/audio/words/{audio_filename}"'
        audio_replacement = '''audio_url = ResourceFinder.find_audio_file(word)
        if not audio_url:
            audio_url = f"/static/audio/words/{word.lower().replace(' ', '_')}.mp3"  # 兜底逻辑'''
        
        content = re.sub(audio_pattern, audio_replacement, content, flags=re.DOTALL)
        
        # 替换图片文件查找逻辑
        image_pattern = r'image_filename = f"{word}\.jpg".*?image_url = f"/static/images/words/{image_filename}"'
        image_replacement = '''image_url = ResourceFinder.find_image_file(word)
        if not image_url:
            image_url = f"/static/images/words/{word.lower().replace(' ', '_')}.jpg"  # 兜底逻辑'''
        
        content = re.sub(image_pattern, image_replacement, content, flags=re.DOTALL)
        
        # 写回文件
        with open(routes_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.integration_log.append(f"✅ 已更新: {routes_file}")
        return True
    
    def update_session_service(self):
        """更新会话服务中的资源查找逻辑"""
        service_file = self.project_root / 'src' / 'services' / 'learning' / 'session_service.py'
        
        if not service_file.exists():
            print(f"❌ 服务文件不存在: {service_file}")
            return False
        
        print(f"🔧 更新会话服务文件...")
        
        # 读取原文件
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加导入语句
        if 'from src.utils.resource_finder import ResourceFinder' not in content:
            import_pattern = r'(from src\.core import get_logger)'
            replacement = r'\1\nfrom src.utils.resource_finder import ResourceFinder'
            content = re.sub(import_pattern, replacement, content)
        
        # 替换音频路径构建逻辑
        audio_path_pattern = r"'audio_path': f'/static/audio/{.*?}\.mp3'"
        audio_path_replacement = "'audio_path': ResourceFinder.find_audio_file(current_word.get('english_word', '')) or f'/static/audio/{current_word.get(\"english_word\", \"\")}.mp3'"
        
        content = re.sub(audio_path_pattern, audio_path_replacement, content)
        
        # 替换图片路径查找逻辑
        image_candidates_pattern = r'image_candidates = \[.*?\]'
        image_candidates_replacement = '''# 使用智能资源查找器
        smart_image_path = ResourceFinder.find_image_file(word)
        if smart_image_path:
            image_candidates = [smart_image_path]
        else:
            image_candidates = [
                f"/static/images/words/{word.lower()}.jpg",
                f"/static/images/words/{word.lower()}.png",
                f"/static/images/words/{word.lower()}.webp"
            ]'''
        
        content = re.sub(image_candidates_pattern, image_candidates_replacement, content, flags=re.DOTALL)
        
        # 写回文件
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.integration_log.append(f"✅ 已更新: {service_file}")
        return True
    
    def update_learning_card_template(self):
        """更新学习卡片模板中的资源加载逻辑"""
        template_file = self.project_root / 'templates' / 'learning_card.html'
        
        if not template_file.exists():
            print(f"❌ 模板文件不存在: {template_file}")
            return False
        
        print(f"🔧 更新学习卡片模板...")
        
        # 读取原文件
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新音频路径构建逻辑，添加容错处理
        audio_js_pattern = r'const audioPath = `/static/audio/words/\${audioFileName}\.mp3`;'
        audio_js_replacement = r'''// 智能音频文件查找
        const audioPath = `/static/audio/words/${audioFileName}.mp3`;
        
        // 音频加载失败时的容错处理
        function tryLoadAudio(word) {
            const candidates = [
                word.toLowerCase().replace(/[\s\'".\/-]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, ''),
                word.toLowerCase().replace(/\s/g, '_'),
                word.toLowerCase(),
                word
            ];
            
            for (const candidate of candidates) {
                const testPath = `/static/audio/words/${candidate}.mp3`;
                // 这里可以添加实际的文件存在性检查
                return testPath;
            }
            return `/static/audio/words/${word}.mp3`;
        }'''
        
        content = re.sub(audio_js_pattern, audio_js_replacement, content)
        
        # 更新图片路径构建逻辑，添加容错处理
        image_js_pattern = r'const imagePath = `/static/images/words/\${imageName}\.jpg`;'
        image_js_replacement = r'''// 智能图片文件查找
        const imagePath = `/static/images/words/${imageName}.jpg`;
        
        // 图片加载失败时的容错处理
        function tryLoadImage(word) {
            const candidates = [
                word.toLowerCase().replace(/[\s\'".\/-]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, ''),
                word.toLowerCase().replace(/\s/g, '_'),
                word.toLowerCase(),
                word
            ];
            
            for (const candidate of candidates) {
                const testPath = `/static/images/words/${candidate}.jpg`;
                // 这里可以添加实际的文件存在性检查
                return testPath;
            }
            return `/static/images/words/${word}.jpg`;
        }'''
        
        content = re.sub(image_js_pattern, image_js_replacement, content)
        
        # 写回文件
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.integration_log.append(f"✅ 已更新: {template_file}")
        return True
    
    def create_migration_script(self):
        """创建迁移脚本用于持续清理"""
        migration_script = self.project_root / 'scripts' / 'maintain_clean_resources.py'
        
        migration_content = '''#!/usr/bin/env python3
"""
资源清理维护脚本
定期运行以保持资源文件的规范化
"""

import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.auto_resolve_duplicates import AutoDuplicateResolver
from scripts.audit_static_resources import StaticResourceAuditor

def main():
    """定期资源维护主函数"""
    print("🧹 开始定期资源维护...")
    
    # 1. 检查是否有新的重复文件
    resolver = AutoDuplicateResolver(project_root)
    
    # 2. 运行重复文件检测
    audio_duplicates = resolver.find_duplicate_groups(resolver.audio_dir, '.mp3')
    image_duplicates = resolver.find_duplicate_groups(resolver.image_dir, '.jpg')
    
    total_duplicates = len(audio_duplicates) + len(image_duplicates)
    
    if total_duplicates > 0:
        print(f"⚠️  发现 {total_duplicates} 组重复文件，开始自动清理...")
        resolver.auto_resolve_all_duplicates()
    else:
        print("✅ 未发现重复文件，资源状态良好")
    
    # 3. 生成资源审计报告
    auditor = StaticResourceAuditor(project_root)
    report = auditor.perform_mece_analysis()
    auditor.print_summary(report)
    
    print("✅ 定期资源维护完成")

if __name__ == '__main__':
    main()
'''
        
        with open(migration_script, 'w', encoding='utf-8') as f:
            f.write(migration_content)
        
        # 设置执行权限
        migration_script.chmod(0o755)
        
        self.integration_log.append(f"✅ 已创建维护脚本: {migration_script}")
        return True
    
    def integrate_all(self):
        """执行完整的集成过程"""
        print("🚀 开始智能资源查找集成...")
        
        success_count = 0
        total_tasks = 4
        
        # 更新各个组件
        if self.update_learning_routes():
            success_count += 1
        
        if self.update_session_service():
            success_count += 1
        
        if self.update_learning_card_template():
            success_count += 1
        
        if self.create_migration_script():
            success_count += 1
        
        # 生成集成报告
        print(f"\n{'='*80}")
        print("📋 智能资源查找集成完成!")
        print(f"{'='*80}")
        print(f"✅ 成功完成: {success_count}/{total_tasks} 项任务")
        
        if self.integration_log:
            print("\n📝 集成详情:")
            for log_entry in self.integration_log:
                print(f"   {log_entry}")
        
        print(f"\n🎯 接下来的步骤:")
        print("1. 重启应用服务器以加载新的资源查找逻辑")
        print("2. 测试各种单词的音频和图片加载功能")  
        print("3. 运行 scripts/maintain_clean_resources.py 进行定期维护")
        print("4. 监控应用日志确保资源查找正常工作")
        
        return success_count == total_tasks

def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    integrator = ResourceFinderIntegrator(project_root)
    return integrator.integrate_all()

if __name__ == '__main__':
    main()