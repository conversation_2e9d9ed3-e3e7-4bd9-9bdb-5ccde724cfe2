#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源命名规范修复脚本
用于建立单词与静态资源的标准化一一对应关系，解决MECE违规问题

功能：
1. 标准化文件命名规范
2. 建立单词ID与资源文件的一一对应关系
3. 重命名现有资源文件
4. 生成资源映射表
"""

import os
import sys
import sqlite3
import shutil
import json
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

class ResourceNamingFixer:
    """资源命名规范修复器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / "instance" / "words.db"
        self.audio_dir = self.project_root / "static" / "audio" / "words"
        self.text_dir = self.project_root / "static" / "cache" / "memory_help"
        self.backup_dir = self.project_root / "backups" / f"resource_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        self.mapping_log = []
        
    def standardize_filename(self, word: str) -> str:
        """标准化文件名规则
        
        规则：
        1. 转换为小写
        2. 空格替换为下划线
        3. 特殊字符标准化处理
        4. 连续下划线合并为单个
        """
        if not word:
            return ""
            
        # 基础标准化
        standardized = word.lower()
        
        # 特殊字符替换规则
        replacements = {
            ' ': '_',           # 空格 -> 下划线
            '/': '_',           # 斜杠 -> 下划线
            '.': '_',           # 句点 -> 下划线
            '-': '_',           # 连字符 -> 下划线
            "'": '',            # 单引号 -> 删除
            '"': '',            # 双引号 -> 删除
            '(': '',            # 左括号 -> 删除
            ')': '',            # 右括号 -> 删除
            '[': '',            # 左方括号 -> 删除
            ']': '',            # 右方括号 -> 删除
            ',': '',            # 逗号 -> 删除
            '!': '',            # 感叹号 -> 删除
            '?': '',            # 问号 -> 删除
            ':': '_',           # 冒号 -> 下划线
            ';': '_',           # 分号 -> 下划线
        }
        
        for old, new in replacements.items():
            standardized = standardized.replace(old, new)
        
        # 合并连续的下划线
        while '__' in standardized:
            standardized = standardized.replace('__', '_')
        
        # 删除首尾下划线
        standardized = standardized.strip('_')
        
        return standardized
        
    def get_all_words_from_db(self) -> List[Tuple[int, str, str]]:
        """从数据库获取所有单词"""
        words = []
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, english_word, chinese_meaning 
                FROM word 
                ORDER BY id
            """)
            
            words = cursor.fetchall()
            conn.close()
            
            print(f"✅ 从数据库获取到 {len(words)} 个单词")
            
        except Exception as e:
            print(f"❌ 数据库查询失败: {e}")
            
        return words
        
    def find_existing_resource_files(self) -> Tuple[Dict[str, str], Dict[str, str]]:
        """查找现有的资源文件"""
        audio_files = {}
        text_files = {}
        
        # 查找音频文件
        if self.audio_dir.exists():
            for file_path in self.audio_dir.glob("*.mp3"):
                filename_without_ext = file_path.stem
                audio_files[filename_without_ext] = str(file_path)
        
        # 查找文本文件
        if self.text_dir.exists():
            for file_path in self.text_dir.glob("*.txt"):
                filename_without_ext = file_path.stem
                text_files[filename_without_ext] = str(file_path)
        
        print(f"✅ 找到现有资源文件: 音频 {len(audio_files)} 个，文本 {len(text_files)} 个")
        return audio_files, text_files
        
    def find_best_match(self, word: str, available_files: Dict[str, str]) -> Optional[str]:
        """为单词查找最佳匹配的现有文件
        
        匹配策略：
        1. 精确匹配标准化后的文件名
        2. 忽略大小写的匹配
        3. 部分匹配（去除特殊字符后）
        """
        if not word or not available_files:
            return None
            
        standardized_word = self.standardize_filename(word)
        
        # 策略1: 精确匹配
        if standardized_word in available_files:
            return available_files[standardized_word]
            
        # 策略2: 忽略大小写匹配
        for filename in available_files:
            if filename.lower() == standardized_word:
                return available_files[filename]
        
        # 策略3: 部分匹配 - 单词是文件名的子串或相反
        for filename in available_files:
            # 单词包含在文件名中
            if standardized_word in filename or filename in standardized_word:
                return available_files[filename]
                
        # 策略4: 去除所有下划线后比较
        word_no_underscore = standardized_word.replace('_', '')
        for filename in available_files:
            filename_no_underscore = filename.replace('_', '')
            if word_no_underscore == filename_no_underscore:
                return available_files[filename]
        
        return None
        
    def create_new_filename_by_id(self, word_id: int, file_type: str) -> str:
        """基于单词ID创建新的标准化文件名"""
        extension = '.mp3' if file_type == 'audio' else '.txt'
        return f"word_{word_id:04d}{extension}"
        
    def backup_file(self, source_path: str, relative_path: str) -> bool:
        """备份文件到备份目录"""
        try:
            source = Path(source_path)
            backup_target = self.backup_dir / relative_path
            backup_target.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(source, backup_target)
            return True
            
        except Exception as e:
            print(f"❌ 备份文件失败 {source_path}: {e}")
            return False
            
    def rename_resource_file(self, old_path: str, new_path: str, word_id: int, word: str, file_type: str) -> bool:
        """重命名资源文件"""
        try:
            old_file = Path(old_path)
            new_file = Path(new_path)
            
            # 如果新文件已存在，先备份
            if new_file.exists():
                backup_name = f"duplicate_{new_file.name}"
                self.backup_file(str(new_file), f"{file_type}_duplicates/{backup_name}")
                print(f"⚠️ 新文件名已存在，已备份: {backup_name}")
            
            # 备份原文件
            relative_backup = f"{file_type}_originals/{old_file.name}"
            self.backup_file(old_path, relative_backup)
            
            # 重命名文件
            shutil.move(old_path, new_path)
            
            # 记录映射关系
            self.mapping_log.append({
                'word_id': word_id,
                'word': word,
                'file_type': file_type,
                'old_filename': old_file.name,
                'new_filename': new_file.name,
                'old_path': old_path,
                'new_path': new_path,
                'timestamp': datetime.now().isoformat()
            })
            
            return True
            
        except Exception as e:
            print(f"❌ 重命名文件失败 {old_path} -> {new_path}: {e}")
            return False
            
    def process_all_words(self) -> Dict[str, any]:
        """处理所有单词的资源重命名"""
        words = self.get_all_words_from_db()
        audio_files, text_files = self.find_existing_resource_files()
        
        results = {
            'total_words': len(words),
            'audio_matched': 0,
            'audio_renamed': 0,
            'text_matched': 0,
            'text_renamed': 0,
            'audio_missing': [],
            'text_missing': [],
            'errors': []
        }
        
        print(f"\n🚀 开始处理 {len(words)} 个单词的资源重命名...")
        
        for word_id, english_word, chinese_meaning in words:
            print(f"\n📝 处理单词 {word_id}: {english_word} ({chinese_meaning})")
            
            # 处理音频文件
            audio_match = self.find_best_match(english_word, audio_files)
            if audio_match:
                new_audio_path = self.audio_dir / self.create_new_filename_by_id(word_id, 'audio')
                if self.rename_resource_file(audio_match, str(new_audio_path), word_id, english_word, 'audio'):
                    results['audio_renamed'] += 1
                    results['audio_matched'] += 1
                    # 从可用文件中移除已使用的文件
                    for k, v in list(audio_files.items()):
                        if v == audio_match:
                            del audio_files[k]
                            break
                    print(f"  ✅ 音频重命名: {Path(audio_match).name} -> {new_audio_path.name}")
                else:
                    results['errors'].append(f"音频重命名失败: {english_word}")
            else:
                results['audio_missing'].append(english_word)
                print(f"  ❌ 未找到音频文件: {english_word}")
            
            # 处理文本文件
            text_match = self.find_best_match(english_word, text_files)
            if text_match:
                new_text_path = self.text_dir / self.create_new_filename_by_id(word_id, 'text')
                if self.rename_resource_file(text_match, str(new_text_path), word_id, english_word, 'text'):
                    results['text_renamed'] += 1
                    results['text_matched'] += 1
                    # 从可用文件中移除已使用的文件
                    for k, v in list(text_files.items()):
                        if v == text_match:
                            del text_files[k]
                            break
                    print(f"  ✅ 文本重命名: {Path(text_match).name} -> {new_text_path.name}")
                else:
                    results['errors'].append(f"文本重命名失败: {english_word}")
            else:
                results['text_missing'].append(english_word)
                print(f"  ❌ 未找到文本文件: {english_word}")
        
        return results
        
    def save_mapping_log(self):
        """保存映射日志"""
        log_file = self.backup_dir / "resource_mapping_log.json"
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.mapping_log, f, ensure_ascii=False, indent=2)
            print(f"✅ 映射日志已保存: {log_file}")
        except Exception as e:
            print(f"❌ 保存映射日志失败: {e}")
            
    def generate_resource_map(self) -> Dict[str, str]:
        """生成资源映射表文件"""
        resource_map = {}
        words = self.get_all_words_from_db()
        
        for word_id, english_word, chinese_meaning in words:
            audio_file = self.create_new_filename_by_id(word_id, 'audio')
            text_file = self.create_new_filename_by_id(word_id, 'text')
            
            resource_map[english_word] = {
                'word_id': word_id,
                'chinese_meaning': chinese_meaning,
                'audio_file': audio_file,
                'text_file': text_file,
                'audio_path': f"/static/audio/words/{audio_file}",
                'text_path': f"/static/cache/memory_help/{text_file}"
            }
        
        # 保存映射表
        map_file = self.project_root / "static" / "cache" / "resource_mapping.json"
        try:
            with open(map_file, 'w', encoding='utf-8') as f:
                json.dump(resource_map, f, ensure_ascii=False, indent=2)
            print(f"✅ 资源映射表已生成: {map_file}")
        except Exception as e:
            print(f"❌ 生成资源映射表失败: {e}")
            
        return resource_map
        
    def print_summary(self, results: Dict[str, any]):
        """打印处理结果摘要"""
        print(f"\n" + "="*60)
        print(f"📊 资源重命名处理结果摘要")
        print(f"="*60)
        print(f"总单词数: {results['total_words']}")
        print(f"")
        print(f"🎵 音频资源:")
        print(f"  匹配成功: {results['audio_matched']}")
        print(f"  重命名成功: {results['audio_renamed']}")
        print(f"  缺失数量: {len(results['audio_missing'])}")
        print(f"")
        print(f"📝 文本资源:")
        print(f"  匹配成功: {results['text_matched']}")
        print(f"  重命名成功: {results['text_renamed']}")
        print(f"  缺失数量: {len(results['text_missing'])}")
        print(f"")
        print(f"❌ 错误数量: {len(results['errors'])}")
        print(f"")
        print(f"💾 备份目录: {self.backup_dir}")
        print(f"📋 映射记录: {len(self.mapping_log)} 条")

def main():
    """主函数"""
    print("🔧 资源命名规范修复脚本启动")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 创建修复器实例
    fixer = ResourceNamingFixer(str(project_root))
    
    # 检查必要的目录
    if not fixer.db_path.exists():
        print(f"❌ 数据库文件不存在: {fixer.db_path}")
        return
        
    if not fixer.audio_dir.exists():
        print(f"❌ 音频目录不存在: {fixer.audio_dir}")
        return
        
    if not fixer.text_dir.exists():
        print(f"❌ 文本目录不存在: {fixer.text_dir}")
        return
    
    try:
        # 执行重命名处理
        results = fixer.process_all_words()
        
        # 保存映射日志
        fixer.save_mapping_log()
        
        # 生成资源映射表
        fixer.generate_resource_map()
        
        # 打印摘要
        fixer.print_summary(results)
        
        print(f"\n✅ 资源重命名修复完成!")
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())