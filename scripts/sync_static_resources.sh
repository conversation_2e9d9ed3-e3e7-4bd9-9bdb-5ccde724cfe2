#!/bin/bash

# Word Learning App v5 静态资源同步脚本
# 用于将本地新生成的静态资源同步到QNAP服务器

set -e

echo "🔄 Word Learning App v5 静态资源同步工具"
echo "=========================================="

# 获取QNAP密码
echo -n "请输入QNAP密码: "
read -s QNAP_PASSWORD
echo
export SSHPASS="$QNAP_PASSWORD"

# QNAP服务器配置
QNAP_HOST="lesong@*************"
QNAP_STATIC_PATH="/share/homes/lesong/word_learning_app_v5/static"
LOCAL_STATIC_PATH="./static"

# 检查本地static目录
if [ ! -d "$LOCAL_STATIC_PATH" ]; then
    echo "❌ 本地static目录不存在: $LOCAL_STATIC_PATH"
    exit 1
fi

# 获取本地静态资源统计
echo "📊 本地静态资源统计:"
echo "   音频文件: $(find $LOCAL_STATIC_PATH/audio/words -name "*.mp3" 2>/dev/null | wc -l) 个"
echo "   图片文件: $(find $LOCAL_STATIC_PATH/images/words -name "*.jpg" 2>/dev/null | wc -l) 个"

# 检查连接
echo "🔗 测试QNAP连接..."
if ! sshpass -e ssh -o ConnectTimeout=10 $QNAP_HOST "echo '连接成功'" >/dev/null 2>&1; then
    echo "❌ 无法连接到QNAP服务器"
    exit 1
fi
echo "✅ QNAP连接正常"

# 创建远程目录结构（如果不存在）
echo "📁 确保远程目录结构..."
sshpass -e ssh $QNAP_HOST "mkdir -p $QNAP_STATIC_PATH/{audio/words,images/words,css,js,cache,exports}"

# 获取远程静态资源统计
echo "📊 远程静态资源统计:"
REMOTE_AUDIO_COUNT=$(sshpass -e ssh $QNAP_HOST "find $QNAP_STATIC_PATH/audio/words -name '*.mp3' 2>/dev/null | wc -l" | tr -d ' ')
REMOTE_IMAGE_COUNT=$(sshpass -e ssh $QNAP_HOST "find $QNAP_STATIC_PATH/images/words -name '*.jpg' 2>/dev/null | wc -l" | tr -d ' ')
echo "   音频文件: $REMOTE_AUDIO_COUNT 个"
echo "   图片文件: $REMOTE_IMAGE_COUNT 个"

# 执行同步
echo ""
echo "🚀 开始同步静态资源..."

# 1. 同步音频文件
echo "🎵 同步音频文件..."
sshpass -e rsync -av --progress --update \
    $LOCAL_STATIC_PATH/audio/ \
    $QNAP_HOST:$QNAP_STATIC_PATH/audio/

# 2. 同步图片文件
echo "🖼️  同步图片文件..."
sshpass -e rsync -av --progress --update \
    $LOCAL_STATIC_PATH/images/ \
    $QNAP_HOST:$QNAP_STATIC_PATH/images/

# 3. 同步CSS和JS文件
echo "🎨 同步CSS和JS文件..."
sshpass -e rsync -av --progress --update \
    $LOCAL_STATIC_PATH/css/ \
    $QNAP_HOST:$QNAP_STATIC_PATH/css/

sshpass -e rsync -av --progress --update \
    $LOCAL_STATIC_PATH/js/ \
    $QNAP_HOST:$QNAP_STATIC_PATH/js/

# 4. 同步其他静态文件
echo "📦 同步其他静态文件..."
sshpass -e rsync -av --progress --update \
    --exclude='audio/' \
    --exclude='images/' \
    --exclude='css/' \
    --exclude='js/' \
    $LOCAL_STATIC_PATH/ \
    $QNAP_HOST:$QNAP_STATIC_PATH/

# 获取同步后的远程统计
echo ""
echo "📊 同步后远程静态资源统计:"
FINAL_AUDIO_COUNT=$(sshpass -e ssh $QNAP_HOST "find $QNAP_STATIC_PATH/audio/words -name '*.mp3' 2>/dev/null | wc -l" | tr -d ' ')
FINAL_IMAGE_COUNT=$(sshpass -e ssh $QNAP_HOST "find $QNAP_STATIC_PATH/images/words -name '*.jpg' 2>/dev/null | wc -l" | tr -d ' ')
echo "   音频文件: $FINAL_AUDIO_COUNT 个 (增加: $((FINAL_AUDIO_COUNT - REMOTE_AUDIO_COUNT)))"
echo "   图片文件: $FINAL_IMAGE_COUNT 个 (增加: $((FINAL_IMAGE_COUNT - REMOTE_IMAGE_COUNT)))"

# 验证容器能否访问新文件
echo ""
echo "🔍 验证容器访问..."
CONTAINER_STATUS=$(sshpass -e ssh $QNAP_HOST "docker ps -f name=word_learning_app_v5 --format '{{.Status}}'" || echo "容器未运行")
echo "   容器状态: $CONTAINER_STATUS"

if [[ $CONTAINER_STATUS == *"Up"* ]]; then
    # 测试随机文件访问
    TEST_AUDIO=$(sshpass -e ssh $QNAP_HOST "ls $QNAP_STATIC_PATH/audio/words/*.mp3 2>/dev/null | head -1" || echo "")
    if [ ! -z "$TEST_AUDIO" ]; then
        TEST_FILE=$(basename "$TEST_AUDIO")
        echo "   测试文件访问: http://*************:5003/static/audio/words/$TEST_FILE"
    fi
else
    echo "   ⚠️  容器未运行，请检查容器状态"
fi

echo ""
echo "✅ 静态资源同步完成!"
echo "🌐 应用访问地址: http://*************:5003"
echo ""
echo "💡 提示："
echo "   - 新的音频和图片文件已同步到服务器"
echo "   - 容器通过挂载卷可以立即访问新文件"
echo "   - 无需重启容器，新资源立即生效"