#!/usr/bin/env python3
"""
智能资源查找功能测试脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class StandaloneResourceFinder:
    """独立的资源查找器，不依赖Flask应用上下文"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.audio_dir = self.project_root / 'static' / 'audio' / 'words'
        self.image_dir = self.project_root / 'static' / 'images' / 'words'
    
    @staticmethod
    def normalize_word_for_filename(word: str) -> str:
        """标准化单词为文件名格式"""
        if not word:
            return ""
        
        import re
        normalized = word.lower()
        
        # 处理特殊的时间表达
        if 'a.m.' in normalized or 'p.m.' in normalized:
            normalized = normalized.replace('a.m./p.m.', 'a_m_p_m')
            normalized = normalized.replace('a.m.', 'a_m')
            normalized = normalized.replace('p.m.', 'p_m')
        
        # 标准替换
        normalized = normalized.replace(' ', '_')
        normalized = normalized.replace("'", "")
        normalized = normalized.replace('"', '')
        normalized = normalized.replace('.', '')
        normalized = normalized.replace('/', '_')
        normalized = normalized.replace('-', '_')
        normalized = normalized.replace('(', '')
        normalized = normalized.replace(')', '')
        
        # 清理多个连续下划线
        normalized = re.sub(r'_+', '_', normalized)
        normalized = normalized.strip('_')
        
        return normalized
    
    def generate_filename_candidates(self, word: str, extension: str):
        """生成可能的文件名候选列表"""
        if not word:
            return []
        
        candidates = []
        
        # 1. 标准化格式
        normalized = self.normalize_word_for_filename(word)
        if normalized:
            candidates.append(f"{normalized}{extension}")
        
        # 2. 简单下划线替换
        underscore_version = word.lower().replace(' ', '_')
        if underscore_version != normalized and underscore_version:
            candidates.append(f"{underscore_version}{extension}")
        
        # 3. 原始格式（保留空格）
        original_lower = word.lower()
        if original_lower not in [normalized, underscore_version]:
            candidates.append(f"{original_lower}{extension}")
        
        # 4. 原始大小写
        if word != original_lower:
            candidates.append(f"{word}{extension}")
        
        # 去重但保持顺序
        seen = set()
        unique_candidates = []
        for candidate in candidates:
            if candidate not in seen:
                seen.add(candidate)
                unique_candidates.append(candidate)
        
        return unique_candidates
    
    def find_audio_file(self, word: str):
        """查找音频文件"""
        if not word or not self.audio_dir.exists():
            return None
        
        candidates = self.generate_filename_candidates(word, '.mp3')
        
        for candidate in candidates:
            file_path = self.audio_dir / candidate
            if file_path.exists() and file_path.is_file():
                return f"/static/audio/words/{candidate}"
        
        return None
    
    def find_image_file(self, word: str):
        """查找图片文件"""
        if not word or not self.image_dir.exists():
            return None
        
        candidates = self.generate_filename_candidates(word, '.jpg')
        
        for candidate in candidates:
            file_path = self.image_dir / candidate
            if file_path.exists() and file_path.is_file():
                return f"/static/images/words/{candidate}"
        
        return None

def test_resource_finder():
    """测试资源查找功能"""
    finder = StandaloneResourceFinder(project_root)
    
    print("🧪 智能资源查找功能测试")
    print("="*60)
    
    # 测试用例
    test_cases = [
        # 已知存在标准化版本的词汇
        'a.m./p.m.',
        'visit my cousin',
        'bus station',
        'the most beautiful',
        'catch the bus',
        'more beautiful',
        'do homework',
        'go shopping',
        'table tennis',
        
        # 一些基础单词
        'apple',
        'book',
        'cat',
        'beautiful',
        'bus',
        
        # 不存在的词汇
        'nonexistent_word',
        'fake_test_word'
    ]
    
    print("\n🎵 音频文件查找测试:")
    print("-" * 40)
    found_audio = 0
    for word in test_cases:
        result = finder.find_audio_file(word)
        status = "✅ 找到" if result else "❌ 未找到"
        print(f"{word:20} | {status}")
        if result:
            found_audio += 1
            print(f"                     | -> {result}")
    
    print(f"\n音频文件查找成功率: {found_audio}/{len(test_cases)} ({found_audio/len(test_cases)*100:.1f}%)")
    
    print("\n🖼️  图片文件查找测试:")
    print("-" * 40)
    found_images = 0
    for word in test_cases:
        result = finder.find_image_file(word)
        status = "✅ 找到" if result else "❌ 未找到"
        print(f"{word:20} | {status}")
        if result:
            found_images += 1
            print(f"                     | -> {result}")
    
    print(f"\n图片文件查找成功率: {found_images}/{len(test_cases)} ({found_images/len(test_cases)*100:.1f}%)")
    
    # 测试标准化函数
    print("\n🔧 文件名标准化测试:")
    print("-" * 40)
    normalize_test_cases = [
        'a.m./p.m.',
        'visit my cousin',
        'bus station',
        'the most beautiful',
        "T-shirt",
        "pen-pal",
        "guest-house"
    ]
    
    for word in normalize_test_cases:
        normalized = finder.normalize_word_for_filename(word)
        print(f"{word:20} -> {normalized}")
    
    print("\n✅ 测试完成!")
    return found_audio, found_images

if __name__ == '__main__':
    test_resource_finder()