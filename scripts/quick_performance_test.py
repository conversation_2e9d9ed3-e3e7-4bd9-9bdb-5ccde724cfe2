#!/usr/bin/env python3
"""
Pattern推荐系统快速性能测试
"""

import os
import sys
import time
import sqlite3
import statistics
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.services.pattern.pattern_recommender import PatternRecommenderService
from src.services.pattern.concept_integrator import ConceptIntegrator
from src.config import Config

def test_basic_performance():
    """测试基本性能指标"""
    print("🚀 Pattern推荐系统快速性能测试")
    print("=" * 50)
    
    # 初始化服务
    recommender = PatternRecommenderService(Config.DATABASE_PATH)
    integrator = ConceptIntegrator()
    
    # 获取测试数据
    conn = sqlite3.connect(Config.DATABASE_PATH)
    cursor = conn.cursor()
    
    # 获取一个测试用户ID
    cursor.execute("SELECT id FROM user LIMIT 1")
    user_result = cursor.fetchone()
    if not user_result:
        print("❌ 没有找到测试用户")
        return
    user_id = user_result[0]
    
    # 获取一些测试单词ID
    cursor.execute("SELECT id FROM word ORDER BY RANDOM() LIMIT 10")
    word_ids = [row[0] for row in cursor.fetchall()]
    conn.close()
    
    print(f"📊 测试配置:")
    print(f"  用户ID: {user_id}")
    print(f"  测试单词数量: {len(word_ids)}")
    
    # 测试推荐性能
    print(f"\n🎯 测试推荐性能...")
    response_times = []
    success_count = 0
    total_recommendations = 0
    
    for i, word_id in enumerate(word_ids):
        start_time = time.time()
        
        try:
            recommendations = recommender.get_similar_words(user_id, word_id)
            end_time = time.time()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            success_count += 1
            total_recommendations += len(recommendations)
            
            print(f"  单词{word_id}: {response_time*1000:.1f}ms, {len(recommendations)}个推荐")
            
        except Exception as e:
            print(f"  单词{word_id}: 错误 - {e}")
    
    # 测试概念整合器性能
    print(f"\n🧠 测试概念整合器性能...")
    
    start_time = time.time()
    concept_stats = integrator.get_concept_statistics()
    concept_time = time.time() - start_time
    
    print(f"  概念统计耗时: {concept_time*1000:.1f}ms")
    print(f"  概念组数量: {concept_stats['total_concept_groups']}")
    
    # 性能统计
    print(f"\n📈 性能统计:")
    print(f"  成功率: {success_count}/{len(word_ids)} ({success_count/len(word_ids)*100:.1f}%)")
    print(f"  总推荐数: {total_recommendations}")
    
    if response_times:
        avg_time = statistics.mean(response_times) * 1000
        max_time = max(response_times) * 1000
        min_time = min(response_times) * 1000
        
        print(f"  平均响应时间: {avg_time:.1f}ms")
        print(f"  最快响应时间: {min_time:.1f}ms")
        print(f"  最慢响应时间: {max_time:.1f}ms")
        
        # 性能评级
        if avg_time < 100:
            grade = "优秀 🏆"
        elif avg_time < 200:
            grade = "良好 ✅"
        elif avg_time < 500:
            grade = "一般 ⚠️"
        else:
            grade = "需要优化 ❌"
        
        print(f"  性能评级: {grade}")
    
    print(f"\n🎉 性能测试完成！")

def test_end_to_end_scenario():
    """测试端到端用户场景"""
    print(f"\n🎭 端到端用户场景测试")
    print("=" * 50)
    
    # 模拟真实的学习场景
    recommender = PatternRecommenderService(Config.DATABASE_PATH)
    
    conn = sqlite3.connect(Config.DATABASE_PATH)
    cursor = conn.cursor()
    
    # 找一个有学习记录的用户
    cursor.execute("""
        SELECT DISTINCT user_id FROM user_word 
        WHERE status != 'new' 
        LIMIT 1
    """)
    user_result = cursor.fetchone()
    
    if not user_result:
        print("❌ 没有找到有学习记录的用户")
        return
    
    user_id = user_result[0]
    
    # 获取该用户当前正在学习的单词
    cursor.execute("""
        SELECT word_id FROM user_word 
        WHERE user_id = ? AND status IN ('review', 'attention')
        ORDER BY RANDOM() LIMIT 5
    """, (user_id,))
    
    learning_words = [row[0] for row in cursor.fetchall()]
    conn.close()
    
    if not learning_words:
        print("❌ 该用户没有正在学习的单词")
        return
    
    print(f"👤 用户ID: {user_id}")
    print(f"📚 正在学习的单词: {len(learning_words)}个")
    
    # 测试每个单词的推荐效果
    total_start = time.time()
    pattern_coverage = set()
    
    for word_id in learning_words:
        print(f"\n📖 为单词{word_id}获取推荐...")
        
        start_time = time.time()
        recommendations = recommender.get_similar_words(user_id, word_id)
        response_time = time.time() - start_time
        
        print(f"  响应时间: {response_time*1000:.1f}ms")
        print(f"  推荐数量: {len(recommendations)}")
        
        # 分析推荐的多样性
        for rec in recommendations:
            pattern_info = rec.pattern_info
            pattern_type = pattern_info.get('pattern_type', 'unknown')
            cognitive_level = pattern_info.get('cognitive_level', 'basic')
            dimension = pattern_info.get('dimension_category', 'semantic')
            
            pattern_coverage.add(f"{dimension}-{cognitive_level}")
            
            print(f"    📋 {pattern_type} ({cognitive_level}/{dimension}): {len(rec.similar_words)}个相似词")
    
    total_time = time.time() - total_start
    
    print(f"\n📊 场景测试总结:")
    print(f"  总耗时: {total_time*1000:.1f}ms")
    print(f"  覆盖的认知模式: {len(pattern_coverage)}种")
    print(f"  模式类型: {', '.join(sorted(pattern_coverage))}")
    
    print(f"\n✅ 端到端测试完成！")

if __name__ == "__main__":
    test_basic_performance()
    test_end_to_end_scenario()