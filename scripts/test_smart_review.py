#!/usr/bin/env python3
"""
智能复习单词选择算法测试脚本
对比新旧算法的选择结果，验证改进效果
"""

import sys
import os
from datetime import date, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from src.services.learning.smart_review_selector import SmartReviewSelector
from src.core.database import db


def test_old_vs_new_selection(username: str = 'naonao', count: int = 20):
    """对比新旧算法的选择结果"""
    
    print(f"=" * 80)
    print(f"智能复习单词选择算法测试")
    print(f"用户: {username}, 选择数量: {count}")
    print(f"=" * 80)
    
    # 获取用户ID
    query = "SELECT id FROM user WHERE username = ?"
    result = db.execute_query(query, (username,))
    if not result:
        print(f"❌ 用户 {username} 不存在")
        return
    
    user_id = result[0]['id']
    print(f"用户ID: {user_id}")
    
    # 1. 旧算法：简单的proficiency排序
    print(f"\n📊 旧算法结果（proficiency ASC）:")
    print("-" * 80)
    
    old_query = """
    SELECT w.english_word, uw.proficiency, uw.learning_count, uw.correct_count, 
           uw.last_learning_date, uw.status
    FROM word w
    JOIN user_word uw ON w.id = uw.word_id
    WHERE uw.user_id = ? AND uw.status IN ('review', 'attention')
    ORDER BY w.priority ASC, uw.proficiency ASC, w.id ASC
    LIMIT ?
    """
    
    old_results = db.execute_query(old_query, (user_id, count))
    
    for i, word in enumerate(old_results):
        accuracy = (word['correct_count'] / word['learning_count'] * 100) if word['learning_count'] > 0 else 0
        print(f"{i+1:2d}. {word['english_word']:15s} | "
              f"{word['proficiency']:5.1f}% | "
              f"学习{word['learning_count']:2d}次 | "
              f"正确{word['correct_count']:2d}次 | "
              f"{accuracy:5.1f}% | "
              f"{word['last_learning_date']} | "
              f"{word['status']}")
    
    # 2. 新算法：智能选择
    print(f"\n🧠 新算法结果（智能评分）:")
    print("-" * 80)
    
    # 获取候选单词（更详细的信息）
    candidate_query = """
    SELECT w.*, uw.proficiency, uw.learning_count, uw.correct_count, 
           uw.last_learning_date, uw.status
    FROM word w
    JOIN user_word uw ON w.id = uw.word_id
    WHERE uw.user_id = ? AND uw.status IN ('review', 'attention')
    ORDER BY w.priority ASC, w.id ASC
    """
    
    candidates = db.execute_query(candidate_query, (user_id,))
    candidate_dicts = [dict(row) for row in candidates]
    
    # 使用新算法选择
    selected_words = SmartReviewSelector.select_review_words(user_id, count, [])
    
    for i, word in enumerate(selected_words):
        # 计算智能评分
        smart_score = SmartReviewSelector._calculate_smart_score(word, user_id)
        accuracy = (word['correct_count'] / word['learning_count'] * 100) if word['learning_count'] > 0 else 0
        
        print(f"{i+1:2d}. {word['english_word']:15s} | "
              f"{word['proficiency']:5.1f}% | "
              f"评分:{smart_score:5.3f} | "
              f"学习{word['learning_count']:2d}次 | "
              f"正确{word['correct_count']:2d}次 | "
              f"{accuracy:5.1f}% | "
              f"{word['last_learning_date']} | "
              f"{word['status']}")
    
    # 3. 分析对比
    print(f"\n📈 算法对比分析:")
    print("-" * 80)
    
    old_words = set(w['english_word'] for w in old_results)
    new_words = set(w['english_word'] for w in selected_words)
    
    overlap = old_words.intersection(new_words)
    old_only = old_words - new_words
    new_only = new_words - old_words
    
    print(f"📋 重叠单词数: {len(overlap)} / {count} ({len(overlap)/count*100:.1f}%)")
    print(f"📉 旧算法独有: {len(old_only)} 个 - {', '.join(sorted(old_only)[:5])}{'...' if len(old_only) > 5 else ''}")
    print(f"📈 新算法独有: {len(new_only)} 个 - {', '.join(sorted(new_only)[:5])}{'...' if len(new_only) > 5 else ''}")
    
    # 4. 统计分析
    old_avg_prof = sum(w['proficiency'] for w in old_results) / len(old_results)
    new_avg_prof = sum(w['proficiency'] for w in selected_words) / len(selected_words)
    
    print(f"\n📊 统计对比:")
    print(f"平均熟练度 - 旧算法: {old_avg_prof:.1f}%, 新算法: {new_avg_prof:.1f}%")
    
    # 5. 时间分布分析
    print(f"\n⏰ 最后学习时间分布:")
    
    def analyze_time_distribution(words, algorithm_name):
        today = date.today()
        time_counts = {'今天': 0, '昨天': 0, '2-3天前': 0, '4-7天前': 0, '一周以上': 0, '从未': 0}
        
        for word in words:
            last_date = word['last_learning_date'] if hasattr(word, '__getitem__') else getattr(word, 'last_learning_date', None)
            if not last_date:
                time_counts['从未'] += 1
                continue
            
            try:
                if ' ' in last_date:
                    date_part = last_date.split(' ')[0]
                else:
                    date_part = last_date
                last_learning = date.fromisoformat(date_part)
                days_diff = (today - last_learning).days
                
                if days_diff == 0:
                    time_counts['今天'] += 1
                elif days_diff == 1:
                    time_counts['昨天'] += 1
                elif days_diff <= 3:
                    time_counts['2-3天前'] += 1
                elif days_diff <= 7:
                    time_counts['4-7天前'] += 1
                else:
                    time_counts['一周以上'] += 1
            except:
                time_counts['从未'] += 1
        
        print(f"{algorithm_name}: ", end="")
        for period, count in time_counts.items():
            if count > 0:
                print(f"{period}({count}) ", end="")
        print()
    
    analyze_time_distribution(old_results, "旧算法")
    analyze_time_distribution(selected_words, "新算法")
    
    return selected_words


def simulate_daily_usage(username: str = 'naonao', days: int = 5):
    """模拟连续几天的使用情况"""
    
    print(f"\n🔄 模拟连续{days}天的复习单词选择:")
    print("=" * 80)
    
    # 获取用户ID
    query = "SELECT id FROM user WHERE username = ?"
    result = db.execute_query(query, (username,))
    if not result:
        print(f"❌ 用户 {username} 不存在")
        return
    
    user_id = result[0]['id']
    
    daily_selections = []
    
    for day in range(days):
        print(f"\n📅 第{day+1}天选择:")
        selected = SmartReviewSelector.select_review_words(user_id, 10, [])
        words = [w['english_word'] for w in selected]
        print(f"   {', '.join(words[:5])}{'...' if len(words) > 5 else ''}")
        daily_selections.append(set(words))
    
    # 分析重复度
    print(f"\n📊 重复度分析:")
    for i in range(1, days):
        overlap = daily_selections[0].intersection(daily_selections[i])
        print(f"第1天与第{i+1}天重叠: {len(overlap)} / 10 ({len(overlap)/10*100:.1f}%)")


if __name__ == "__main__":
    print("🚀 开始测试智能复习单词选择算法")
    
    # 1. 对比测试
    selected = test_old_vs_new_selection('naonao', 20)
    
    # 2. 模拟连续使用
    simulate_daily_usage('naonao', 5)
    
    # 3. 获取详细分析
    if selected:
        print(f"\n🔍 选择分析详情:")
        analysis = SmartReviewSelector.get_selection_analysis(3, selected)  # naonao的user_id是3
        
        print(f"总数: {analysis['total']}")
        print(f"平均熟练度: {analysis['avg_proficiency']:.1f}%")
        print(f"评分范围: {analysis['score_range']['min']:.3f} - {analysis['score_range']['max']:.3f}")
        
        print(f"\n前10个单词的详细分析:")
        for item in analysis['analysis'][:10]:
            print(f"  {item['word']:15s} | 熟练度:{item['proficiency']:5.1f}% | "
                  f"评分:{item['smart_score']:5.3f} | 最后学习:{item['last_learning_date']}")
    
    print(f"\n✅ 测试完成！")