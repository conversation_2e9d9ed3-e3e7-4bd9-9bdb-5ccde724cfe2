#!/usr/bin/env python3
"""
资源清理维护脚本
定期运行以保持资源文件的规范化
"""

import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.auto_resolve_duplicates import AutoDuplicateResolver
from scripts.audit_static_resources import StaticResourceAuditor

def main():
    """定期资源维护主函数"""
    print("🧹 开始定期资源维护...")
    
    # 1. 检查是否有新的重复文件
    resolver = AutoDuplicateResolver(project_root)
    
    # 2. 运行重复文件检测
    audio_duplicates = resolver.find_duplicate_groups(resolver.audio_dir, '.mp3')
    image_duplicates = resolver.find_duplicate_groups(resolver.image_dir, '.jpg')
    
    total_duplicates = len(audio_duplicates) + len(image_duplicates)
    
    if total_duplicates > 0:
        print(f"⚠️  发现 {total_duplicates} 组重复文件，开始自动清理...")
        resolver.auto_resolve_all_duplicates()
    else:
        print("✅ 未发现重复文件，资源状态良好")
    
    # 3. 生成资源审计报告
    print("\n📊 生成资源审计报告...")
    auditor = StaticResourceAuditor(project_root)
    report = auditor.perform_mece_analysis()
    auditor.print_summary(report)
    
    print("\n✅ 定期资源维护完成")
    return total_duplicates == 0

if __name__ == '__main__':
    main()