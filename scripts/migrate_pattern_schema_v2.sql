-- Pattern推荐系统重构 - 数据库模式迁移脚本 v2.0
-- 添加认知层次和概念整合功能支持
-- 执行时间: 2025-01-26

-- ========================================
-- 第一步：添加新字段到word_patterns表
-- ========================================

-- 1.1 添加认知层次字段
ALTER TABLE word_patterns 
ADD COLUMN cognitive_level VARCHAR(20) DEFAULT 'basic' 
CHECK(cognitive_level IN ('basic', 'intermediate', 'advanced'));

-- 1.2 添加概念组字段（用于语义等价pattern整合）
ALTER TABLE word_patterns 
ADD COLUMN concept_group VARCHAR(50) DEFAULT NULL;

-- 1.3 添加四维度分类标识字段
ALTER TABLE word_patterns 
ADD COLUMN dimension_category VARCHAR(30) DEFAULT 'orthography' 
CHECK(dimension_category IN ('orthography', 'semantic', 'morphology', 'collocation'));

-- 1.4 添加教育价值评分字段
ALTER TABLE word_patterns 
ADD COLUMN educational_value REAL DEFAULT 0.5 
CHECK(educational_value >= 0.0 AND educational_value <= 1.0);

-- ========================================
-- 第二步：更新pattern_type约束，支持新四维度分类
-- ========================================

-- 由于SQLite不支持直接修改CHECK约束，需要创建新表并迁移数据
BEGIN TRANSACTION;

-- 2.1 创建新的临时表，包含扩展的pattern_type约束
CREATE TABLE word_patterns_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pattern_type VARCHAR(50) NOT NULL,
    pattern_value VARCHAR(100) NOT NULL,
    pattern_name VARCHAR(100),
    description TEXT,
    word_count INTEGER DEFAULT 0,
    priority_level INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    cognitive_level VARCHAR(20) DEFAULT 'basic',
    concept_group VARCHAR(50) DEFAULT NULL,
    dimension_category VARCHAR(30) DEFAULT 'orthography',
    educational_value REAL DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    UNIQUE(pattern_type, pattern_value),
    CHECK(priority_level >= 1 AND priority_level <= 5),
    CHECK(cognitive_level IN ('basic', 'intermediate', 'advanced')),
    CHECK(dimension_category IN ('orthography', 'semantic', 'morphology', 'collocation')),
    CHECK(educational_value >= 0.0 AND educational_value <= 1.0),
    -- 扩展的pattern_type支持四维度分类体系
    CHECK(pattern_type IN (
        -- 拼写与发音维度 (Orthography & Phonetics)
        'letter_combo', 'grapheme_phoneme', 'rhyme', 'phonetic', 'silent_letter', 'syllable',
        -- 词义关联维度 (Semantic Association)
        'theme', 'semantic', 'synonym', 'antonym',
        -- 构词与变形维度 (Morphology & Inflection)
        'prefix', 'suffix', 'root', 'morphology', 'adjective_forms', 'plural_forms', 'verb_forms',
        -- 搭配与用法维度 (Collocation & Usage)
        'collocation', 'contextual', 'usage_pattern',
        -- 兼容性支持（原有类型）
        'vowel_pattern', 'similar_spelling'
    ))
);

-- 2.2 迁移现有数据到新表
INSERT INTO word_patterns_new (
    id, pattern_type, pattern_value, pattern_name, description, word_count, 
    priority_level, is_active, created_at, updated_at,
    cognitive_level, concept_group, dimension_category, educational_value
)
SELECT 
    id, pattern_type, pattern_value, pattern_name, description, word_count,
    priority_level, is_active, created_at, updated_at,
    -- 基于现有pattern_type设置默认认知层次
    CASE 
        WHEN pattern_type IN ('letter_combo', 'phonetic', 'rhyme') THEN 'basic'
        WHEN pattern_type IN ('theme', 'semantic', 'collocation') THEN 'intermediate'
        WHEN pattern_type IN ('prefix', 'suffix', 'morphology') THEN 'advanced'
        ELSE 'basic'
    END as cognitive_level,
    -- 基于pattern_value设置概念组（解决语义重复问题）
    CASE
        WHEN pattern_value IN ('er') THEN 'er_concept'
        WHEN pattern_value IN ('ing') THEN 'ing_concept'
        WHEN pattern_value IN ('family') THEN 'family_concept'
        WHEN pattern_value IN ('time') THEN 'time_concept'
        WHEN pattern_value IN ('colors') THEN 'color_concept'
        WHEN pattern_value IN ('food') THEN 'food_concept'
        WHEN pattern_value IN ('clothes') THEN 'clothing_concept'
        WHEN pattern_value IN ('animals') THEN 'animal_concept'
        WHEN pattern_value IN ('jobs') THEN 'occupation_concept'
        WHEN pattern_value IN ('body_parts') THEN 'body_concept'
        ELSE NULL
    END as concept_group,
    -- 基于pattern_type设置维度分类
    CASE
        WHEN pattern_type IN ('letter_combo', 'phonetic', 'rhyme', 'vowel_pattern', 'similar_spelling') THEN 'orthography'
        WHEN pattern_type IN ('theme', 'semantic') THEN 'semantic'
        WHEN pattern_type IN ('prefix', 'suffix') THEN 'morphology'
        ELSE 'semantic'
    END as dimension_category,
    -- 基于priority_level和word_count计算教育价值
    ROUND(CAST(priority_level AS REAL) / 5.0 * 0.7 + CAST(word_count AS REAL) / 110.0 * 0.3, 2) as educational_value
FROM word_patterns;

-- 2.3 删除原表并重命名新表
DROP TABLE word_patterns;
ALTER TABLE word_patterns_new RENAME TO word_patterns;

COMMIT;

-- ========================================
-- 第三步：创建优化索引
-- ========================================

-- 3.1 重建基础索引
CREATE INDEX IF NOT EXISTS idx_word_patterns_type ON word_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_word_patterns_priority ON word_patterns(priority_level DESC, word_count DESC);
CREATE INDEX IF NOT EXISTS idx_word_patterns_active ON word_patterns(is_active, priority_level DESC);

-- 3.2 新增认知层次相关索引
CREATE INDEX IF NOT EXISTS idx_word_patterns_cognitive ON word_patterns(cognitive_level, educational_value DESC);
CREATE INDEX IF NOT EXISTS idx_word_patterns_dimension ON word_patterns(dimension_category, cognitive_level);
CREATE INDEX IF NOT EXISTS idx_word_patterns_concept_group ON word_patterns(concept_group, is_active);

-- 3.3 创建复合索引优化推荐查询
CREATE INDEX IF NOT EXISTS idx_pattern_recommendation ON word_patterns(
    is_active, dimension_category, cognitive_level, educational_value DESC, priority_level DESC
);

-- 3.4 创建概念组查询索引
CREATE INDEX IF NOT EXISTS idx_concept_integration ON word_patterns(
    concept_group, educational_value DESC, word_count DESC
) WHERE concept_group IS NOT NULL;

-- ========================================
-- 第四步：更新视图定义
-- ========================================

-- 4.1 删除原有视图
DROP VIEW IF EXISTS v_word_patterns_summary;
DROP VIEW IF EXISTS v_pattern_statistics;

-- 4.2 创建增强的单词pattern汇总视图
CREATE VIEW v_word_patterns_summary AS
SELECT 
    w.id as word_id,
    w.english_word,
    w.chinese_meaning,
    COUNT(wpr.pattern_id) as pattern_count,
    GROUP_CONCAT(
        wp.pattern_name || ' (' || wp.cognitive_level || ')', 
        '; '
    ) as patterns_with_levels,
    MAX(wpr.match_strength) as max_match_strength,
    MAX(wp.educational_value) as max_educational_value,
    -- 按认知层次分组统计
    COUNT(CASE WHEN wp.cognitive_level = 'basic' THEN 1 END) as basic_patterns,
    COUNT(CASE WHEN wp.cognitive_level = 'intermediate' THEN 1 END) as intermediate_patterns,
    COUNT(CASE WHEN wp.cognitive_level = 'advanced' THEN 1 END) as advanced_patterns
FROM word w
LEFT JOIN word_pattern_relations wpr ON w.id = wpr.word_id
LEFT JOIN word_patterns wp ON wpr.pattern_id = wp.id AND wp.is_active = 1
GROUP BY w.id, w.english_word, w.chinese_meaning;

-- 4.3 创建增强的pattern统计视图
CREATE VIEW v_pattern_statistics AS
SELECT 
    wp.id as pattern_id,
    wp.pattern_type,
    wp.pattern_value,
    wp.pattern_name,
    wp.cognitive_level,
    wp.concept_group,
    wp.dimension_category,
    wp.educational_value,
    wp.priority_level,
    COUNT(wpr.word_id) as actual_word_count,
    wp.word_count as expected_word_count,
    AVG(wpr.match_strength) as avg_match_strength,
    COUNT(CASE WHEN wpr.is_primary = 1 THEN 1 END) as primary_matches,
    -- 计算模式质量分数
    ROUND(
        wp.educational_value * 0.4 + 
        CAST(wp.priority_level AS REAL) / 5.0 * 0.3 + 
        COALESCE(AVG(wpr.match_strength), 0) * 0.3, 
        3
    ) as quality_score
FROM word_patterns wp
LEFT JOIN word_pattern_relations wpr ON wp.id = wpr.pattern_id
WHERE wp.is_active = 1
GROUP BY wp.id, wp.pattern_type, wp.pattern_value, wp.pattern_name, 
         wp.cognitive_level, wp.concept_group, wp.dimension_category, 
         wp.educational_value, wp.priority_level, wp.word_count;

-- 4.4 创建概念组整合视图
CREATE VIEW v_concept_groups AS
SELECT 
    concept_group,
    COUNT(*) as pattern_count,
    AVG(educational_value) as avg_educational_value,
    MAX(priority_level) as max_priority,
    SUM(word_count) as total_word_count,
    GROUP_CONCAT(pattern_name, ' | ') as integrated_patterns
FROM word_patterns 
WHERE concept_group IS NOT NULL AND is_active = 1
GROUP BY concept_group
ORDER BY avg_educational_value DESC, total_word_count DESC;

-- ========================================
-- 第五步：创建配置表支持动态调整
-- ========================================

-- 5.1 创建概念映射配置表
CREATE TABLE IF NOT EXISTS pattern_concept_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_pattern_type VARCHAR(50) NOT NULL,
    source_pattern_value VARCHAR(100) NOT NULL,
    target_concept_group VARCHAR(50) NOT NULL,
    integration_priority INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(source_pattern_type, source_pattern_value),
    CHECK(integration_priority >= 1 AND integration_priority <= 5)
);

-- 5.2 插入初始概念映射数据
INSERT OR IGNORE INTO pattern_concept_mappings (source_pattern_type, source_pattern_value, target_concept_group, integration_priority) VALUES
-- ER概念组整合
('letter_combo', 'er', 'er_concept', 5),
('suffix', 'er', 'er_concept', 5),
('phonetic', 'er', 'er_concept', 4),

-- ING概念组整合  
('suffix', 'ing', 'ing_concept', 5),
('letter_combo', 'ing', 'ing_concept', 4),
('rhyme', 'ng', 'ing_concept', 3),

-- 家庭概念组整合
('theme', 'family', 'family_concept', 5),
('semantic', '家庭关系', 'family_concept', 4),
('collocation', 'family_words', 'family_concept', 3),

-- 时间概念组整合
('theme', 'time', 'time_concept', 5),
('semantic', '时间', 'time_concept', 4),
('collocation', 'time_words', 'time_concept', 3);

-- 5.3 创建推荐配置表
CREATE TABLE IF NOT EXISTS pattern_recommendation_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(50) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5.4 插入默认推荐配置
INSERT OR IGNORE INTO pattern_recommendation_config (config_key, config_value, description) VALUES
('max_recommendations_per_word', '3', '每个单词最大推荐pattern数量'),
('min_similarity_threshold', '0.3', '最低相似度阈值'),
('concept_integration_enabled', 'true', '是否启用概念整合功能'),
('cognitive_adaptation_enabled', 'true', '是否启用认知层次适配'),
('basic_learner_threshold', '50', '基础学习者熟练度阈值'),
('advanced_learner_threshold', '80', '高级学习者熟练度阈值'),
('educational_value_weight', '0.4', '教育价值权重'),
('priority_weight', '0.3', '优先级权重'),
('match_strength_weight', '0.3', '匹配强度权重');

-- ========================================
-- 第六步：创建数据验证查询
-- ========================================

-- 检查迁移结果
SELECT 
    'Schema Migration Validation' as check_type,
    COUNT(*) as total_patterns,
    COUNT(CASE WHEN cognitive_level IS NOT NULL THEN 1 END) as cognitive_level_set,
    COUNT(CASE WHEN concept_group IS NOT NULL THEN 1 END) as concept_groups_assigned,
    COUNT(CASE WHEN dimension_category IS NOT NULL THEN 1 END) as dimensions_categorized
FROM word_patterns;

-- 显示概念组分布
SELECT 
    'Concept Groups Distribution' as info,
    concept_group,
    COUNT(*) as pattern_count,
    AVG(educational_value) as avg_value
FROM word_patterns 
WHERE concept_group IS NOT NULL 
GROUP BY concept_group 
ORDER BY pattern_count DESC;

-- 显示认知层次分布
SELECT 
    'Cognitive Levels Distribution' as info,
    cognitive_level,
    COUNT(*) as pattern_count,
    AVG(educational_value) as avg_value
FROM word_patterns 
GROUP BY cognitive_level 
ORDER BY 
    CASE cognitive_level 
        WHEN 'basic' THEN 1 
        WHEN 'intermediate' THEN 2 
        WHEN 'advanced' THEN 3 
    END;

-- 显示维度分类分布
SELECT 
    'Dimension Categories Distribution' as info,
    dimension_category,
    COUNT(*) as pattern_count,
    AVG(educational_value) as avg_value
FROM word_patterns 
GROUP BY dimension_category 
ORDER BY pattern_count DESC;

-- ========================================
-- 迁移完成提示
-- ========================================
SELECT 
    '✅ Pattern推荐系统数据库迁移完成' as status,
    'v2.0 - 认知层次和概念整合功能已就绪' as version,
    datetime('now') as completed_at;