#!/usr/bin/env python3
"""
Pattern API测试脚本
测试新增的pattern相关API路由
"""

import requests
import sys
import json


def test_pattern_api():
    """测试pattern API"""
    base_url = "http://localhost:5005"
    
    # 模拟登录 (这里需要真实的登录逻辑)
    session = requests.Session()
    
    print("=== Pattern API测试 ===\n")
    
    # 测试获取单词pattern建议
    print("1. 测试获取单词pattern建议...")
    word_id = 415  # girl的ID (从之前的测试中得知)
    
    try:
        response = session.get(f"{base_url}/api/word_pattern_suggestions/{word_id}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"数据结构: {list(data.keys())}")
            if data.get('success'):
                print(f"推荐数量: {len(data['data']['recommendations'])}")
                print(f"有推荐: {data['data']['has_recommendations']}")
            else:
                print(f"API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    print("\n" + "="*50)
    
    # 测试获取pattern学习建议
    print("2. 测试获取pattern学习建议...")
    
    try:
        response = session.get(f"{base_url}/api/pattern_learning_suggestions")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            if data.get('success'):
                print(f"建议数量: {len(data['data']['suggestions'])}")
            else:
                print(f"API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    print("\n" + "="*50)
    
    # 测试记录交互
    print("3. 测试记录pattern交互...")
    
    interaction_data = {
        "pattern_id": 1,  # 假设第一个pattern的ID是1
        "word_id": word_id,
        "interaction_type": "view",
        "session_id": "test_session"
    }
    
    try:
        response = session.post(
            f"{base_url}/api/pattern_interaction",
            json=interaction_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 交互记录成功")
            print(f"响应: {data}")
        else:
            print(f"❌ 交互记录失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def test_pattern_data_integrity():
    """测试pattern数据完整性"""
    import sqlite3
    
    print("\n=== Pattern数据完整性测试 ===\n")
    
    db_path = "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/instance/words.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查pattern表数据
        cursor.execute("SELECT COUNT(*) FROM word_patterns WHERE is_active = 1")
        active_patterns = cursor.fetchone()[0]
        print(f"✅ 活跃patterns: {active_patterns}")
        
        # 检查关联关系
        cursor.execute("SELECT COUNT(*) FROM word_pattern_relations")
        total_relations = cursor.fetchone()[0]
        print(f"✅ 总关联关系: {total_relations}")
        
        # 检查有pattern的单词数
        cursor.execute("SELECT COUNT(DISTINCT word_id) FROM word_pattern_relations")
        words_with_patterns = cursor.fetchone()[0]
        print(f"✅ 有pattern的单词: {words_with_patterns}")
        
        # 检查最受欢迎的patterns
        cursor.execute("""
            SELECT wp.pattern_name, COUNT(*) as word_count
            FROM word_pattern_relations wpr
            JOIN word_patterns wp ON wpr.pattern_id = wp.id
            WHERE wp.is_active = 1
            GROUP BY wp.id, wp.pattern_name
            ORDER BY word_count DESC
            LIMIT 5
        """)
        
        print("\n📊 Top 5 Patterns:")
        for pattern_name, word_count in cursor.fetchall():
            print(f"   - {pattern_name}: {word_count}个单词")
        
        conn.close()
        print("\n✅ 数据完整性检查完成")
        
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")


def main():
    """主函数"""
    print("开始Pattern功能测试...\n")
    
    # 先检查数据完整性
    test_pattern_data_integrity()
    
    # 然后测试API (需要应用运行)
    print("\n" + "="*60)
    print("📋 API测试说明:")
    print("以下API测试需要应用处于运行状态 (python app.py)")
    print("如果应用未运行，API测试将失败")
    print("="*60)
    
    test_pattern_api()
    
    print("\n🎉 Pattern功能测试完成！")
    print("\n📝 集成说明:")
    print("1. 确保在学习页面HTML中引入pattern_helper.js和pattern_helper.css")
    print("2. 在单词切换时触发'wordChanged'事件")
    print("3. PatternHelper会自动显示相似单词提示")


if __name__ == "__main__":
    main()