#!/usr/bin/env python3
"""
熟练度批量刷新脚本
根据新的权重计算公式更新数据库中所有user_word记录的proficiency值
"""

import sqlite3
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from src.services.proficiency.calculator import ProficiencyCalculator


def get_avg_duration(cursor, user_id, word_id):
    """获取用户对特定单词的平均用时"""
    cursor.execute('''
        SELECT AVG(duration_seconds) 
        FROM word_record 
        WHERE user_id = ? AND word_id = ?
    ''', (user_id, word_id))
    
    result = cursor.fetchone()
    return result[0] if result[0] else 0.0


def refresh_all_proficiency():
    """刷新所有用户单词的熟练度"""
    
    db_path = os.path.join(project_root, 'instance', 'words.db')
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件不存在 {db_path}")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 获取所有需要更新的user_word记录
        cursor.execute('''
            SELECT uw.id, uw.user_id, uw.word_id, uw.learning_count, 
                   uw.correct_count, uw.last_learning_date, uw.proficiency,
                   u.username, w.english_word
            FROM user_word uw
            JOIN user u ON uw.user_id = u.id
            JOIN word w ON uw.word_id = w.id
            WHERE uw.learning_count > 0
            ORDER BY u.username, w.english_word
        ''')
        
        user_words = cursor.fetchall()
        total_records = len(user_words)
        updated_count = 0
        
        print(f"开始刷新 {total_records} 条user_word记录的熟练度...")
        print("-" * 80)
        
        for record in user_words:
            (uw_id, user_id, word_id, learning_count, correct_count, 
             last_learning_date, old_proficiency, username, english_word) = record
            
            # 获取平均用时
            avg_duration = get_avg_duration(cursor, user_id, word_id)
            
            # 计算新的熟练度
            new_proficiency = ProficiencyCalculator.calculate_proficiency(
                learning_count=learning_count,
                correct_count=correct_count,
                avg_duration=avg_duration,
                last_learning_date=last_learning_date
            )
            
            # 更新数据库
            cursor.execute('''
                UPDATE user_word 
                SET proficiency = ? 
                WHERE id = ?
            ''', (new_proficiency, uw_id))
            
            # 显示更新信息
            change = new_proficiency - old_proficiency
            change_symbol = "+" if change > 0 else ""
            print(f"{updated_count+1:3d}. {username:8s} | {english_word:12s} | "
                  f"{old_proficiency:5.1f}% → {new_proficiency:5.1f}% | "
                  f"({change_symbol}{change:5.1f}%) | "
                  f"学习{learning_count:2d}次，正确{correct_count:2d}次，平均用时{avg_duration:5.1f}s")
            
            updated_count += 1
        
        # 提交事务
        conn.commit()
        
        print("-" * 80)
        print(f"✅ 成功更新 {updated_count} 条记录的熟练度")
        
        # 统计更新效果
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                AVG(proficiency) as avg_proficiency,
                MIN(proficiency) as min_proficiency,
                MAX(proficiency) as max_proficiency
            FROM user_word 
            WHERE learning_count > 0
        ''')
        
        stats = cursor.fetchone()
        print(f"📊 更新后统计:")
        print(f"   总记录数: {stats[0]}")
        print(f"   平均熟练度: {stats[1]:.1f}%")
        print(f"   最低熟练度: {stats[2]:.1f}%")
        print(f"   最高熟练度: {stats[3]:.1f}%")
        
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 更新失败: {e}")
        return False
        
    finally:
        conn.close()


if __name__ == "__main__":
    print("=" * 60)
    print("熟练度批量刷新工具")
    print("新权重: 正确率(60%) + 学习间隔(25%) + 回答时间(15%)")
    print("=" * 60)
    print()
    
    # 确认操作
    confirm = input("⚠️  此操作将更新所有用户的熟练度数据，是否继续？(y/N): ")
    if confirm.lower() not in ['y', 'yes']:
        print("操作已取消")
        sys.exit(0)
    
    # 执行刷新
    success = refresh_all_proficiency()
    
    if success:
        print(f"\n🎉 熟练度刷新完成! {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print(f"\n💥 熟练度刷新失败! {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(1)