#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完成图片文件名恢复脚本
处理恢复脚本遗漏的word_开头的图片文件，将其恢复为英文名
"""
import os
import sqlite3
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class CompleteImageRestoration:
    """完成图片文件名恢复服务"""

    def __init__(self, project_root: str, dry_run: bool = True):
        self.project_root = Path(project_root)
        self.dry_run = dry_run
        self.db_path = self.project_root / "instance" / "words.db"
        self.images_dir = self.project_root / "static" / "images" / "words"
        
        # 创建备份目录
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.backup_dir = self.project_root / "backups" / f"complete_image_restoration_{timestamp}"
        if not self.dry_run:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def get_word_from_database(self, word_id: int) -> Optional[str]:
        """从数据库获取单词的英文形式"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT english_word FROM word WHERE id = ?", (word_id,))
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            print(f"⚠️ 数据库查询失败 (word_id={word_id}): {e}")
            return None
    
    def find_word_files_to_restore(self) -> List[Tuple[str, str, int, str]]:
        """
        查找需要恢复的word_开头的图片文件
        
        Returns:
            List[Tuple[current_path, target_path, word_id, english_word]]
        """
        files_to_restore = []
        
        if not self.images_dir.exists():
            print(f"❌ 图片目录不存在: {self.images_dir}")
            return files_to_restore
        
        # 查找所有word_开头的jpg文件
        word_pattern_files = list(self.images_dir.glob("word_*.jpg"))
        print(f"🔍 找到 {len(word_pattern_files)} 个word_开头的图片文件")
        
        for word_file in word_pattern_files:
            try:
                # 从文件名提取word_id
                filename_parts = word_file.stem.split('_')
                if len(filename_parts) >= 2:
                    word_id = int(filename_parts[1])
                    
                    # 从数据库获取英文单词
                    english_word = self.get_word_from_database(word_id)
                    
                    if english_word:
                        # 生成目标文件名
                        # 处理特殊字符，确保文件名安全
                        safe_filename = self._make_filename_safe(english_word)
                        target_file = self.images_dir / f"{safe_filename}.jpg"
                        
                        # 检查目标文件是否已存在
                        if not target_file.exists():
                            files_to_restore.append((
                                str(word_file),
                                str(target_file),
                                word_id,
                                english_word
                            ))
                        else:
                            print(f"⚠️ 目标文件已存在，跳过: {target_file.name}")
                    else:
                        print(f"⚠️ 无法从数据库获取word_id={word_id}的英文单词")
                        
            except (ValueError, IndexError) as e:
                print(f"⚠️ 解析文件名失败: {word_file.name} - {e}")
                continue
        
        return files_to_restore
    
    def _make_filename_safe(self, filename: str) -> str:
        """
        将英文单词转换为安全的文件名
        处理特殊字符和空格
        """
        # 替换不安全的字符
        unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        safe_name = filename
        
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
        
        # 处理多个空格和特殊情况
        safe_name = safe_name.strip()
        
        # 如果包含空格或特殊字符，保持原样（很多文件名就是这样的）
        return safe_name
    
    def execute_restoration(self) -> Dict[str, any]:
        """执行图片文件名恢复"""
        print(f"{'🔍 DRY RUN: ' if self.dry_run else '🔄 '}开始完成图片文件名恢复...")
        
        # 查找需要恢复的文件
        files_to_restore = self.find_word_files_to_restore()
        
        results = {
            'total_found': len(files_to_restore),
            'restored': 0,
            'errors': [],
            'skipped': [],
            'restoration_log': []
        }
        
        if not files_to_restore:
            print("✅ 没有找到需要恢复的word_开头的图片文件")
            return results
        
        print(f"📋 找到 {len(files_to_restore)} 个需要恢复的文件")
        
        # 创建恢复日志
        restoration_log = []
        
        for current_path, target_path, word_id, english_word in files_to_restore:
            try:
                current_file = Path(current_path)
                target_file = Path(target_path)
                
                if self.dry_run:
                    print(f"🔍 DRY RUN: {current_file.name} -> {target_file.name} (word_id: {word_id}, word: {english_word})")
                else:
                    # 执行重命名
                    shutil.move(str(current_file), str(target_file))
                    print(f"✅ 已恢复: {current_file.name} -> {target_file.name} (word: {english_word})")
                
                # 记录恢复操作
                restoration_entry = {
                    'word_id': word_id,
                    'english_word': english_word,
                    'old_path': current_path,
                    'new_path': target_path,
                    'timestamp': datetime.now().isoformat()
                }
                restoration_log.append(restoration_entry)
                results['restoration_log'].append(restoration_entry)
                results['restored'] += 1
                
            except Exception as e:
                error_msg = f"恢复失败 {current_path} -> {target_path}: {str(e)}"
                results['errors'].append(error_msg)
                print(f"❌ {error_msg}")
        
        # 保存恢复日志
        if not self.dry_run and restoration_log:
            log_file = self.backup_dir / "restoration_log.json"
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'total_restored': len(restoration_log),
                    'restorations': restoration_log
                }, f, indent=2, ensure_ascii=False)
            print(f"📝 恢复日志已保存: {log_file}")
        
        # 打印结果摘要
        self._print_results_summary(results)
        
        return results
    
    def _print_results_summary(self, results: Dict):
        """打印结果摘要"""
        print("\n" + "="*60)
        print(f"{'DRY RUN ' if self.dry_run else ''}图片文件名恢复完成")
        print("="*60)
        print(f"📊 发现的word_文件: {results['total_found']}")
        print(f"✅ 成功恢复: {results['restored']}")
        print(f"⚠️ 跳过的文件: {len(results['skipped'])}")
        print(f"❌ 错误数量: {len(results['errors'])}")
        
        if results['errors']:
            print("\n❌ 错误详情:")
            for error in results['errors']:
                print(f"  - {error}")
        
        if results['skipped']:
            print(f"\n⚠️ 跳过的文件:")
            for skip in results['skipped']:
                print(f"  - {skip}")
    
    def verify_remaining_word_files(self) -> Dict[str, int]:
        """验证是否还有剩余的word_文件"""
        if not self.images_dir.exists():
            return {'word_files': 0, 'english_files': 0}
        
        word_files = list(self.images_dir.glob("word_*.jpg"))
        all_jpg_files = list(self.images_dir.glob("*.jpg"))
        english_files = [f for f in all_jpg_files if not f.name.startswith("word_")]
        
        print(f"\n📊 当前图片目录状态:")
        print(f"  - word_开头的文件: {len(word_files)}")
        print(f"  - 英文名文件: {len(english_files)}")
        print(f"  - 总jpg文件: {len(all_jpg_files)}")
        
        if word_files:
            print(f"\n⚠️ 仍有 {len(word_files)} 个word_开头的文件:")
            for i, wf in enumerate(word_files[:10]):  # 只显示前10个
                print(f"  - {wf.name}")
            if len(word_files) > 10:
                print(f"  - ... 还有 {len(word_files) - 10} 个文件")
        
        return {
            'word_files': len(word_files),
            'english_files': len(english_files),
            'total_files': len(all_jpg_files)
        }


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='完成图片文件名恢复')
    parser.add_argument('--force', action='store_true', 
                       help='执行实际恢复操作（默认为dry run）')
    parser.add_argument('--project-root', type=str, 
                       default='/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod',
                       help='项目根目录路径')
    parser.add_argument('--verify-only', action='store_true',
                       help='仅验证剩余的word_文件，不执行恢复')
    
    args = parser.parse_args()
    
    # 初始化恢复服务
    restoration_service = CompleteImageRestoration(
        project_root=args.project_root,
        dry_run=not args.force
    )
    
    try:
        if args.verify_only:
            # 仅验证模式
            restoration_service.verify_remaining_word_files()
        else:
            # 先验证当前状态
            restoration_service.verify_remaining_word_files()
            
            # 执行恢复操作
            results = restoration_service.execute_restoration()
            
            # 再次验证
            print(f"\n{'='*60}")
            print("恢复后验证:")
            restoration_service.verify_remaining_word_files()
            
            if restoration_service.dry_run:
                print(f"\n🔍 这是DRY RUN模式。如要执行实际恢复，请使用 --force 参数")
            else:
                print(f"\n✅ 图片文件名恢复完成！")
                
    except KeyboardInterrupt:
        print(f"\n⚠️ 操作被用户中断")
    except Exception as e:
        print(f"\n❌ 恢复操作失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()