#!/usr/bin/env python3
"""
Pattern推荐系统重构 - 数据分类更新脚本
将现有24个patterns按新四维度框架重新分类和优化
"""

import sqlite3
import sys
import os
from typing import Dict, List, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import get_logger

logger = get_logger(__name__)


class PatternClassificationUpdater:
    """Pattern分类更新器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.classification_mapping = self._build_classification_mapping()
    
    def _build_classification_mapping(self) -> Dict[Tuple[str, str], Dict]:
        """构建pattern分类映射规则"""
        return {
            # 字母组合类 - 拼写与发音维度，基础认知层次
            ('letter_combo', 'er'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': 'er_concept',
                'educational_value': 0.85,
                'priority_level': 5,
                'new_description': '含ER字母组合 - 最重要的英语拼写模式之一，涵盖职业词汇(teacher)、比较级(bigger)等核心概念'
            },
            ('letter_combo', 'ar'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.80,
                'priority_level': 5,
                'new_description': '含AR字母组合 - 英语基础拼写模式，常见于核心词汇如car、star、march等'
            },
            ('letter_combo', 'th'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.78,
                'priority_level': 5,
                'new_description': '含TH字母组合 - 英语特有发音[θ][ð]，掌握后显著提升发音准确性'
            },
            ('letter_combo', 'ea'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.75,
                'priority_level': 4,
                'new_description': '含EA字母组合 - 多音模式[iː][e]，是掌握英语发音复杂性的重要模式'
            },
            ('letter_combo', 'or'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.72,
                'priority_level': 4,
                'new_description': '含OR字母组合 - 稳定发音[ɔːr]，多见于职业和状态词汇'
            },
            ('letter_combo', 'ou'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.70,
                'priority_level': 4,
                'new_description': '含OU字母组合 - 发音丰富[aʊ][ʌ]，高频日常词汇的重要模式'
            },
            ('letter_combo', 'ch'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.68,
                'priority_level': 4,
                'new_description': '含CH字母组合 - 清晰发音[tʃ]，多为具体名词，适合联想记忆'
            },
            ('letter_combo', 'ur'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.65,
                'priority_level': 4,
                'new_description': '含UR字母组合 - 发音[ɜːr]，包含有趣概念，容易形成深刻印象'
            },
            ('letter_combo', 'ai'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.63,
                'priority_level': 4,
                'new_description': '含AI字母组合 - 发音[eɪ]，多为积极词汇，学习时容易产生愉快联想'
            },
            ('letter_combo', 'oo'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.60,
                'priority_level': 4,
                'new_description': '含OO字母组合 - 长短音[uː][ʊ]，掌握发音规律提升语感'
            },
            ('letter_combo', 'ir'): {
                'dimension_category': 'orthography',
                'cognitive_level': 'basic',
                'concept_group': None,
                'educational_value': 0.58,
                'priority_level': 4,
                'new_description': '含IR字母组合 - 发音[ɜːr]，包含动作和状态词，提升表达能力'
            },
            ('letter_combo', 'ly'): {
                'dimension_category': 'morphology',
                'cognitive_level': 'intermediate',
                'concept_group': None,
                'educational_value': 0.55,
                'priority_level': 3,
                'new_description': '含LY字母组合 - 多为副词后缀，掌握词性转换规律的关键'
            },
            
            # 主题分组类 - 语义关联维度，中级认知层次
            ('theme', 'time'): {
                'dimension_category': 'semantic',
                'cognitive_level': 'intermediate',
                'concept_group': 'time_concept',
                'educational_value': 0.85,
                'priority_level': 5,
                'new_description': '时间表达词汇 - 日常交流核心，建议按年-月-日-时顺序记忆'
            },
            ('theme', 'clothes'): {
                'dimension_category': 'semantic',
                'cognitive_level': 'intermediate',
                'concept_group': 'clothing_concept',
                'educational_value': 0.65,
                'priority_level': 4,
                'new_description': '服装类词汇 - 实用性强，可联想自己衣柜或穿着场景记忆'
            },
            ('theme', 'colors'): {
                'dimension_category': 'semantic',
                'cognitive_level': 'basic',
                'concept_group': 'color_concept',
                'educational_value': 0.70,
                'priority_level': 4,
                'new_description': '颜色词汇 - 强烈视觉联想，结合具体场景(red apple)加深记忆'
            },
            ('theme', 'food'): {
                'dimension_category': 'semantic',
                'cognitive_level': 'intermediate',
                'concept_group': 'food_concept',
                'educational_value': 0.68,
                'priority_level': 4,
                'new_description': '食物词汇 - 贴近生活，结合味觉记忆想象食物味道和外观'
            },
            ('theme', 'jobs'): {
                'dimension_category': 'semantic',
                'cognitive_level': 'intermediate',
                'concept_group': 'occupation_concept',
                'educational_value': 0.72,
                'priority_level': 4,
                'new_description': '职业词汇 - 职场必备，对职业发展很有帮助的高频词汇类别'
            },
            ('theme', 'body_parts'): {
                'dimension_category': 'semantic',
                'cognitive_level': 'basic',
                'concept_group': 'body_concept',
                'educational_value': 0.60,
                'priority_level': 3,
                'new_description': '身体部位词汇 - 通过动作记忆法学习，边说边指对应部位效果更佳'
            },
            ('theme', 'family'): {
                'dimension_category': 'semantic',
                'cognitive_level': 'intermediate',
                'concept_group': 'family_concept',
                'educational_value': 0.55,
                'priority_level': 3,
                'new_description': '家庭成员词汇 - 包含情感因素，容易形成长期记忆，可按关系远近分层记忆'
            },
            ('theme', 'animals'): {
                'dimension_category': 'semantic',
                'cognitive_level': 'basic',
                'concept_group': 'animal_concept',
                'educational_value': 0.50,
                'priority_level': 3,
                'new_description': '动物词汇 - 形象生动，结合动物特征和叫声进行联想记忆'
            },
            
            # 后缀类 - 构词与变形维度，中级认知层次
            ('suffix', 'ing'): {
                'dimension_category': 'morphology',
                'cognitive_level': 'intermediate',
                'concept_group': 'ing_concept',
                'educational_value': 0.75,
                'priority_level': 4,
                'new_description': 'ING后缀 - 表示进行时态，英语语法基础，掌握动词ing形式提升语法准确性'
            },
            ('suffix', 'ed'): {
                'dimension_category': 'morphology',
                'cognitive_level': 'intermediate',
                'concept_group': None,
                'educational_value': 0.70,
                'priority_level': 4,
                'new_description': 'ED后缀 - 表示过去时态，规律动词变化基础，是英语语法的重要基石'
            },
            
            # 前缀类 - 构词与变形维度，高级认知层次
            ('prefix', 'un'): {
                'dimension_category': 'morphology',
                'cognitive_level': 'advanced',
                'concept_group': None,
                'educational_value': 0.65,
                'priority_level': 3,
                'new_description': 'UN前缀 - 表示否定，构成反义词的重要方法，学会后能成倍扩展词汇量'
            },
            ('prefix', 're'): {
                'dimension_category': 'morphology',
                'cognitive_level': 'advanced',
                'concept_group': None,
                'educational_value': 0.60,
                'priority_level': 3,
                'new_description': 'RE前缀 - 表示重复再次，掌握规律后遇到新词能猜出大概意思'
            }
        }
    
    def update_all_patterns(self) -> bool:
        """更新所有patterns的分类信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            updated_count = 0
            
            for (pattern_type, pattern_value), classification in self.classification_mapping.items():
                # 查找对应的pattern
                cursor.execute("""
                    SELECT id FROM word_patterns 
                    WHERE pattern_type = ? AND pattern_value = ?
                """, (pattern_type, pattern_value))
                
                result = cursor.fetchone()
                if not result:
                    logger.warning(f"Pattern {pattern_type}:{pattern_value} 不存在，跳过")
                    continue
                
                pattern_id = result[0]
                
                # 更新分类信息
                update_query = """
                UPDATE word_patterns SET
                    dimension_category = ?,
                    cognitive_level = ?,
                    concept_group = ?,
                    educational_value = ?,
                    priority_level = ?,
                    description = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """
                
                cursor.execute(update_query, (
                    classification['dimension_category'],
                    classification['cognitive_level'],
                    classification['concept_group'],
                    classification['educational_value'],
                    classification['priority_level'],
                    classification['new_description'],
                    pattern_id
                ))
                
                updated_count += 1
                logger.info(f"更新Pattern: {pattern_type}:{pattern_value} -> {classification['cognitive_level']}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"Pattern分类更新完成，共更新 {updated_count} 个patterns")
            return True
            
        except Exception as e:
            logger.error(f"Pattern分类更新失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def validate_classifications(self) -> Dict:
        """验证分类更新结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计各维度分布
            cursor.execute("""
                SELECT dimension_category, COUNT(*) as count
                FROM word_patterns 
                WHERE is_active = 1
                GROUP BY dimension_category
                ORDER BY count DESC
            """)
            dimension_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 统计认知层次分布
            cursor.execute("""
                SELECT cognitive_level, COUNT(*) as count
                FROM word_patterns 
                WHERE is_active = 1
                GROUP BY cognitive_level
                ORDER BY 
                    CASE cognitive_level 
                        WHEN 'basic' THEN 1 
                        WHEN 'intermediate' THEN 2 
                        WHEN 'advanced' THEN 3 
                    END
            """)
            cognitive_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 统计概念组分布
            cursor.execute("""
                SELECT concept_group, COUNT(*) as count
                FROM word_patterns 
                WHERE concept_group IS NOT NULL AND is_active = 1
                GROUP BY concept_group
                ORDER BY count DESC
            """)
            concept_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 统计教育价值分布
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN educational_value >= 0.8 THEN 'high'
                        WHEN educational_value >= 0.6 THEN 'medium'
                        ELSE 'low'
                    END as value_level,
                    COUNT(*) as count
                FROM word_patterns 
                WHERE is_active = 1
                GROUP BY value_level
                ORDER BY 
                    CASE value_level 
                        WHEN 'high' THEN 1 
                        WHEN 'medium' THEN 2 
                        WHEN 'low' THEN 3 
                    END
            """)
            value_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            conn.close()
            
            validation_result = {
                'dimension_distribution': dimension_stats,
                'cognitive_distribution': cognitive_stats,
                'concept_groups': concept_stats,
                'educational_value_distribution': value_stats,
                'total_patterns': sum(dimension_stats.values()),
                'concept_integration_rate': len(concept_stats) / sum(dimension_stats.values()) if dimension_stats else 0
            }
            
            # 打印统计结果
            print("\n" + "="*50)
            print("Pattern分类更新验证结果")
            print("="*50)
            
            print(f"\n📊 总Pattern数量: {validation_result['total_patterns']}")
            
            print(f"\n🎯 维度分布:")
            for dim, count in dimension_stats.items():
                print(f"  {dim}: {count} patterns")
            
            print(f"\n🧠 认知层次分布:")
            for level, count in cognitive_stats.items():
                print(f"  {level}: {count} patterns")
            
            print(f"\n🔗 概念组整合:")
            print(f"  概念组数量: {len(concept_stats)}")
            print(f"  整合覆盖率: {validation_result['concept_integration_rate']:.1%}")
            for group, count in concept_stats.items():
                print(f"  {group}: {count} patterns")
            
            print(f"\n💎 教育价值分布:")
            for level, count in value_stats.items():
                print(f"  {level}: {count} patterns")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"分类验证失败: {e}")
            return {}
    
    def generate_migration_report(self) -> str:
        """生成迁移报告"""
        from datetime import datetime
        report_lines = [
            "# Pattern推荐系统重构 - 数据迁移报告",
            f"## 迁移概述",
            f"- 迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"- 迁移范围: 24个核心patterns重新分类",
            f"- 框架升级: 静态/动态 → 四维度认知框架",
            "",
            "## 新分类体系",
            "### 四个核心维度",
            "1. **拼写与发音** (Orthography & Phonetics) - 基础感知层",
            "2. **词义关联** (Semantic Association) - 语义关联层", 
            "3. **构词与变形** (Morphology & Inflection) - 高级应用层",
            "4. **搭配与用法** (Collocation & Usage) - 高级应用层",
            "",
            "### 三个认知层次",
            "1. **Basic** - 基础感知层：直接视听觉识别",
            "2. **Intermediate** - 语义关联层：意义理解和连接",
            "3. **Advanced** - 高级应用层：语法规律和应用",
            "",
            "## 重要改进",
            "### 概念整合机制",
            "- 解决语义冗余问题（如aunt案例中的多重匹配）",
            "- ER概念组整合：ER字母组合 + ER后缀 + ER发音",
            "- ING概念组整合：ING后缀 + ING字母组合",
            "- 家庭概念组整合：家庭主题 + 家庭语义 + 家庭搭配",
            "",
            "### 认知层次适配",
            "- 基础学习者：优先推荐拼写发音patterns",
            "- 中级学习者：增加词义关联patterns",
            "- 高级学习者：重点推荐构词变形patterns",
            "",
            "### 教育价值评估",
            "- 综合考虑：单词数量、匹配强度、认知价值",
            "- 动态权重：根据用户水平调整推荐优先级",
            "- 质量保证：去除低价值和重复推荐",
            "",
            "## 预期效果",
            "1. **信息简化**: 解决信息过载，提供精准推荐",
            "2. **学习路径**: 符合认知规律的渐进式学习",
            "3. **个性化**: 根据用户水平动态调整推荐策略",
            "4. **教育价值**: 提供更有价值的学习指导"
        ]
        
        return "\n".join(report_lines)


def main():
    """主函数"""
    from datetime import datetime
    
    # 数据库路径
    db_path = "instance/words.db"
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return 1
    
    print("开始Pattern分类更新...")
    
    # 创建更新器并执行更新
    updater = PatternClassificationUpdater(db_path)
    
    if updater.update_all_patterns():
        print("✅ Pattern分类更新成功")
        
        # 验证更新结果
        validation_result = updater.validate_classifications()
        
        if validation_result:
            print("✅ 分类验证完成")
            
            # 生成迁移报告
            report = updater.generate_migration_report()
            report_file = f"pattern_migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"📝 迁移报告已生成: {report_file}")
            
        return 0
    else:
        print("❌ Pattern分类更新失败")
        return 1


if __name__ == "__main__":
    exit(main())