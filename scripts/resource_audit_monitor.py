#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源审计和监控脚本
提供全面的MECE合规性监控、资源完整性检查和自动告警机制

功能：
1. 实时MECE合规性监控
2. 资源完整性审计
3. 异常检测和告警
4. 性能指标统计
5. 自动修复建议
6. 历史趋势分析
"""

import os
import sys
import sqlite3
import json
import asyncio
import aiofiles
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass, asdict
import hashlib
import time

# 添加项目根目录到路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

@dataclass
class ResourceAuditResult:
    """资源审计结果"""
    timestamp: str
    total_words: int
    perfect_matches: int
    missing_audio: int
    missing_text: int
    orphaned_audio: int
    orphaned_text: int
    corrupted_files: int
    mece_compliance_rate: float
    resource_integrity_rate: float
    total_file_size_mb: float
    avg_audio_size_kb: float
    avg_text_size_kb: float
    audit_duration_seconds: float

@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric: str
    threshold: float
    operator: str  # '<', '>', '<=', '>='
    severity: str  # 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    message_template: str

@dataclass
class Alert:
    """告警信息"""
    timestamp: str
    rule_name: str
    severity: str
    message: str
    current_value: float
    threshold: float
    suggestions: List[str]

class ResourceAuditMonitor:
    """资源审计监控器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / "instance" / "words.db"
        self.audio_dir = self.project_root / "static" / "audio" / "words"
        self.text_dir = self.project_root / "static" / "cache" / "memory_help"
        
        # 审计和监控文件
        self.audit_log_dir = self.project_root / "logs" / "audit"
        self.audit_log_dir.mkdir(parents=True, exist_ok=True)
        
        self.audit_history_file = self.audit_log_dir / "audit_history.jsonl"
        self.alerts_file = self.audit_log_dir / "alerts.json"
        self.metrics_file = self.audit_log_dir / "metrics_summary.json"
        self.dashboard_file = self.audit_log_dir / "dashboard_data.json"
        
        # 配置告警规则
        self.alert_rules = self._setup_alert_rules()
        
        # 缓存数据
        self._word_cache = None
        self._cache_timestamp = None
        self._cache_ttl = 300  # 5分钟缓存
    
    def _setup_alert_rules(self) -> List[AlertRule]:
        """设置告警规则"""
        return [
            AlertRule(
                name="MECE_COMPLIANCE_LOW",
                metric="mece_compliance_rate",
                threshold=95.0,
                operator="<",
                severity="HIGH",
                message_template="MECE合规率过低: {current_value:.1f}% < {threshold}%"
            ),
            AlertRule(
                name="RESOURCE_INTEGRITY_LOW",
                metric="resource_integrity_rate",
                threshold=98.0,
                operator="<",
                severity="MEDIUM",
                message_template="资源完整性过低: {current_value:.1f}% < {threshold}%"
            ),
            AlertRule(
                name="MISSING_AUDIO_HIGH",
                metric="missing_audio",
                threshold=50,
                operator=">",
                severity="HIGH",
                message_template="缺失音频文件过多: {current_value} > {threshold}"
            ),
            AlertRule(
                name="MISSING_TEXT_HIGH",
                metric="missing_text",
                threshold=100,
                operator=">",
                severity="HIGH",
                message_template="缺失文本文件过多: {current_value} > {threshold}"
            ),
            AlertRule(
                name="CORRUPTED_FILES_DETECTED",
                metric="corrupted_files",
                threshold=0,
                operator=">",
                severity="CRITICAL",
                message_template="检测到损坏文件: {current_value} 个文件无法读取"
            ),
            AlertRule(
                name="ORPHANED_FILES_HIGH",
                metric="orphaned_audio",
                threshold=200,
                operator=">",
                severity="MEDIUM",
                message_template="孤立音频文件过多: {current_value} > {threshold}"
            ),
            AlertRule(
                name="ORPHANED_TEXT_HIGH",
                metric="orphaned_text",
                threshold=200,
                operator=">",
                severity="MEDIUM",
                message_template="孤立文本文件过多: {current_value} > {threshold}"
            )
        ]
    
    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def get_standardized_filename(self, word_id: int, file_type: str) -> str:
        """获取标准化文件名"""
        extension = '.mp3' if file_type == 'audio' else '.txt'
        return f"word_{word_id:04d}{extension}"
    
    async def get_cached_words(self) -> List[Tuple[int, str, str]]:
        """获取缓存的单词数据"""
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._word_cache is not None and 
            self._cache_timestamp is not None and 
            current_time - self._cache_timestamp < self._cache_ttl):
            return self._word_cache
        
        # 重新加载数据
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, english_word, chinese_meaning 
                FROM word 
                ORDER BY id
            """)
            
            words = cursor.fetchall()
            conn.close()
            
            # 更新缓存
            self._word_cache = words
            self._cache_timestamp = current_time
            
            return words
            
        except Exception as e:
            self.log_message(f"数据库查询失败: {e}", "ERROR")
            return []
    
    async def check_file_integrity(self, file_path: Path) -> Tuple[bool, int, str]:
        """检查文件完整性"""
        try:
            if not file_path.exists():
                return False, 0, "文件不存在"
            
            file_size = file_path.stat().st_size
            
            if file_size == 0:
                return False, 0, "文件为空"
            
            # 尝试读取文件内容
            if file_path.suffix == '.mp3':
                # 对于音频文件，检查基础格式
                with open(file_path, 'rb') as f:
                    header = f.read(10)
                    if not header.startswith(b'ID3') and not header.startswith(b'\xff\xfb'):
                        return False, file_size, "音频格式无效"
            else:
                # 对于文本文件，检查编码
                async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                    content = await f.read(100)  # 只读前100个字符
                    if not content.strip():
                        return False, file_size, "文件内容为空"
            
            return True, file_size, "正常"
            
        except Exception as e:
            return False, 0, f"读取错误: {str(e)}"
    
    async def audit_single_word_resources(self, word_id: int, english_word: str) -> Dict[str, any]:
        """审计单个单词的资源"""
        result = {
            'word_id': word_id,
            'english_word': english_word,
            'audio_exists': False,
            'text_exists': False,
            'audio_valid': False,
            'text_valid': False,
            'audio_size': 0,
            'text_size': 0,
            'issues': []
        }
        
        # 检查音频文件
        audio_filename = self.get_standardized_filename(word_id, 'audio')
        audio_path = self.audio_dir / audio_filename
        
        audio_valid, audio_size, audio_status = await self.check_file_integrity(audio_path)
        result['audio_exists'] = audio_path.exists()
        result['audio_valid'] = audio_valid
        result['audio_size'] = audio_size
        
        if not result['audio_exists']:
            result['issues'].append(f"缺失音频文件: {audio_filename}")
        elif not result['audio_valid']:
            result['issues'].append(f"音频文件损坏: {audio_status}")
        
        # 检查文本文件
        text_filename = self.get_standardized_filename(word_id, 'text')
        text_path = self.text_dir / text_filename
        
        text_valid, text_size, text_status = await self.check_file_integrity(text_path)
        result['text_exists'] = text_path.exists()
        result['text_valid'] = text_valid
        result['text_size'] = text_size
        
        if not result['text_exists']:
            result['issues'].append(f"缺失文本文件: {text_filename}")
        elif not result['text_valid']:
            result['issues'].append(f"文本文件损坏: {text_status}")
        
        return result
    
    async def find_orphaned_files(self) -> Tuple[List[str], List[str]]:
        """查找孤立文件"""
        words = await self.get_cached_words()
        expected_audio = {self.get_standardized_filename(word_id, 'audio') for word_id, _, _ in words}
        expected_text = {self.get_standardized_filename(word_id, 'text') for word_id, _, _ in words}
        
        # 查找实际文件
        actual_audio = {f.name for f in self.audio_dir.glob("*.mp3")} if self.audio_dir.exists() else set()
        actual_text = {f.name for f in self.text_dir.glob("*.txt")} if self.text_dir.exists() else set()
        
        # 计算孤立文件
        orphaned_audio = list(actual_audio - expected_audio)
        orphaned_text = list(actual_text - expected_text)
        
        return orphaned_audio, orphaned_text
    
    async def perform_full_audit(self) -> ResourceAuditResult:
        """执行完整的资源审计"""
        start_time = time.time()
        self.log_message("🔍 开始执行完整资源审计...")
        
        words = await self.get_cached_words()
        
        # 初始化统计
        total_words = len(words)
        perfect_matches = 0
        missing_audio = 0
        missing_text = 0
        corrupted_files = 0
        total_audio_size = 0
        total_text_size = 0
        valid_audio_count = 0
        valid_text_count = 0
        
        # 逐个审计单词资源
        audit_tasks = []
        for word_id, english_word, _ in words:
            audit_tasks.append(self.audit_single_word_resources(word_id, english_word))
        
        # 并发执行审计
        batch_size = 50
        for i in range(0, len(audit_tasks), batch_size):
            batch = audit_tasks[i:i + batch_size]
            results = await asyncio.gather(*batch, return_exceptions=True)
            
            for result in results:
                if isinstance(result, dict):
                    # 统计完美匹配
                    if result['audio_valid'] and result['text_valid']:
                        perfect_matches += 1
                    
                    # 统计缺失资源
                    if not result['audio_exists']:
                        missing_audio += 1
                    if not result['text_exists']:
                        missing_text += 1
                    
                    # 统计损坏文件
                    if result['audio_exists'] and not result['audio_valid']:
                        corrupted_files += 1
                    if result['text_exists'] and not result['text_valid']:
                        corrupted_files += 1
                    
                    # 统计文件大小
                    if result['audio_valid']:
                        total_audio_size += result['audio_size']
                        valid_audio_count += 1
                    if result['text_valid']:
                        total_text_size += result['text_size']
                        valid_text_count += 1
        
        # 查找孤立文件
        orphaned_audio, orphaned_text = await self.find_orphaned_files()
        
        # 计算指标
        mece_compliance_rate = (perfect_matches / total_words * 100) if total_words > 0 else 0
        
        # 资源完整性 = (存在且有效的资源数量) / (应该存在的资源总数)
        expected_resources = total_words * 2  # 每个单词应有2个资源文件
        valid_resources = sum([
            1 for word_id, _, _ in words
            if (self.audio_dir / self.get_standardized_filename(word_id, 'audio')).exists() and
               (self.text_dir / self.get_standardized_filename(word_id, 'text')).exists()
        ]) * 2
        
        resource_integrity_rate = (valid_resources / expected_resources * 100) if expected_resources > 0 else 0
        
        # 计算平均文件大小
        avg_audio_size_kb = (total_audio_size / valid_audio_count / 1024) if valid_audio_count > 0 else 0
        avg_text_size_kb = (total_text_size / valid_text_count / 1024) if valid_text_count > 0 else 0
        total_file_size_mb = (total_audio_size + total_text_size) / (1024 * 1024)
        
        audit_duration = time.time() - start_time
        
        # 创建审计结果
        result = ResourceAuditResult(
            timestamp=datetime.now().isoformat(),
            total_words=total_words,
            perfect_matches=perfect_matches,
            missing_audio=missing_audio,
            missing_text=missing_text,
            orphaned_audio=len(orphaned_audio),
            orphaned_text=len(orphaned_text),
            corrupted_files=corrupted_files,
            mece_compliance_rate=mece_compliance_rate,
            resource_integrity_rate=resource_integrity_rate,
            total_file_size_mb=total_file_size_mb,
            avg_audio_size_kb=avg_audio_size_kb,
            avg_text_size_kb=avg_text_size_kb,
            audit_duration_seconds=audit_duration
        )
        
        self.log_message(f"✅ 审计完成: MECE合规率 {mece_compliance_rate:.1f}%, "
                        f"完整性 {resource_integrity_rate:.1f}%, "
                        f"耗时 {audit_duration:.1f}秒")
        
        return result
    
    async def evaluate_alerts(self, audit_result: ResourceAuditResult) -> List[Alert]:
        """评估告警条件"""
        alerts = []
        
        for rule in self.alert_rules:
            current_value = getattr(audit_result, rule.metric)
            
            # 评估告警条件
            triggered = False
            if rule.operator == '<':
                triggered = current_value < rule.threshold
            elif rule.operator == '>':
                triggered = current_value > rule.threshold
            elif rule.operator == '<=':
                triggered = current_value <= rule.threshold
            elif rule.operator == '>=':
                triggered = current_value >= rule.threshold
            
            if triggered:
                # 生成建议
                suggestions = self._generate_suggestions(rule.name, current_value, audit_result)
                
                alert = Alert(
                    timestamp=datetime.now().isoformat(),
                    rule_name=rule.name,
                    severity=rule.severity,
                    message=rule.message_template.format(
                        current_value=current_value,
                        threshold=rule.threshold
                    ),
                    current_value=current_value,
                    threshold=rule.threshold,
                    suggestions=suggestions
                )
                
                alerts.append(alert)
        
        return alerts
    
    def _generate_suggestions(self, rule_name: str, current_value: float, audit_result: ResourceAuditResult) -> List[str]:
        """生成修复建议"""
        suggestions = []
        
        if rule_name == "MECE_COMPLIANCE_LOW":
            suggestions.extend([
                f"运行 generate_missing_text_resources.py 修复 {audit_result.missing_text} 个缺失文本",
                f"运行 resource_sync_monitor.py 自动生成 {audit_result.missing_audio} 个缺失音频",
                "执行 fix_resource_naming.py 标准化现有资源命名"
            ])
        
        elif rule_name == "MISSING_AUDIO_HIGH":
            suggestions.extend([
                "运行资源同步脚本自动生成缺失音频",
                "检查TTS服务是否正常运行",
                "考虑批量音频生成以提高效率"
            ])
        
        elif rule_name == "MISSING_TEXT_HIGH":
            suggestions.extend([
                "运行 generate_missing_text_resources.py 批量生成文本",
                "检查AI文本生成服务状态",
                "启用基础模板生成作为备选方案"
            ])
        
        elif rule_name == "CORRUPTED_FILES_DETECTED":
            suggestions.extend([
                "备份损坏文件到 backups/ 目录",
                "重新生成损坏的资源文件",
                "检查存储设备是否存在硬件问题"
            ])
        
        elif rule_name == "ORPHANED_FILES_HIGH":
            suggestions.extend([
                "运行清理脚本移除孤立文件",
                "将有用的孤立文件重新映射到正确单词",
                f"备份后删除 {current_value} 个孤立文件以节省空间"
            ])
        
        return suggestions
    
    async def save_audit_history(self, audit_result: ResourceAuditResult):
        """保存审计历史"""
        try:
            audit_data = asdict(audit_result)
            audit_line = json.dumps(audit_data, ensure_ascii=False)
            
            async with aiofiles.open(self.audit_history_file, 'a', encoding='utf-8') as f:
                await f.write(audit_line + '\n')
                
        except Exception as e:
            self.log_message(f"保存审计历史失败: {e}", "ERROR")
    
    async def save_alerts(self, alerts: List[Alert]):
        """保存告警信息"""
        try:
            alerts_data = {
                'timestamp': datetime.now().isoformat(),
                'total_alerts': len(alerts),
                'alerts_by_severity': {
                    'CRITICAL': len([a for a in alerts if a.severity == 'CRITICAL']),
                    'HIGH': len([a for a in alerts if a.severity == 'HIGH']),
                    'MEDIUM': len([a for a in alerts if a.severity == 'MEDIUM']),
                    'LOW': len([a for a in alerts if a.severity == 'LOW'])
                },
                'alerts': [asdict(alert) for alert in alerts]
            }
            
            async with aiofiles.open(self.alerts_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(alerts_data, ensure_ascii=False, indent=2))
                
        except Exception as e:
            self.log_message(f"保存告警信息失败: {e}", "ERROR")
    
    async def generate_dashboard_data(self, audit_result: ResourceAuditResult, alerts: List[Alert]):
        """生成仪表板数据"""
        try:
            # 读取历史数据计算趋势
            historical_compliance = await self._get_historical_compliance()
            
            dashboard_data = {
                'last_update': datetime.now().isoformat(),
                'current_status': {
                    'mece_compliance_rate': audit_result.mece_compliance_rate,
                    'resource_integrity_rate': audit_result.resource_integrity_rate,
                    'total_words': audit_result.total_words,
                    'perfect_matches': audit_result.perfect_matches,
                    'issues_count': audit_result.missing_audio + audit_result.missing_text + audit_result.corrupted_files
                },
                'resource_breakdown': {
                    'missing_audio': audit_result.missing_audio,
                    'missing_text': audit_result.missing_text,
                    'orphaned_audio': audit_result.orphaned_audio,
                    'orphaned_text': audit_result.orphaned_text,
                    'corrupted_files': audit_result.corrupted_files
                },
                'performance_metrics': {
                    'total_size_mb': audit_result.total_file_size_mb,
                    'avg_audio_size_kb': audit_result.avg_audio_size_kb,
                    'avg_text_size_kb': audit_result.avg_text_size_kb,
                    'audit_duration_seconds': audit_result.audit_duration_seconds
                },
                'alerts_summary': {
                    'total_alerts': len(alerts),
                    'critical_alerts': len([a for a in alerts if a.severity == 'CRITICAL']),
                    'high_alerts': len([a for a in alerts if a.severity == 'HIGH']),
                    'active_issues': [a.message for a in alerts if a.severity in ['CRITICAL', 'HIGH']]
                },
                'trends': {
                    'compliance_history': historical_compliance[-30:],  # 最近30次记录
                    'compliance_trend': self._calculate_trend(historical_compliance[-7:])  # 最近7次的趋势
                },
                'recommendations': self._generate_priority_recommendations(audit_result, alerts)
            }
            
            async with aiofiles.open(self.dashboard_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(dashboard_data, ensure_ascii=False, indent=2))
                
        except Exception as e:
            self.log_message(f"生成仪表板数据失败: {e}", "ERROR")
    
    async def _get_historical_compliance(self) -> List[float]:
        """获取历史合规率数据"""
        compliance_history = []
        
        try:
            if self.audit_history_file.exists():
                async with aiofiles.open(self.audit_history_file, 'r', encoding='utf-8') as f:
                    async for line in f:
                        if line.strip():
                            audit_data = json.loads(line)
                            compliance_history.append(audit_data.get('mece_compliance_rate', 0))
        except Exception:
            pass
        
        return compliance_history
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势方向"""
        if len(values) < 2:
            return "stable"
        
        recent_avg = sum(values[-3:]) / len(values[-3:]) if len(values) >= 3 else values[-1]
        older_avg = sum(values[:-3]) / len(values[:-3]) if len(values) > 3 else values[0]
        
        if recent_avg > older_avg + 1:
            return "improving"
        elif recent_avg < older_avg - 1:
            return "declining"
        else:
            return "stable"
    
    def _generate_priority_recommendations(self, audit_result: ResourceAuditResult, alerts: List[Alert]) -> List[Dict[str, any]]:
        """生成优先级建议"""
        recommendations = []
        
        # 基于严重程度排序建议
        if audit_result.corrupted_files > 0:
            recommendations.append({
                'priority': 'CRITICAL',
                'action': 'fix_corrupted_files',
                'description': f'立即修复 {audit_result.corrupted_files} 个损坏文件',
                'command': 'python scripts/fix_corrupted_resources.py'
            })
        
        if audit_result.missing_text > 50:
            recommendations.append({
                'priority': 'HIGH',
                'action': 'generate_missing_text',
                'description': f'生成 {audit_result.missing_text} 个缺失文本资源',
                'command': 'python scripts/generate_missing_text_resources.py'
            })
        
        if audit_result.missing_audio > 20:
            recommendations.append({
                'priority': 'HIGH',
                'action': 'generate_missing_audio',
                'description': f'生成 {audit_result.missing_audio} 个缺失音频资源',
                'command': 'python scripts/resource_sync_monitor.py'
            })
        
        if audit_result.orphaned_audio + audit_result.orphaned_text > 100:
            recommendations.append({
                'priority': 'MEDIUM',
                'action': 'cleanup_orphaned_files',
                'description': f'清理 {audit_result.orphaned_audio + audit_result.orphaned_text} 个孤立文件',
                'command': 'python scripts/cleanup_orphaned_resources.py'
            })
        
        return recommendations
    
    async def print_audit_summary(self, audit_result: ResourceAuditResult, alerts: List[Alert]):
        """打印审计摘要"""
        print(f"\n" + "="*70)
        print(f"📊 资源审计报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"="*70)
        
        print(f"\n🎯 总体状况:")
        print(f"  单词总数: {audit_result.total_words}")
        print(f"  完美匹配: {audit_result.perfect_matches} ({audit_result.perfect_matches/audit_result.total_words*100:.1f}%)")
        print(f"  MECE合规率: {audit_result.mece_compliance_rate:.1f}%")
        print(f"  资源完整性: {audit_result.resource_integrity_rate:.1f}%")
        
        print(f"\n❌ 问题统计:")
        print(f"  缺失音频: {audit_result.missing_audio}")
        print(f"  缺失文本: {audit_result.missing_text}")
        print(f"  损坏文件: {audit_result.corrupted_files}")
        print(f"  孤立音频: {audit_result.orphaned_audio}")
        print(f"  孤立文本: {audit_result.orphaned_text}")
        
        print(f"\n📦 存储统计:")
        print(f"  总大小: {audit_result.total_file_size_mb:.1f} MB")
        print(f"  平均音频: {audit_result.avg_audio_size_kb:.1f} KB")
        print(f"  平均文本: {audit_result.avg_text_size_kb:.1f} KB")
        
        print(f"\n🚨 告警信息:")
        if alerts:
            for alert in sorted(alerts, key=lambda x: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'].index(x.severity), reverse=True):
                print(f"  [{alert.severity}] {alert.message}")
                for suggestion in alert.suggestions[:2]:  # 只显示前2个建议
                    print(f"    💡 {suggestion}")
        else:
            print(f"  ✅ 无告警")
        
        print(f"\n📁 输出文件:")
        print(f"  审计历史: {self.audit_history_file}")
        print(f"  告警记录: {self.alerts_file}")
        print(f"  仪表板: {self.dashboard_file}")
        
        print(f"\n⏱️ 审计耗时: {audit_result.audit_duration_seconds:.1f} 秒")

async def main():
    """主函数"""
    print("🔧 资源审计监控器启动")
    
    project_root = Path(__file__).parent.parent
    monitor = ResourceAuditMonitor(str(project_root))
    
    if not monitor.db_path.exists():
        print(f"❌ 数据库文件不存在: {monitor.db_path}")
        return 1
    
    try:
        # 执行完整审计
        audit_result = await monitor.perform_full_audit()
        
        # 评估告警
        alerts = await monitor.evaluate_alerts(audit_result)
        
        # 保存结果
        await monitor.save_audit_history(audit_result)
        await monitor.save_alerts(alerts)
        await monitor.generate_dashboard_data(audit_result, alerts)
        
        # 打印摘要
        await monitor.print_audit_summary(audit_result, alerts)
        
        print(f"\n✅ 资源审计完成!")
        
        # 根据告警级别返回状态码
        critical_alerts = [a for a in alerts if a.severity == 'CRITICAL']
        high_alerts = [a for a in alerts if a.severity == 'HIGH']
        
        if critical_alerts:
            return 2  # 严重问题
        elif high_alerts:
            return 1  # 高优先级问题
        else:
            return 0  # 正常
            
    except Exception as e:
        print(f"❌ 审计执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))