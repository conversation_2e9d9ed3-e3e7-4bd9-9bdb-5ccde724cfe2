#!/usr/bin/env python3
"""
Pattern数据填充脚本
使用PatternDetectorService为所有单词建立pattern关联关系
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.pattern.pattern_detector import PatternDetectorService
from src.core import get_logger
import time

logger = get_logger(__name__)


def main():
    """主函数"""
    db_path = "/Users/<USER>/Documents/Lei_MBP/repo/app_dev/word_learning_app_v5_prod/instance/words.db"
    
    print("开始填充Pattern数据...")
    print("=" * 50)
    
    start_time = time.time()
    
    try:
        # 初始化PatternDetectorService
        detector = PatternDetectorService(db_path)
        
        # 处理所有单词
        stats = detector.process_all_words()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 50)
        print("Pattern数据填充完成！")
        print(f"处理时间: {duration:.2f}秒")
        print(f"总单词数: {stats['total_words']}")
        print(f"成功处理: {stats['processed_words']}")
        print(f"失败数量: {stats['failed_words']}")
        print(f"总关联数: {stats['total_matches']}")
        print(f"平均每词关联: {stats['total_matches'] / stats['processed_words']:.1f}个pattern" if stats['processed_words'] > 0 else "0")
        
        # 验证数据
        print("\n验证数据...")
        verify_data(db_path)
        
    except Exception as e:
        logger.error(f"填充pattern数据失败: {e}")
        print(f"错误: {e}")
        sys.exit(1)


def verify_data(db_path: str):
    """验证填充的数据"""
    import sqlite3
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查总体统计
        cursor.execute("SELECT COUNT(*) FROM word_pattern_relations")
        total_relations = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT word_id) FROM word_pattern_relations")
        words_with_patterns = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM word")
        total_words = cursor.fetchone()[0]
        
        print(f"数据验证结果:")
        print(f"- 总pattern关联数: {total_relations}")
        print(f"- 有pattern的单词数: {words_with_patterns}")
        print(f"- 单词总数: {total_words}")
        print(f"- Pattern覆盖率: {words_with_patterns/total_words*100:.1f}%")
        
        # 检查各pattern类型的分布
        cursor.execute("""
            SELECT wp.pattern_type, COUNT(*) as count
            FROM word_pattern_relations wpr
            JOIN word_patterns wp ON wpr.pattern_id = wp.id
            GROUP BY wp.pattern_type
            ORDER BY count DESC
        """)
        
        print(f"\nPattern类型分布:")
        for pattern_type, count in cursor.fetchall():
            print(f"- {pattern_type}: {count}个关联")
        
        # 检查top patterns
        cursor.execute("""
            SELECT wp.pattern_name, COUNT(*) as word_count
            FROM word_pattern_relations wpr
            JOIN word_patterns wp ON wpr.pattern_id = wp.id
            GROUP BY wp.id, wp.pattern_name
            ORDER BY word_count DESC
            LIMIT 10
        """)
        
        print(f"\nTop 10 Patterns:")
        for pattern_name, word_count in cursor.fetchall():
            print(f"- {pattern_name}: {word_count}个单词")
        
        # 随机展示几个示例
        cursor.execute("""
            SELECT w.english_word, wp.pattern_name, wpr.match_reason
            FROM word_pattern_relations wpr
            JOIN word w ON wpr.word_id = w.id
            JOIN word_patterns wp ON wpr.pattern_id = wp.id
            WHERE wpr.is_primary = 1
            ORDER BY RANDOM()
            LIMIT 5
        """)
        
        print(f"\n示例单词pattern:")
        for english_word, pattern_name, match_reason in cursor.fetchall():
            print(f"- {english_word}: {pattern_name} ({match_reason})")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"验证数据失败: {e}")
        print(f"验证错误: {e}")


if __name__ == "__main__":
    main()