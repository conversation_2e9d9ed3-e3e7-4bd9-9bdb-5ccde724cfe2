#!/bin/bash

# QNAP部署诊断脚本
# 用于排查部署后无法访问的问题

set -e

echo "🔍 Word Learning App v5 QNAP部署诊断工具"
echo "========================================="

# 获取QNAP密码
echo -n "请输入QNAP密码: "
read -s QNAP_PASSWORD
echo
export SSHPASS="$QNAP_PASSWORD"

QNAP_HOST="lesong@*************"
CONTAINER_NAME="word_learning_app_v5"

echo "🔗 1. 测试QNAP连接..."
if sshpass -e ssh -o ConnectTimeout=10 $QNAP_HOST "echo 'SSH连接成功'" 2>/dev/null; then
    echo "✅ SSH连接正常"
else
    echo "❌ SSH连接失败"
    exit 1
fi

echo ""
echo "🐳 2. 检查Docker容器状态..."
DOCKER="/share/ZFS530_DATA/.qpkg/container-station/bin/docker"

# 检查容器是否存在和运行状态
CONTAINER_STATUS=$(sshpass -e ssh $QNAP_HOST "$DOCKER ps -a -f name=$CONTAINER_NAME --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'" 2>/dev/null || echo "容器不存在")
echo "$CONTAINER_STATUS"

# 检查容器是否正在运行
IS_RUNNING=$(sshpass -e ssh $QNAP_HOST "$DOCKER ps -f name=$CONTAINER_NAME --format '{{.Names}}'" 2>/dev/null || echo "")
if [ -z "$IS_RUNNING" ]; then
    echo "❌ 容器未运行，正在检查容器日志..."
    echo ""
    echo "📋 容器日志（最后20行）:"
    sshpass -e ssh $QNAP_HOST "$DOCKER logs --tail 20 $CONTAINER_NAME" 2>/dev/null || echo "无法获取日志"
    
    echo ""
    echo "🔄 尝试重启容器..."
    sshpass -e ssh $QNAP_HOST "$DOCKER start $CONTAINER_NAME" 2>/dev/null || echo "重启失败"
    sleep 5
    
    # 再次检查状态
    NEW_STATUS=$(sshpass -e ssh $QNAP_HOST "$DOCKER ps -f name=$CONTAINER_NAME --format '{{.Status}}'" 2>/dev/null || echo "仍未运行")
    echo "重启后状态: $NEW_STATUS"
else
    echo "✅ 容器正在运行"
fi

echo ""
echo "🌐 3. 检查网络连通性..."

# 检查端口监听
echo "检查QNAP端口5003监听状态:"
PORT_STATUS=$(sshpass -e ssh $QNAP_HOST "netstat -ln | grep :5003" 2>/dev/null || echo "端口5003未监听")
echo "$PORT_STATUS"

# 检查容器内部端口5005
echo ""
echo "检查容器内部端口5005:"
CONTAINER_PORTS=$(sshpass -e ssh $QNAP_HOST "$DOCKER port $CONTAINER_NAME" 2>/dev/null || echo "无法获取端口信息")
echo "$CONTAINER_PORTS"

echo ""
echo "🔍 4. 检查应用健康状态..."

# 检查容器内部应用是否启动
echo "检查容器内部应用进程:"
APP_PROCESS=$(sshpass -e ssh $QNAP_HOST "$DOCKER exec $CONTAINER_NAME ps aux | grep python" 2>/dev/null || echo "无法检查应用进程")
echo "$APP_PROCESS"

# 尝试从容器内部访问应用
echo ""
echo "从容器内部测试应用响应:"
INTERNAL_HEALTH=$(sshpass -e ssh $QNAP_HOST "$DOCKER exec $CONTAINER_NAME curl -s -o /dev/null -w '%{http_code}' http://localhost:5005/health" 2>/dev/null || echo "内部访问失败")
echo "内部健康检查响应码: $INTERNAL_HEALTH"

# 从QNAP主机测试应用
echo ""
echo "从QNAP主机测试应用响应:"
HOST_HEALTH=$(sshpass -e ssh $QNAP_HOST "curl -s -o /dev/null -w '%{http_code}' http://localhost:5003/health" 2>/dev/null || echo "主机访问失败")
echo "主机健康检查响应码: $HOST_HEALTH"

echo ""
echo "📁 5. 检查挂载卷状态..."

# 检查挂载目录是否存在
echo "检查静态资源挂载目录:"
STATIC_DIR_STATUS=$(sshpass -e ssh $QNAP_HOST "ls -la /share/homes/lesong/word_learning_app_v5/static/ | head -5" 2>/dev/null || echo "静态目录不存在或无法访问")
echo "$STATIC_DIR_STATUS"

# 检查数据库挂载目录
echo ""
echo "检查数据库挂载目录:"
DB_DIR_STATUS=$(sshpass -e ssh $QNAP_HOST "ls -la /share/homes/lesong/word_learning_app_v5/instance/" 2>/dev/null || echo "数据库目录不存在或无法访问")
echo "$DB_DIR_STATUS"

# 检查容器内挂载情况
echo ""
echo "检查容器内挂载情况:"
CONTAINER_MOUNTS=$(sshpass -e ssh $QNAP_HOST "$DOCKER exec $CONTAINER_NAME df -h | grep '/app'" 2>/dev/null || echo "无法检查容器挂载")
echo "$CONTAINER_MOUNTS"

echo ""
echo "🔧 6. 尝试解决方案建议..."

if [ "$INTERNAL_HEALTH" = "200" ] && [ "$HOST_HEALTH" != "200" ]; then
    echo "💡 应用内部正常但外部无法访问，可能是端口映射问题"
    echo "   建议: 检查QNAP防火墙设置或重新创建容器"
elif [ "$INTERNAL_HEALTH" != "200" ]; then
    echo "💡 应用内部异常，可能的原因:"
    echo "   1. 应用启动失败 - 检查容器日志"
    echo "   2. 数据库连接问题 - 检查挂载卷"
    echo "   3. 依赖缺失 - 重新构建镜像"
    
    echo ""
    echo "🔄 建议操作:"
    echo "   1. 查看详细日志: docker logs $CONTAINER_NAME"
    echo "   2. 进入容器调试: docker exec -it $CONTAINER_NAME /bin/bash"
    echo "   3. 重新部署: ./deploy.sh"
else
    echo "✅ 应用看起来正常，可能是网络延迟问题"
fi

echo ""
echo "🌐 7. 最终访问测试..."
echo "请尝试访问: http://*************:5003"
echo "健康检查端点: http://*************:5003/health"

# 从本地机器ping测试
echo ""
echo "从本地ping测试QNAP:"
ping -c 3 ************* || echo "ping失败"

echo ""
echo "🏁 诊断完成！"