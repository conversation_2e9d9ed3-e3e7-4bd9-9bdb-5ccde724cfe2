#!/usr/bin/env python3
"""
自动化静态资源重复文件解决方案
全自动检测、分析、合并重复文件，无需手动干预
"""

import os
import sys
import sqlite3
import shutil
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import hashlib
import re

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class AutoDuplicateResolver:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / 'instance' / 'words.db'
        self.audio_dir = self.project_root / 'static' / 'audio' / 'words'
        self.image_dir = self.project_root / 'static' / 'images' / 'words'
        self.backup_dir = self.project_root / 'static' / 'backup' / f'duplicates_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        
        # 创建备份目录
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        self.resolution_log = []
    
    def normalize_word_for_filename(self, word):
        """标准化单词为文件名格式"""
        # 更强的标准化规则
        normalized = word.lower()
        
        # 处理特殊的时间表达
        if 'a.m.' in normalized or 'p.m.' in normalized:
            normalized = normalized.replace('a.m./p.m.', 'a_m_p_m')
            normalized = normalized.replace('a.m.', 'a_m')
            normalized = normalized.replace('p.m.', 'p_m')
        
        # 标准替换
        normalized = normalized.replace(' ', '_')
        normalized = normalized.replace("'", "")
        normalized = normalized.replace('"', '')
        normalized = normalized.replace('.', '')
        normalized = normalized.replace('/', '_')
        normalized = normalized.replace('-', '_')
        normalized = normalized.replace('(', '')
        normalized = normalized.replace(')', '')
        
        # 清理多个连续下划线
        normalized = re.sub(r'_+', '_', normalized)
        normalized = normalized.strip('_')
        
        return normalized
    
    def get_file_quality_score(self, file_path):
        """计算文件质量分数，用于选择最佳版本"""
        if not file_path.exists():
            return 0
        
        score = 0
        file_size = file_path.stat().st_size
        
        # 文件大小分数 (更大的文件通常质量更好)
        score += min(file_size / 1000, 100)  # 最多100分
        
        # 命名规范分数
        name = file_path.stem
        if '_' in name and ' ' not in name:  # 使用下划线而非空格
            score += 20
        if not any(char in name for char in ['..', '__', '  ']):  # 没有连续特殊字符
            score += 10
        if name.islower():  # 全小写
            score += 10
        
        # 文件完整性检查
        try:
            with open(file_path, 'rb') as f:
                # 检查文件头
                header = f.read(100)
                if file_path.suffix == '.mp3' and header.startswith(b'ID3'):
                    score += 20
                elif file_path.suffix == '.jpg' and header.startswith(b'\xff\xd8'):
                    score += 20
        except:
            score -= 50  # 文件损坏
        
        return score
    
    def find_duplicate_groups(self, directory, extension):
        """查找重复文件组"""
        files = list(directory.glob(f'*{extension}'))
        
        # 按标准化后的名称分组
        groups = defaultdict(list)
        for file_path in files:
            normalized = self.normalize_word_for_filename(file_path.stem)
            groups[normalized].append(file_path)
        
        # 只返回有重复的组
        duplicate_groups = {k: v for k, v in groups.items() if len(v) > 1}
        
        return duplicate_groups
    
    def resolve_duplicate_group(self, normalized_name, file_list, target_dir):
        """解决一组重复文件"""
        if len(file_list) <= 1:
            return None
        
        print(f"\n🔍 处理重复文件组: {normalized_name}")
        print(f"   发现 {len(file_list)} 个重复文件:")
        
        # 计算每个文件的质量分数
        file_scores = []
        for file_path in file_list:
            score = self.get_file_quality_score(file_path)
            file_scores.append((file_path, score))
            print(f"   - {file_path.name} (质量分数: {score:.1f})")
        
        # 选择最佳文件
        file_scores.sort(key=lambda x: x[1], reverse=True)
        best_file, best_score = file_scores[0]
        
        # 目标文件名
        target_extension = best_file.suffix
        target_name = f"{normalized_name}{target_extension}"
        target_path = target_dir / target_name
        
        # 备份所有文件
        backup_group_dir = self.backup_dir / f"{normalized_name}_group"
        backup_group_dir.mkdir(exist_ok=True)
        
        actions_taken = []
        
        # 处理每个文件
        for file_path, score in file_scores:
            backup_path = backup_group_dir / file_path.name
            shutil.copy2(file_path, backup_path)
            
            if file_path == best_file:
                # 这是最佳文件
                if file_path.name != target_name:
                    # 需要重命名
                    if target_path.exists():
                        # 目标文件已存在，比较质量
                        target_score = self.get_file_quality_score(target_path)
                        if best_score > target_score:
                            # 当前文件更好，替换目标文件
                            shutil.move(str(target_path), str(backup_group_dir / f"replaced_{target_path.name}"))
                            shutil.move(str(file_path), str(target_path))
                            actions_taken.append(f"重命名并替换: {file_path.name} -> {target_name}")
                        else:
                            # 目标文件更好，删除当前文件
                            file_path.unlink()
                            actions_taken.append(f"删除较差版本: {file_path.name}")
                    else:
                        # 直接重命名
                        shutil.move(str(file_path), str(target_path))
                        actions_taken.append(f"重命名: {file_path.name} -> {target_name}")
                else:
                    # 文件名已经正确
                    actions_taken.append(f"保留最佳文件: {file_path.name}")
            else:
                # 这是较差的文件，删除
                file_path.unlink()
                actions_taken.append(f"删除重复文件: {file_path.name}")
        
        result = {
            'normalized_name': normalized_name,
            'original_files': [f.name for f in file_list],
            'best_file': best_file.name,
            'final_name': target_name,
            'actions': actions_taken,
            'backup_location': str(backup_group_dir)
        }
        
        self.resolution_log.append(result)
        print(f"   ✅ 解决完成: 保留 {target_name}")
        
        return result
    
    def get_database_words(self):
        """从数据库获取所有单词"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT english_word FROM word ORDER BY english_word")
            words = [row[0] for row in cursor.fetchall()]
            conn.close()
            return words
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return []
    
    def create_missing_file_placeholders(self):
        """为缺失的资源创建占位符文件或提醒"""
        db_words = self.get_database_words()
        missing_audio = []
        missing_images = []
        
        for word in db_words:
            normalized = self.normalize_word_for_filename(word)
            
            # 检查音频文件
            audio_file = self.audio_dir / f"{normalized}.mp3"
            if not audio_file.exists():
                missing_audio.append(word)
            
            # 检查图片文件
            image_file = self.image_dir / f"{normalized}.jpg"
            if not image_file.exists():
                missing_images.append(word)
        
        return missing_audio, missing_images
    
    def auto_resolve_all_duplicates(self):
        """自动解决所有重复文件"""
        print("🚀 开始自动化重复文件解决方案")
        print(f"📁 项目路径: {self.project_root}")
        print(f"💾 备份路径: {self.backup_dir}")
        
        total_resolved = 0
        
        # 处理音频重复文件
        print("\n🎵 处理音频文件重复...")
        audio_duplicates = self.find_duplicate_groups(self.audio_dir, '.mp3')
        print(f"发现 {len(audio_duplicates)} 组重复音频文件")
        
        for normalized_name, file_list in audio_duplicates.items():
            result = self.resolve_duplicate_group(normalized_name, file_list, self.audio_dir)
            if result:
                total_resolved += len(result['original_files']) - 1
        
        # 处理图片重复文件
        print("\n🖼️  处理图片文件重复...")
        image_duplicates = self.find_duplicate_groups(self.image_dir, '.jpg')
        print(f"发现 {len(image_duplicates)} 组重复图片文件")
        
        for normalized_name, file_list in image_duplicates.items():
            result = self.resolve_duplicate_group(normalized_name, file_list, self.image_dir)
            if result:
                total_resolved += len(result['original_files']) - 1
        
        # 检查缺失文件
        print("\n🔍 检查缺失资源...")
        missing_audio, missing_images = self.create_missing_file_placeholders()
        
        # 生成完整报告
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_duplicates_resolved': total_resolved,
                'audio_duplicate_groups': len(audio_duplicates),
                'image_duplicate_groups': len(image_duplicates),
                'missing_audio_files': len(missing_audio),
                'missing_image_files': len(missing_images)
            },
            'resolution_details': self.resolution_log,
            'missing_resources': {
                'audio': missing_audio,
                'images': missing_images
            },
            'backup_location': str(self.backup_dir)
        }
        
        # 保存报告
        report_path = self.project_root / f'duplicate_resolution_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n{'='*80}")
        print("📋 自动化重复文件解决完成!")
        print(f"{'='*80}")
        print(f"✅ 总计解决重复文件: {total_resolved} 个")
        print(f"🎵 音频重复组: {len(audio_duplicates)} 组")
        print(f"🖼️  图片重复组: {len(image_duplicates)} 组")
        print(f"❌ 缺失音频文件: {len(missing_audio)} 个")
        print(f"❌ 缺失图片文件: {len(missing_images)} 个")
        print(f"💾 备份位置: {self.backup_dir}")
        print(f"📊 详细报告: {report_path}")
        
        if missing_audio or missing_images:
            print(f"\n⚠️  仍有 {len(missing_audio)} 个音频和 {len(missing_images)} 个图片文件缺失")
            print("这些文件需要重新生成或从其他源获取")
        
        return report

def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    
    # 创建解决器
    resolver = AutoDuplicateResolver(project_root)
    
    # 执行自动解决
    report = resolver.auto_resolve_all_duplicates()
    
    return report

if __name__ == '__main__':
    main()