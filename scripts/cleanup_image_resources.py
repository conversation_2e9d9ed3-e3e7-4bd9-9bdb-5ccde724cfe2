#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片资源MECE清理脚本
完成音频和文本清理后，对图片文件进行标准化命名和MECE合规性处理

功能：
1. 分析图片文件与数据库单词的对应关系
2. 标准化图片文件命名 (word_XXXX.jpg)
3. 清理无对应单词的孤立图片文件
4. 创建完整的资源映射关系
5. 生成MECE合规性报告
"""

import os
import sys
import sqlite3
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Set
import re

# 添加项目根目录到路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

class ImageResourceCleaner:
    """图片资源清理器"""
    
    def __init__(self, project_root: str, dry_run: bool = True):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / "instance" / "words.db"
        self.images_dir = self.project_root / "static" / "images" / "words"
        self.backup_dir = self.project_root / "backups" / f"image_cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.log_file = self.project_root / "logs" / "image_cleanup.log"
        self.report_file = self.project_root / "logs" / "image_cleanup_report.json"
        
        self.dry_run = dry_run
        
        # 确保必要目录存在
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        if not dry_run:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 处理统计
        self.stats = {
            'total_images': 0,
            'matched_images': 0,
            'renamed_images': 0,
            'orphaned_images': 0,
            'deleted_images': 0,
            'preserved_images': 0,
            'errors': 0
        }
        
        # 匹配模式
        self.filename_patterns = [
            r'^(.+)\.jpg$',  # 基本模式
            r'^(.+)\.jpeg$', # JPEG扩展名
            r'^(.+)\.png$',  # PNG格式
        ]
    
    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception:
            pass
        
        print(f"{log_entry.strip()}")
    
    def get_standardized_filename(self, word_id: int) -> str:
        """获取标准化图片文件名"""
        return f"word_{word_id:04d}.jpg"
    
    def get_all_words(self) -> Dict[str, Tuple[int, str]]:
        """获取所有单词映射"""
        word_map = {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, english_word, chinese_meaning 
                FROM word 
                ORDER BY id
            """)
            
            for word_id, english_word, chinese_meaning in cursor.fetchall():
                # 创建多种可能的匹配键
                possible_keys = [
                    english_word.lower(),
                    english_word.lower().replace(' ', '_'),
                    english_word.lower().replace(' ', ''),
                    english_word.lower().replace(' ', '-'),
                    english_word.lower().replace('/', '_'),
                    english_word.lower().replace('.', ''),
                ]
                
                for key in possible_keys:
                    if key not in word_map:
                        word_map[key] = (word_id, english_word)
            
            conn.close()
            self.log_message(f"✅ 加载 {len(set(item[0] for item in word_map.values()))} 个唯一单词")
            
        except Exception as e:
            self.log_message(f"❌ 数据库查询失败: {e}", "ERROR")
            self.stats['errors'] += 1
            
        return word_map
    
    def normalize_filename(self, filename: str) -> str:
        """标准化文件名用于匹配"""
        # 移除扩展名
        name = filename.rsplit('.', 1)[0]
        
        # 转换为小写
        name = name.lower()
        
        # 清理特殊字符
        name = re.sub(r'[^\w\s-]', '', name)
        name = re.sub(r'\s+', ' ', name).strip()
        
        return name
    
    def find_word_match(self, filename: str, word_map: Dict[str, Tuple[int, str]]) -> Optional[Tuple[int, str]]:
        """为图片文件名找到对应的单词"""
        normalized = self.normalize_filename(filename)
        
        # 尝试直接匹配
        if normalized in word_map:
            return word_map[normalized]
        
        # 尝试不同的变体
        variants = [
            normalized.replace(' ', '_'),
            normalized.replace(' ', ''),
            normalized.replace(' ', '-'),
            normalized.replace('_', ' '),
            normalized.replace('-', ' '),
        ]
        
        for variant in variants:
            if variant in word_map:
                return word_map[variant]
        
        # 尝试部分匹配
        for key, value in word_map.items():
            if (normalized in key) or (key in normalized):
                return value
        
        return None
    
    def analyze_images(self) -> Dict[str, any]:
        """分析图片文件"""
        if not self.images_dir.exists():
            self.log_message(f"❌ 图片目录不存在: {self.images_dir}", "ERROR")
            return {}
        
        word_map = self.get_all_words()
        analysis_result = {
            'matched_files': [],
            'orphaned_files': [],
            'rename_operations': [],
            'conflicts': []
        }
        
        # 获取所有图片文件
        image_files = []
        for pattern in ['*.jpg', '*.jpeg', '*.png', '*.JPG', '*.JPEG', '*.PNG']:
            image_files.extend(self.images_dir.glob(pattern))
        
        self.stats['total_images'] = len(image_files)
        self.log_message(f"📊 发现 {len(image_files)} 个图片文件")
        
        for image_file in image_files:
            filename = image_file.name
            
            # 跳过已经标准化的文件
            if re.match(r'^word_\d{4}\.(jpg|jpeg|png)$', filename.lower()):
                self.stats['matched_images'] += 1
                analysis_result['matched_files'].append({
                    'file': filename,
                    'status': 'already_standardized'
                })
                continue
            
            # 尝试找到对应的单词
            match = self.find_word_match(filename, word_map)
            
            if match:
                word_id, english_word = match
                standard_filename = self.get_standardized_filename(word_id)
                
                self.stats['matched_images'] += 1
                analysis_result['matched_files'].append({
                    'file': filename,
                    'word_id': word_id,
                    'english_word': english_word,
                    'new_filename': standard_filename,
                    'status': 'needs_rename'
                })
                
                analysis_result['rename_operations'].append({
                    'old_path': str(image_file),
                    'new_path': str(self.images_dir / standard_filename),
                    'word_id': word_id,
                    'english_word': english_word
                })
                
            else:
                self.stats['orphaned_images'] += 1
                analysis_result['orphaned_files'].append({
                    'file': filename,
                    'path': str(image_file),
                    'size': image_file.stat().st_size,
                    'status': 'orphaned'
                })
        
        self.log_message(f"📊 分析完成: 匹配 {self.stats['matched_images']}, 孤立 {self.stats['orphaned_images']}")
        return analysis_result
    
    def backup_file(self, file_path: Path) -> bool:
        """备份文件"""
        if self.dry_run:
            return True
            
        try:
            backup_path = self.backup_dir / file_path.name
            shutil.copy2(file_path, backup_path)
            return True
        except Exception as e:
            self.log_message(f"❌ 备份文件失败 {file_path.name}: {e}", "ERROR")
            self.stats['errors'] += 1
            return False
    
    def execute_rename_operations(self, rename_operations: List[Dict]) -> Dict[str, int]:
        """执行重命名操作"""
        results = {'success': 0, 'failed': 0, 'skipped': 0}
        
        for operation in rename_operations:
            old_path = Path(operation['old_path'])
            new_path = Path(operation['new_path'])
            
            if not old_path.exists():
                self.log_message(f"⚠️ 源文件不存在: {old_path.name}", "WARN")
                results['skipped'] += 1
                continue
            
            if new_path.exists():
                self.log_message(f"⚠️ 目标文件已存在，跳过: {new_path.name}", "WARN")
                results['skipped'] += 1
                continue
            
            if self.dry_run:
                self.log_message(f"🔍 [DRY-RUN] 重命名: {old_path.name} -> {new_path.name}")
                results['success'] += 1
                continue
            
            # 执行重命名
            try:
                # 先备份
                if self.backup_file(old_path):
                    old_path.rename(new_path)
                    self.log_message(f"✅ 重命名成功: {old_path.name} -> {new_path.name}")
                    results['success'] += 1
                    self.stats['renamed_images'] += 1
                else:
                    results['failed'] += 1
                    
            except Exception as e:
                self.log_message(f"❌ 重命名失败 {old_path.name}: {e}", "ERROR")
                results['failed'] += 1
                self.stats['errors'] += 1
        
        return results
    
    def cleanup_orphaned_files(self, orphaned_files: List[Dict]) -> Dict[str, int]:
        """清理孤立文件"""
        results = {'deleted': 0, 'preserved': 0, 'failed': 0}
        
        # 分析孤立文件，决定删除还是保留
        for file_info in orphaned_files:
            file_path = Path(file_info['path'])
            filename = file_info['file']
            
            if not file_path.exists():
                continue
            
            # 保留条件：特殊文件或小文件可能有价值
            should_preserve = (
                file_path.stat().st_size < 1024 or  # 很小的文件
                any(keyword in filename.lower() for keyword in ['logo', 'icon', 'banner', 'template'])
            )
            
            if should_preserve:
                self.log_message(f"🔒 保留文件: {filename} (特殊文件)")
                results['preserved'] += 1
                self.stats['preserved_images'] += 1
                continue
            
            if self.dry_run:
                self.log_message(f"🔍 [DRY-RUN] 删除: {filename}")
                results['deleted'] += 1
                continue
            
            # 执行删除
            try:
                # 先备份
                if self.backup_file(file_path):
                    file_path.unlink()
                    self.log_message(f"🗑️ 删除成功: {filename}")
                    results['deleted'] += 1
                    self.stats['deleted_images'] += 1
                else:
                    results['failed'] += 1
                    
            except Exception as e:
                self.log_message(f"❌ 删除失败 {filename}: {e}", "ERROR")
                results['failed'] += 1
                self.stats['errors'] += 1
        
        return results
    
    def generate_final_report(self, analysis_result: Dict, rename_results: Dict, cleanup_results: Dict):
        """生成最终报告"""
        report = {
            'execution_time': datetime.now().isoformat(),
            'dry_run': self.dry_run,
            'statistics': self.stats,
            'analysis_summary': {
                'total_files_analyzed': len(analysis_result.get('matched_files', [])) + len(analysis_result.get('orphaned_files', [])),
                'matched_files': len(analysis_result.get('matched_files', [])),
                'orphaned_files': len(analysis_result.get('orphaned_files', [])),
            },
            'operation_results': {
                'rename_operations': rename_results,
                'cleanup_operations': cleanup_results
            },
            'file_details': {
                'renamed_files': [op for op in analysis_result.get('rename_operations', [])],
                'orphaned_files': analysis_result.get('orphaned_files', [])[:20],  # 只记录前20个
            }
        }
        
        try:
            with open(self.report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            self.log_message(f"📊 报告已保存: {self.report_file}")
        except Exception as e:
            self.log_message(f"❌ 保存报告失败: {e}", "ERROR")
    
    def run_cleanup(self) -> bool:
        """执行完整清理流程"""
        self.log_message(f"🚀 开始图片资源清理 (DRY_RUN: {self.dry_run})")
        
        try:
            # 1. 分析图片文件
            analysis_result = self.analyze_images()
            
            # 2. 执行重命名操作
            rename_results = self.execute_rename_operations(
                analysis_result.get('rename_operations', [])
            )
            
            # 3. 清理孤立文件
            cleanup_results = self.cleanup_orphaned_files(
                analysis_result.get('orphaned_files', [])
            )
            
            # 4. 生成报告
            self.generate_final_report(analysis_result, rename_results, cleanup_results)
            
            self.log_message("✅ 图片资源清理完成")
            return True
            
        except Exception as e:
            self.log_message(f"❌ 清理过程异常: {e}", "ERROR")
            self.stats['errors'] += 1
            return False
    
    def print_summary(self):
        """打印清理摘要"""
        print(f"\n" + "="*60)
        print(f"📊 图片资源清理摘要")
        print(f"="*60)
        print(f"模式: {'DRY-RUN (预览)' if self.dry_run else 'EXECUTE (执行)'}")
        print(f"\n📈 处理统计:")
        print(f"  总计图片: {self.stats['total_images']}")
        print(f"  匹配成功: {self.stats['matched_images']}")
        print(f"  重命名文件: {self.stats['renamed_images']}")
        print(f"  孤立文件: {self.stats['orphaned_images']}")
        print(f"  删除文件: {self.stats['deleted_images']}")
        print(f"  保留文件: {self.stats['preserved_images']}")
        print(f"  处理错误: {self.stats['errors']}")
        
        if self.dry_run:
            print(f"\n💡 这是预览模式，实际文件未被修改")
            print(f"如需执行实际操作，请使用 --force 参数")
        else:
            print(f"\n📁 备份目录: {self.backup_dir}")
        
        print(f"📋 详细日志: {self.log_file}")
        print(f"📊 完整报告: {self.report_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="图片资源MECE清理脚本")
    parser.add_argument('--force', action='store_true', help='执行实际操作（否则为预览模式）')
    parser.add_argument('--project-root', default=None, help='项目根目录路径')
    args = parser.parse_args()
    
    project_root = args.project_root or Path(__file__).parent.parent
    dry_run = not args.force
    
    print(f"🔧 图片资源清理脚本启动")
    print(f"📂 项目路径: {project_root}")
    print(f"🔍 运行模式: {'预览模式' if dry_run else '执行模式'}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    cleaner = ImageResourceCleaner(str(project_root), dry_run=dry_run)
    
    # 检查必要条件
    if not cleaner.db_path.exists():
        print(f"❌ 数据库文件不存在: {cleaner.db_path}")
        return 1
    
    if not cleaner.images_dir.exists():
        print(f"❌ 图片目录不存在: {cleaner.images_dir}")
        return 1
    
    try:
        success = cleaner.run_cleanup()
        cleaner.print_summary()
        
        if success:
            print(f"\n✅ 图片资源清理完成!")
            return 0
        else:
            print(f"\n❌ 图片资源清理失败!")
            return 1
            
    except Exception as e:
        print(f"\n❌ 脚本执行异常: {e}")
        return 1
    finally:
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    exit(main())