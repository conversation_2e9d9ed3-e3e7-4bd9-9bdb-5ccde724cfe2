#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缺失文本资源生成脚本
优先解决297个缺失文本资源的问题，恢复MECE合规性

功能：
1. 识别所有缺失的文本资源
2. 为每个缺失的单词生成记忆辅助文本
3. 使用标准化命名规范
4. 支持批量生成和增量更新
5. 集成AI辅助内容生成
"""

import os
import sys
import sqlite3
import asyncio
import aiofiles
import httpx
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import json

# 添加项目根目录到路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

class MissingTextGenerator:
    """缺失文本资源生成器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.db_path = self.project_root / "instance" / "words.db"
        self.text_dir = self.project_root / "static" / "cache" / "memory_help"
        self.log_file = self.project_root / "logs" / "text_generation.log"
        self.progress_file = self.project_root / "logs" / "text_generation_progress.json"
        
        # 确保目录存在
        self.text_dir.mkdir(parents=True, exist_ok=True)
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # AI服务配置
        self.ai_api_url = "http://localhost:5005/api/generate_memory_help"
        
        # 生成进度跟踪
        self.generation_progress = {
            'total_missing': 0,
            'generated': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': None,
            'last_update': None,
            'completed_words': []
        }
    
    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception:
            pass
        
        print(f"{log_entry.strip()}")
    
    def get_standardized_filename(self, word_id: int) -> str:
        """获取标准化文本文件名"""
        return f"word_{word_id:04d}.txt"
    
    async def get_missing_text_words(self) -> List[Tuple[int, str, str]]:
        """获取所有缺失文本文件的单词"""
        missing_words = []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, english_word, chinese_meaning 
                FROM word 
                ORDER BY id
            """)
            
            all_words = cursor.fetchall()
            conn.close()
            
            # 检查哪些单词缺失文本文件
            for word_id, english_word, chinese_meaning in all_words:
                text_filename = self.get_standardized_filename(word_id)
                text_path = self.text_dir / text_filename
                
                if not text_path.exists():
                    missing_words.append((word_id, english_word, chinese_meaning))
            
            self.log_message(f"✅ 发现 {len(missing_words)} 个缺失文本资源的单词")
            
        except Exception as e:
            self.log_message(f"❌ 查询缺失单词失败: {e}", "ERROR")
            
        return missing_words
    
    def generate_enhanced_memory_content(self, english_word: str, chinese_meaning: str) -> str:
        """生成增强的记忆辅助内容"""
        
        # 基础词汇分析
        word_length = len(english_word)
        has_common_prefix = any(english_word.lower().startswith(prefix) for prefix in 
                               ['un', 'in', 'dis', 'pre', 'pro', 'anti', 'auto'])
        has_common_suffix = any(english_word.lower().endswith(suffix) for suffix in 
                               ['ing', 'ed', 'ly', 'tion', 'ness', 'ment', 'able'])
        
        # 生成记忆技巧
        memory_tips = []
        
        if word_length <= 4:
            memory_tips.append("**短词记忆**: 这是一个短单词，可以通过多次重复快速记忆")
        elif word_length >= 8:
            memory_tips.append("**长词分解**: 建议将单词分解为小段来记忆")
            
        if has_common_prefix:
            memory_tips.append("**前缀识别**: 注意单词的前缀，它通常有特定含义")
            
        if has_common_suffix:
            memory_tips.append("**后缀识别**: 注意单词的后缀，有助于理解词性和含义")
        
        # 生成联想提示
        association_hints = []
        
        # 基于首字母的联想
        first_letter = english_word[0].upper()
        association_hints.append(f"**首字母联想**: 以'{first_letter}'开头，可以联想相关概念")
        
        # 基于中文含义的联想
        if '的' in chinese_meaning:
            association_hints.append("**形容词特征**: 这是一个描述性词汇，注意其修饰作用")
        elif any(char in chinese_meaning for char in ['做', '进行', '完成']):
            association_hints.append("**动作记忆**: 这是一个动作词汇，可以通过动作联想记忆")
        
        # 组装完整内容
        content = f"""# {english_word}

## 📝 基本信息
- **英文单词**: {english_word}
- **中文释义**: {chinese_meaning}
- **单词长度**: {word_length} 个字母
- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🧠 记忆技巧

### 核心记忆策略
{chr(10).join(f"- {tip}" for tip in memory_tips)}

### 联想记忆
{chr(10).join(f"- {hint}" for hint in association_hints)}

### 发音要点
- **重音位置**: 待标注（请根据实际发音确定）
- **发音难点**: 待补充（请注意容易读错的部分）

## 📚 使用指南

### 学习阶段
1. **初识阶段**: 重点记忆中文释义和基本拼写
2. **熟悉阶段**: 注意单词在句子中的使用
3. **掌握阶段**: 能够在不同语境中正确使用

### 常见搭配
- 待补充常用短语和搭配

### 例句练习
- 待补充实用例句

## 🔗 相关扩展

### 同根词汇
- 待补充同词根的相关单词

### 近义词
- 待补充意思相近的单词

### 反义词
- 待补充意思相反的单词

## 💡 学习小贴士

- **重复频率**: 建议每天复习1-2次，直到熟练掌握
- **应用练习**: 尝试在日常对话或写作中使用这个单词
- **错误记录**: 如果经常拼错，重点练习容易出错的字母

## 📊 进度追踪

### 掌握程度自评
- [ ] 能够识别单词
- [ ] 知道中文意思  
- [ ] 能够正确拼写
- [ ] 能够正确发音
- [ ] 能够在语境中使用

---
*本文档由系统自动生成，建议根据个人学习情况进行补充和完善*
"""
        
        return content
    
    async def generate_ai_enhanced_content(self, english_word: str, chinese_meaning: str) -> Optional[str]:
        """尝试使用AI服务生成增强内容"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.ai_api_url,
                    json={
                        "word": english_word,
                        "meaning": chinese_meaning
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success') and result.get('content'):
                        return result['content']
                        
        except Exception as e:
            self.log_message(f"⚠️ AI内容生成失败，使用基础模板: {english_word} - {e}", "WARN")
            
        return None
    
    async def generate_single_text_resource(self, word_id: int, english_word: str, chinese_meaning: str) -> bool:
        """生成单个文本资源"""
        text_filename = self.get_standardized_filename(word_id)
        text_path = self.text_dir / text_filename
        
        # 如果文件已存在，跳过
        if text_path.exists():
            self.generation_progress['skipped'] += 1
            return True
        
        try:
            # 尝试AI增强内容生成
            content = await self.generate_ai_enhanced_content(english_word, chinese_meaning)
            
            # 如果AI生成失败，使用基础模板
            if not content:
                content = self.generate_enhanced_memory_content(english_word, chinese_meaning)
            
            # 保存文件
            async with aiofiles.open(text_path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            self.generation_progress['generated'] += 1
            self.generation_progress['completed_words'].append(english_word)
            self.log_message(f"✅ 文本生成成功: {english_word} -> {text_filename}")
            
            return True
            
        except Exception as e:
            self.generation_progress['failed'] += 1
            self.log_message(f"❌ 文本生成失败: {english_word} - {e}", "ERROR")
            return False
    
    async def save_progress(self):
        """保存生成进度"""
        self.generation_progress['last_update'] = datetime.now().isoformat()
        
        try:
            async with aiofiles.open(self.progress_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(self.generation_progress, ensure_ascii=False, indent=2))
        except Exception as e:
            self.log_message(f"❌ 保存进度失败: {e}", "ERROR")
    
    async def generate_all_missing_texts(self, batch_size: int = 20) -> Dict[str, int]:
        """批量生成所有缺失的文本资源"""
        missing_words = await self.get_missing_text_words()
        
        if not missing_words:
            self.log_message("✅ 没有发现缺失的文本资源")
            return {'total': 0, 'generated': 0, 'failed': 0, 'skipped': 0}
        
        # 初始化进度跟踪
        self.generation_progress['total_missing'] = len(missing_words)
        self.generation_progress['start_time'] = datetime.now().isoformat()
        
        self.log_message(f"🚀 开始生成 {len(missing_words)} 个缺失文本资源")
        
        # 批量处理
        for i in range(0, len(missing_words), batch_size):
            batch = missing_words[i:i + batch_size]
            
            # 并发处理批次
            tasks = []
            for word_id, english_word, chinese_meaning in batch:
                tasks.append(self.generate_single_text_resource(word_id, english_word, chinese_meaning))
            
            # 执行批次任务
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # 保存进度
            await self.save_progress()
            
            # 进度汇报
            processed = min(i + batch_size, len(missing_words))
            progress_percent = (processed / len(missing_words)) * 100
            
            self.log_message(f"📊 进度: {processed}/{len(missing_words)} ({progress_percent:.1f}%) - "
                           f"成功:{self.generation_progress['generated']}, "
                           f"失败:{self.generation_progress['failed']}, "
                           f"跳过:{self.generation_progress['skipped']}")
            
            # 避免过快处理
            if i + batch_size < len(missing_words):
                await asyncio.sleep(1)
        
        # 最终结果
        results = {
            'total': len(missing_words),
            'generated': self.generation_progress['generated'],
            'failed': self.generation_progress['failed'],
            'skipped': self.generation_progress['skipped']
        }
        
        return results
    
    async def verify_generation_results(self) -> Dict[str, int]:
        """验证生成结果"""
        verification_results = {
            'total_checked': 0,
            'files_exist': 0,
            'files_missing': 0,
            'files_corrupted': 0
        }
        
        # 重新检查所有单词的文本文件
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, english_word FROM word ORDER BY id")
            all_words = cursor.fetchall()
            conn.close()
            
            for word_id, english_word in all_words:
                verification_results['total_checked'] += 1
                
                text_filename = self.get_standardized_filename(word_id)
                text_path = self.text_dir / text_filename
                
                if text_path.exists():
                    # 检查文件是否损坏
                    try:
                        async with aiofiles.open(text_path, 'r', encoding='utf-8') as f:
                            content = await f.read()
                            if len(content.strip()) > 0:
                                verification_results['files_exist'] += 1
                            else:
                                verification_results['files_corrupted'] += 1
                    except Exception:
                        verification_results['files_corrupted'] += 1
                else:
                    verification_results['files_missing'] += 1
                    
        except Exception as e:
            self.log_message(f"❌ 验证失败: {e}", "ERROR")
        
        return verification_results
    
    async def print_final_summary(self, generation_results: Dict[str, int], verification_results: Dict[str, int]):
        """打印最终摘要"""
        print(f"\n" + "="*60)
        print(f"📊 缺失文本资源生成完成报告")
        print(f"="*60)
        
        print(f"\n🎯 生成结果:")
        print(f"  总计需生成: {generation_results['total']}")
        print(f"  成功生成: {generation_results['generated']}")
        print(f"  生成失败: {generation_results['failed']}")
        print(f"  已存在跳过: {generation_results['skipped']}")
        
        success_rate = (generation_results['generated'] / generation_results['total'] * 100) if generation_results['total'] > 0 else 0
        print(f"  成功率: {success_rate:.1f}%")
        
        print(f"\n🔍 验证结果:")
        print(f"  总计检查: {verification_results['total_checked']}")
        print(f"  文件存在: {verification_results['files_exist']}")
        print(f"  文件缺失: {verification_results['files_missing']}")
        print(f"  文件损坏: {verification_results['files_corrupted']}")
        
        coverage_rate = (verification_results['files_exist'] / verification_results['total_checked'] * 100) if verification_results['total_checked'] > 0 else 0
        print(f"  覆盖率: {coverage_rate:.1f}%")
        
        print(f"\n📁 输出目录: {self.text_dir}")
        print(f"📋 日志文件: {self.log_file}")
        print(f"📈 进度文件: {self.progress_file}")

async def main():
    """主函数"""
    print("🔧 缺失文本资源生成脚本启动")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    project_root = Path(__file__).parent.parent
    generator = MissingTextGenerator(str(project_root))
    
    # 检查必要条件
    if not generator.db_path.exists():
        print(f"❌ 数据库文件不存在: {generator.db_path}")
        return 1
    
    try:
        # 执行文本资源生成
        generation_results = await generator.generate_all_missing_texts(batch_size=20)
        
        # 验证生成结果
        verification_results = await generator.verify_generation_results()
        
        # 打印摘要
        await generator.print_final_summary(generation_results, verification_results)
        
        print(f"\n✅ 缺失文本资源生成完成!")
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 返回状态码
        if generation_results['failed'] == 0:
            return 0  # 完全成功
        elif generation_results['generated'] > generation_results['failed']:
            return 0  # 大部分成功
        else:
            return 1  # 大部分失败
            
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))