<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闹闹疯狂学单词 - 仪表板</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .current-date {
            color: #666;
            font-size: 18px;
            margin-top: 10px;
        }
        .top-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .combined-stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }
        .combined-stats-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            pointer-events: none;
        }
        .unified-stats {
            text-align: center;
            position: relative;
            z-index: 1;
        }
        .unified-stats h3 {
            margin: 0 0 20px 0;
            font-size: 16px;
            opacity: 0.9;
            font-weight: 500;
        }
        .stats-formula {
            font-size: 42px;
            font-weight: 700;
            margin: 20px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .learned-count, .total-count {
            color: #fff;
        }
        .divider {
            color: #fff;
            opacity: 0.8;
            font-size: 36px;
        }
        .progress-text {
            font-size: 16px;
            opacity: 0.95;
            margin-top: 15px;
            font-weight: 500;
        }
        .proficiency-distribution-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }
        .proficiency-table {
            width: 100%;
        }
        .proficiency-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .proficiency-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 15px;
            padding: 18px;
            margin-bottom: 12px;
            background: white;
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            align-items: center;
        }
        .proficiency-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .level-info {
            font-weight: 600;
            font-size: 16px;
            color: #333;
        }
        .score-range {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }
        .word-count {
            font-weight: 700;
            color: #2196F3;
            font-size: 16px;
        }
        .percentage {
            font-weight: 700;
            color: #4CAF50;
            font-size: 16px;
        }
        .proficient-border {
            border-left: 4px solid #28a745 !important;
        }
        .intermediate-border {
            border-left: 4px solid #ffc107 !important;
        }
        .beginner-border {
            border-left: 4px solid #dc3545 !important;
        }
        .user-info {
            margin-top: 10px;
            color: #888;
        }
        .today-btn {
            background: #43e97b !important;
        }
        .proficiency-summary {
            margin-top: 15px;
            padding: 15px;
            background-color: #e7f3ff;
            border-radius: 5px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-card .sub-text {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
        .today-plan {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 25px;
            border-radius: 10px;
            color: white;
            margin-bottom: 30px;
        }
        .today-plan h2 {
            margin: 0 0 15px 0;
            text-align: center;
        }
        .plan-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            text-align: center;
        }
        .plan-item {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
        }
        .plan-item .label {
            font-size: 14px;
            opacity: 0.9;
        }
        .plan-item .value {
            font-size: 24px;
            font-weight: bold;
            margin-top: 5px;
        }
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        .btn-info {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
        }
        .date-selector {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(252, 182, 159, 0.3);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .date-selector h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .date-input-group {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .date-input {
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }
        .date-btn {
            padding: 10px 20px;
            background: #4facfe;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        .date-btn:hover {
            background: #3d8bfe;
        }
        .quick-date-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        .quick-btn {
            padding: 8px 15px;
            background: rgba(255,255,255,0.8);
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .quick-btn:hover {
            background: white;
            transform: translateY(-1px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .top-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .stats-formula {
                font-size: 28px;
                flex-direction: column;
                gap: 5px;
            }
            .proficiency-distribution-card {
                padding: 15px;
            }
            .proficiency-header, .proficiency-row {
                grid-template-columns: 2fr 1fr 1fr 1fr;
                font-size: 12px;
            }
            .date-selector {
                padding: 15px;
            }
            .combined-stats-card {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <a href="/logout" class="logout-btn">退出登录</a>

    <div class="dashboard-container">
        <div class="header">
            <h1>🎓 闹闹疯狂学单词 v0701</h1>
            <div class="current-date">📅 当前日期: {{ current_date }}</div>
            <div class="user-info">
                👤 欢迎回来, {{ user.username }}！
                📊 注册 {{ days_since_registration }} 天
            </div>
        </div>

        <!-- 第一行：日期选择器 + 学习统计 -->
        <div class="top-row" data-testid="top-section">
            <div class="date-selector">
                <h3>📅 选择学习日期</h3>
                <div class="date-input-group">
                    <input type="date" id="selectedDate" class="date-input" value="{{ current_date }}">
                    <button onclick="loadDatePlan()" class="date-btn">查看计划</button>
                    <button onclick="resetToToday()" class="date-btn today-btn">今天</button>
                </div>
                <div class="quick-date-buttons">
                    <button onclick="changeDate(-1)" class="quick-btn">昨天</button>
                    <button onclick="changeDate(1)" class="quick-btn">明天</button>
                    <button onclick="changeDate(7)" class="quick-btn">下周</button>
                    <button onclick="changeDate(-7)" class="quick-btn">上周</button>
                </div>
            </div>

            <div class="combined-stats-card" data-testid="combined-stats">
                <div class="unified-stats">
                    <h3>📖 已学习单词/📚 总单词库</h3>
                    <div class="stats-formula">
                        <span class="learned-count" data-testid="learned-words-count">{{ words_actually_learned }}</span>
                        <span class="divider">/</span>
                        <span class="total-count" data-testid="total-words-count">{{ total_words_in_db }}</span>
                    </div>
                    <div class="progress-text">学习进度: {{ "%.1f"|format((words_actually_learned / total_words_in_db * 100) if total_words_in_db > 0 else 0) }}%</div>
                </div>
            </div>
        </div>

        <!-- 第二行：熟练度分布 -->
        <div class="proficiency-distribution-card" data-testid="proficiency-distribution">
            <h3>📊 综合熟练度分布</h3>
            <div class="proficiency-table">
                <div class="proficiency-header">
                    <span>等级</span>
                    <span>评分范围</span>
                    <span>单词数</span>
                    <span>占比</span>
                </div>
                {% set simplified_levels = {
                    'proficient': proficiency_distribution.expert.count + proficiency_distribution.proficient.count,
                    'intermediate': proficiency_distribution.intermediate.count + proficiency_distribution.basic.count,
                    'beginner': proficiency_distribution.beginner.count + proficiency_distribution.unfamiliar.count
                } %}
                {% set total_words = simplified_levels.proficient + simplified_levels.intermediate + simplified_levels.beginner %}

                <div class="proficiency-row proficient-border">
                    <span class="level-info">🏆 熟练</span>
                    <span class="score-range">80-100分</span>
                    <span class="word-count">{{ simplified_levels.proficient }}个</span>
                    <span class="percentage">{{ "%.1f"|format((simplified_levels.proficient / total_words * 100) if total_words > 0 else 0) }}%</span>
                </div>

                <div class="proficiency-row intermediate-border">
                    <span class="level-info">📚 中等</span>
                    <span class="score-range">60-79分</span>
                    <span class="word-count">{{ simplified_levels.intermediate }}个</span>
                    <span class="percentage">{{ "%.1f"|format((simplified_levels.intermediate / total_words * 100) if total_words > 0 else 0) }}%</span>
                </div>

                <div class="proficiency-row beginner-border">
                    <span class="level-info">🌱 初学</span>
                    <span class="score-range">0-59分</span>
                    <span class="word-count">{{ simplified_levels.beginner }}个</span>
                    <span class="percentage">{{ "%.1f"|format((simplified_levels.beginner / total_words * 100) if total_words > 0 else 0) }}%</span>
                </div>
            </div>
        </div>

        <div class="today-plan" data-testid="today-plan-section">
            <h2 data-testid="plan-title">📋 今日学习计划</h2>
            <div class="plan-details" data-testid="plan-details">
                <div class="plan-item" data-testid="new-words-plan">
                    <div class="label">🆕 新单词</div>
                    <div class="value" data-testid="new-words-count">{{ new_words_count }}</div>
                </div>
                <div class="plan-item" data-testid="review-words-plan">
                    <div class="label">🔄 复习单词</div>
                    <div class="value" data-testid="review-words-count">{{ review_words_count }}</div>
                </div>
                <div class="plan-item" data-testid="total-words-plan">
                    <div class="label">📊 总计</div>
                    <div class="value" data-testid="total-today-count">{{ new_words_count + review_words_count }}</div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="/learning?date={{ current_date }}" class="btn btn-primary">
                🚀 开始学习
            </a>
            <a href="/learning_card?date={{ current_date }}" class="btn" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);">
                🎴 卡片学习模式
            </a>
            <a href="/vocabulary_book" class="btn btn-secondary">
                📚 生词本
            </a>
            <a href="/feature-learning/insights" class="btn btn-info">
                🧠 学习洞察
            </a>
            <a href="/daily_words?date={{ current_date }}" class="btn btn-secondary">
                📝 查看详细计划
            </a>
            <a href="/api/words/import" class="btn" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);">
                📥 导入新单词
            </a>
            {% if user.username == 'admin' %}
            <a href="/admin/database" class="btn" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);">
                🛠️ 数据库管理
            </a>
            {% endif %}
        </div>

        {% if new_words_count + review_words_count == 0 %}
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h3>🎉 今日学习任务已完成！</h3>
            <p>您今天没有新的学习任务，可以：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>复习之前学过的单词</li>
                <li>查看学习报告</li>
                <li>准备明天的学习计划</li>
            </ul>
        </div>
        {% endif %}
    </div>

    <script>
        // 获取今天的日期字符串
        const today = '{{ current_date }}';

        // 加载指定日期的学习计划
        function loadDatePlan() {
            const selectedDate = document.getElementById('selectedDate').value;
            if (selectedDate) {
                // 重新加载页面，传递选择的日期
                window.location.href = `/dashboard?date=${selectedDate}`;
                // 同时更新所有按钮的链接
                updateActionButtons(selectedDate);
            }
        }

        // 重置到今天
        function resetToToday() {
            document.getElementById('selectedDate').value = today;
            window.location.href = '/dashboard';
        }

        // 改变日期（相对于当前选择的日期）
        function changeDate(days) {
            const currentDate = new Date(document.getElementById('selectedDate').value);
            currentDate.setDate(currentDate.getDate() + days);

            const newDate = currentDate.toISOString().split('T')[0];
            document.getElementById('selectedDate').value = newDate;

            // 自动加载新日期的计划
            window.location.href = `/dashboard?date=${newDate}`;
        }

        // 更新操作按钮的链接
        function updateActionButtons(selectedDate) {
            const buttons = document.querySelectorAll('.action-buttons a');
            buttons.forEach(button => {
                const href = button.getAttribute('href');
                const baseUrl = href.split('?')[0];

                // 某些页面不需要日期参数
                const noDatePages = ['/vocabulary_book', '/api/words/import', '/admin/database', '/feature-learning/insights'];
                const needsDate = !noDatePages.some(page => baseUrl.includes(page));

                if (needsDate) {
                    button.setAttribute('href', `${baseUrl}?date=${selectedDate}`);
                } else {
                    button.setAttribute('href', baseUrl);
                }
            });
        }

        // 页面加载时，如果URL中有日期参数，更新日期选择器和按钮链接
        window.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const dateParam = urlParams.get('date');
            if (dateParam) {
                document.getElementById('selectedDate').value = dateParam;
                updateActionButtons(dateParam);
            } else {
                // 如果没有日期参数，使用当前显示的日期更新按钮
                const currentDisplayDate = document.getElementById('selectedDate').value;
                updateActionButtons(currentDisplayDate);
            }
        });

        // 监听日期输入框的变化
        document.getElementById('selectedDate').addEventListener('change', function() {
            loadDatePlan();
        });

        // 移除购物券功能 - 已移至learning界面
    </script>
</body>
</html>
