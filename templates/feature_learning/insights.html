<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习洞察 - Word Learning App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .insight-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .insight-card:hover {
            transform: translateY(-2px);
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        .progress-ring {
            width: 120px;
            height: 120px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .pattern-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            margin: 2px;
        }
        .error-pattern {
            background-color: #fff2f2;
            color: #dc3545;
            border: 1px solid #f5c6cb;
        }
        .time-pattern {
            background-color: #f0f8ff;
            color: #0066cc;
            border: 1px solid #b8daff;
        }
        .efficiency-pattern {
            background-color: #f0fff4;
            color: #28a745;
            border: 1px solid #c3e6cb;
        }
        .recommendation-item {
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid;
            background-color: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        .recommendation-high {
            border-left-color: #dc3545;
        }
        .recommendation-medium {
            border-left-color: #ffc107;
        }
        .recommendation-low {
            border-left-color: #28a745;
        }
        .loading-spinner {
            display: none;
        }
        .trend-arrow {
            font-size: 20px;
            margin-left: 10px;
        }
        .trend-improving {
            color: #28a745;
        }
        .trend-declining {
            color: #dc3545;
        }
        .trend-stable {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain me-2"></i>Word Learning App
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/simple-dashboard">
                    <i class="fas fa-home me-1"></i>主页
                </a>
                <a class="nav-link active" href="/feature-learning/insights">
                    <i class="fas fa-chart-line me-1"></i>学习洞察
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-center">
                    <i class="fas fa-brain text-primary me-2"></i>
                    个人学习洞察
                </h2>
                <p class="text-center text-muted">基于AI特征学习的个性化分析</p>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-spinner text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在分析您的学习数据...</p>
        </div>

        <!-- 数据不足提示 -->
        <div id="insufficient-data" class="alert alert-info text-center" style="display: none;">
            <i class="fas fa-info-circle me-2"></i>
            <strong>数据收集中</strong><br>
            特征学习功能需要一定的学习数据积累。请继续练习单词，我们将为您提供更精准的个性化洞察！
            <div class="mt-3">
                <a href="/learning" class="btn btn-primary">
                    <i class="fas fa-play me-1"></i>开始学习
                </a>
            </div>
        </div>

        <!-- 主要统计数据 -->
        <div id="main-stats" class="row mb-4" style="display: none;">
            <div class="col-md-3 mb-3">
                <div class="card insight-card">
                    <div class="card-body text-center">
                        <div class="stat-icon bg-primary mx-auto mb-3">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h5 class="card-title">总尝试次数</h5>
                        <h3 class="text-primary" id="total-attempts">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card insight-card">
                    <div class="card-body text-center">
                        <div class="stat-icon bg-success mx-auto mb-3">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <h5 class="card-title">准确率</h5>
                        <h3 class="text-success" id="accuracy-rate">0%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card insight-card">
                    <div class="card-body text-center">
                        <div class="stat-icon bg-info mx-auto mb-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h5 class="card-title">平均用时</h5>
                        <h3 class="text-info" id="avg-duration">0s</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card insight-card">
                    <div class="card-body text-center">
                        <div class="stat-icon bg-warning mx-auto mb-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5 class="card-title">
                            学习趋势
                            <span id="trend-arrow" class="trend-arrow"></span>
                        </h5>
                        <h3 id="recent-trend">分析中</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细分析 -->
        <div id="detailed-analysis" class="row" style="display: none;">
            <!-- 学习模式分析 -->
            <div class="col-lg-8 mb-4">
                <div class="card insight-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-microscope me-2"></i>学习模式分析
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 错误模式 -->
                        <div class="mb-4">
                            <h6 class="text-danger">
                                <i class="fas fa-exclamation-triangle me-1"></i>常见错误模式
                            </h6>
                            <div id="error-patterns">
                                <!-- 错误模式将由JavaScript动态填充 -->
                            </div>
                        </div>

                        <!-- 时间模式 -->
                        <div class="mb-4">
                            <h6 class="text-primary">
                                <i class="fas fa-clock me-1"></i>时间使用模式
                            </h6>
                            <div id="time-patterns">
                                <!-- 时间模式将由JavaScript动态填充 -->
                            </div>
                        </div>

                        <!-- 效率模式 -->
                        <div class="mb-4">
                            <h6 class="text-success">
                                <i class="fas fa-tachometer-alt me-1"></i>学习效率分析
                            </h6>
                            <div id="efficiency-patterns">
                                <!-- 效率模式将由JavaScript动态填充 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个性化推荐 -->
            <div class="col-lg-4 mb-4">
                <div class="card insight-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>个性化建议
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="recommendations">
                            <!-- 推荐内容将由JavaScript动态填充 -->
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="updatePatterns()">
                                <i class="fas fa-sync-alt me-1"></i>更新分析
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 相似度测试工具 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card insight-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>智能相似度测试
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">测试我们的AI如何分析您的输入与正确答案的相似度</p>
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">您的输入</label>
                                <input type="text" class="form-control" id="test-input" placeholder="输入单词...">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">正确答案</label>
                                <input type="text" class="form-control" id="test-answer" placeholder="正确单词...">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button class="btn btn-primary" onclick="testSimilarity()">
                                        <i class="fas fa-play me-1"></i>测试相似度
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div id="similarity-result" class="mt-3" style="display: none;">
                            <!-- 相似度测试结果 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时获取学习洞察
        document.addEventListener('DOMContentLoaded', function() {
            loadLearningInsights();
        });

        // 加载学习洞察数据
        async function loadLearningInsights() {
            try {
                showLoading(true);
                
                const response = await fetch('/feature-learning/api/insights');
                const data = await response.json();
                
                if (data.success) {
                    displayInsights(data.data);
                } else {
                    console.error('获取洞察失败:', data.message);
                    showError('获取数据失败: ' + data.message);
                }
            } catch (error) {
                console.error('请求失败:', error);
                showError('网络请求失败，请稍后重试');
            } finally {
                showLoading(false);
            }
        }

        // 显示洞察数据
        function displayInsights(insights) {
            if (!insights.insights_available) {
                document.getElementById('insufficient-data').style.display = 'block';
                return;
            }

            // 显示基础统计
            const stats = insights.basic_stats;
            document.getElementById('total-attempts').textContent = stats.total_attempts;
            document.getElementById('accuracy-rate').textContent = (stats.accuracy_rate * 100).toFixed(1) + '%';
            document.getElementById('avg-duration').textContent = stats.average_duration.toFixed(1) + 's';
            
            // 显示趋势
            displayTrend(stats.recent_trend);
            
            // 显示详细分析
            displayPatterns(insights.patterns);
            displayRecommendations(insights.recommendations);
            
            // 显示主要区域
            document.getElementById('main-stats').style.display = 'flex';
            document.getElementById('detailed-analysis').style.display = 'flex';
        }

        // 显示趋势
        function displayTrend(trend) {
            const trendElement = document.getElementById('recent-trend');
            const arrowElement = document.getElementById('trend-arrow');
            
            let trendText, arrowIcon, arrowClass;
            
            switch(trend) {
                case 'improving':
                    trendText = '上升中';
                    arrowIcon = '↗';
                    arrowClass = 'trend-improving';
                    break;
                case 'declining':
                    trendText = '需改进';
                    arrowIcon = '↘';
                    arrowClass = 'trend-declining';
                    break;
                case 'stable':
                    trendText = '保持稳定';
                    arrowIcon = '→';
                    arrowClass = 'trend-stable';
                    break;
                default:
                    trendText = '分析中';
                    arrowIcon = '';
                    arrowClass = '';
            }
            
            trendElement.textContent = trendText;
            arrowElement.textContent = arrowIcon;
            arrowElement.className = 'trend-arrow ' + arrowClass;
        }

        // 显示学习模式
        function displayPatterns(patterns) {
            if (patterns.status !== 'success') {
                return;
            }

            const results = patterns.results;

            // 显示错误模式
            if (results.error_patterns) {
                displayErrorPatterns(results.error_patterns);
            }

            // 显示时间模式
            if (results.time_patterns) {
                displayTimePatterns(results.time_patterns);
            }

            // 显示效率模式
            if (results.efficiency_patterns) {
                displayEfficiencyPatterns(results.efficiency_patterns);
            }
        }

        // 显示错误模式
        function displayErrorPatterns(errorPatterns) {
            const container = document.getElementById('error-patterns');
            let html = '';

            const commonErrors = errorPatterns.most_common_errors;
            for (const [errorType, count] of Object.entries(commonErrors)) {
                html += `<span class="pattern-badge error-pattern">${getErrorTypeName(errorType)}: ${count}次</span>`;
            }

            html += `<div class="mt-2 small text-muted">
                平均相似度: ${(errorPatterns.average_similarity_score * 100).toFixed(1)}% | 
                错误频率: ${(errorPatterns.error_frequency * 100).toFixed(1)}%
            </div>`;

            container.innerHTML = html;
        }

        // 显示时间模式
        function displayTimePatterns(timePatterns) {
            const container = document.getElementById('time-patterns');
            let html = '';

            html += `<div class="row">
                <div class="col-6">
                    <strong>平均用时:</strong> ${timePatterns.average_duration.toFixed(1)}s
                </div>
                <div class="col-6">
                    <strong>平均速度:</strong> ${timePatterns.average_speed.toFixed(1)} 字符/秒
                </div>
            </div>`;

            if (timePatterns.best_learning_hours && timePatterns.best_learning_hours.length > 0) {
                html += `<div class="mt-2">
                    <strong>最佳学习时间:</strong> 
                    ${timePatterns.best_learning_hours.map(h => h + ':00').join(', ')}
                </div>`;
            }

            container.innerHTML = html;
        }

        // 显示效率模式
        function displayEfficiencyPatterns(efficiencyPatterns) {
            const container = document.getElementById('efficiency-patterns');
            let html = '';

            if (efficiencyPatterns.fatigue_pattern && efficiencyPatterns.fatigue_pattern.has_fatigue) {
                html += `<div class="alert alert-warning alert-sm">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    检测到疲劳模式，建议每次学习${efficiencyPatterns.fatigue_pattern.optimal_session_length}个单词
                </div>`;
            }

            html += `<div class="small text-muted">
                学习效率分析基于会话位置、时间段等因素
            </div>`;

            container.innerHTML = html;
        }

        // 显示个性化推荐
        function displayRecommendations(recommendations) {
            const container = document.getElementById('recommendations');
            let html = '';

            // 重点改进领域
            if (recommendations.focus_areas && recommendations.focus_areas.length > 0) {
                html += `<div class="recommendation-item recommendation-high">
                    <strong><i class="fas fa-target me-1"></i>重点改进:</strong><br>
                    ${recommendations.focus_areas.join('<br>')}
                </div>`;
            }

            // 最佳学习时间
            if (recommendations.best_time_slots && recommendations.best_time_slots.length > 0) {
                html += `<div class="recommendation-item recommendation-medium">
                    <strong><i class="fas fa-clock me-1"></i>推荐时间:</strong><br>
                    ${recommendations.best_time_slots.map(h => h + ':00').join(', ')}
                </div>`;
            }

            // 会话长度建议
            html += `<div class="recommendation-item recommendation-low">
                <strong><i class="fas fa-list me-1"></i>建议会话:</strong><br>
                每次学习 ${recommendations.optimal_session_length} 个单词
            </div>`;

            // 具体建议
            if (recommendations.specific_suggestions && recommendations.specific_suggestions.length > 0) {
                html += `<div class="mt-3">
                    <h6>具体建议:</h6>
                    <ul class="small">
                        ${recommendations.specific_suggestions.map(s => `<li>${s}</li>`).join('')}
                    </ul>
                </div>`;
            }

            container.innerHTML = html;
        }

        // 更新学习模式
        async function updatePatterns() {
            try {
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>更新中...';
                button.disabled = true;

                const response = await fetch('/feature-learning/api/update-patterns', {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    // 重新加载洞察数据
                    await loadLearningInsights();
                    showSuccess('学习模式已更新');
                } else {
                    showError('更新失败: ' + data.message);
                }
            } catch (error) {
                console.error('更新失败:', error);
                showError('网络请求失败');
            } finally {
                const button = event.target;
                button.innerHTML = '<i class="fas fa-sync-alt me-1"></i>更新分析';
                button.disabled = false;
            }
        }

        // 测试相似度
        async function testSimilarity() {
            const userInput = document.getElementById('test-input').value.trim();
            const correctAnswer = document.getElementById('test-answer').value.trim();

            if (!userInput || !correctAnswer) {
                showError('请输入要测试的单词');
                return;
            }

            try {
                const response = await fetch('/feature-learning/api/enhanced-similarity', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_input: userInput,
                        correct_answer: correctAnswer
                    })
                });

                const data = await response.json();

                if (data.success) {
                    displaySimilarityResult(data.data);
                } else {
                    showError('相似度测试失败: ' + data.message);
                }
            } catch (error) {
                console.error('相似度测试失败:', error);
                showError('网络请求失败');
            }
        }

        // 显示相似度测试结果
        function displaySimilarityResult(result) {
            const container = document.getElementById('similarity-result');
            
            const similarity = (result.similarity_score * 100).toFixed(1);
            const statusClass = result.exact_match ? 'success' : 
                              result.is_close_match ? 'warning' : 'danger';
            
            let html = `
                <div class="alert alert-${statusClass}">
                    <h6><i class="fas fa-search me-1"></i>相似度分析结果</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>相似度分数:</strong> ${similarity}%<br>
                            <strong>编辑距离:</strong> ${result.edit_distance}<br>
                            <strong>完全匹配:</strong> ${result.exact_match ? '是' : '否'}
                        </div>
                        <div class="col-md-6">
                            <strong>接近匹配:</strong> ${result.is_close_match ? '是' : '否'}<br>
                            <strong>轻微错误:</strong> ${result.has_minor_errors ? '是' : '否'}<br>
                            <strong>AI反馈:</strong> ${result.suggested_feedback}
                        </div>
                    </div>
            `;

            if (result.character_analysis) {
                const charAnalysis = result.character_analysis;
                if (charAnalysis.missing_chars.length > 0 || charAnalysis.extra_chars.length > 0) {
                    html += `<div class="mt-2 small">`;
                    if (charAnalysis.missing_chars.length > 0) {
                        html += `<span class="text-danger">缺失字符: ${charAnalysis.missing_chars.join(', ')}</span> `;
                    }
                    if (charAnalysis.extra_chars.length > 0) {
                        html += `<span class="text-warning">多余字符: ${charAnalysis.extra_chars.join(', ')}</span>`;
                    }
                    html += `</div>`;
                }
            }

            html += '</div>';
            
            container.innerHTML = html;
            container.style.display = 'block';
        }

        // 工具函数
        function getErrorTypeName(errorType) {
            const names = {
                'no_error': '无错误',
                'too_short': '长度不足',
                'too_long': '长度过长',
                'substitution': '字符替换',
                'transposition': '字符换位',
                'double_letter': '重复字母',
                'other': '其他错误'
            };
            return names[errorType] || errorType;
        }

        function showLoading(show) {
            const spinner = document.querySelector('.loading-spinner');
            spinner.style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            // 简单的错误提示，可以用更好的UI组件替换
            alert('错误: ' + message);
        }

        function showSuccess(message) {
            // 简单的成功提示，可以用更好的UI组件替换
            alert('成功: ' + message);
        }
    </script>
</body>
</html>