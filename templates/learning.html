<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>闹闹疯狂学单词 - 学习</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/responsive.css">
    <link rel="stylesheet" href="/static/css/mobile.css">
    <!-- 🚀 前后端分离版本：移除内联样式，使用模块化CSS -->
    <link rel="stylesheet" href="/static/css/learning-page.css">
    <!-- Pattern学习辅助功能样式 -->
    <link rel="stylesheet" href="/static/css/pattern_helper.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" />
</head>
<body>
    <!-- 🔧 购物券功能指示器 -->
    <div class="voucher-indicator" id="voucher-indicator" style="position: fixed; top: 20px; right: 20px; background: linear-gradient(45deg, #007bff, #0056b3); color: white; padding: 10px 15px; border-radius: 25px; font-size: 14px; box-shadow: 0 4px 15px rgba(0,123,255,0.3); opacity: 0; transition: all 0.3s ease; z-index: 1000;">
        🎫 <span id="voucher-count">0</span>张购物券
    </div>

    <div class="dashboard-container">
        <div class="left-column">
            <a href="/dashboard" class="back-to-dashboard">← 返回</a>
            <div class="header-container">
                <h1>闹闹疯狂学单词 v0622</h1>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <!-- 购物券显示区域 -->
                    <div class="voucher-display" style="display: flex; align-items: center; gap: 8px; position: relative;">
                        <span style="font-size: 24px;">🎫</span>
                        <span id="voucher-count" style="color: #FFD700; font-weight: bold; font-size: 18px;">0</span>
                        
                        <!-- 悬浮提示卡片 -->
                        <div class="voucher-tooltip" style="
                            position: absolute;
                            top: 100%;
                            left: 50%;
                            transform: translateX(-50%);
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 15px;
                            border-radius: 10px;
                            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                            min-width: 280px;
                            z-index: 1000;
                            opacity: 0;
                            visibility: hidden;
                            transition: all 0.3s ease;
                            margin-top: 10px;
                        ">
                            <div style="font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid rgba(255,255,255,0.3); padding-bottom: 8px;">
                                🎫 购物券激励条件
                            </div>
                            <div style="font-size: 13px; line-height: 1.4;">
                                <div style="margin-bottom: 6px;">⚡ <strong>速度激励:</strong> 3秒内响应 + 80%正确率</div>
                                <div style="margin-bottom: 6px;">🎯 <strong>正确率激励:</strong> 95%以上正确率</div>
                                <div style="margin-bottom: 6px;">🏆 <strong>完美会话:</strong> 100%正确率</div>
                                <div style="margin-bottom: 6px;">✅ <strong>每日完成:</strong> 完成所有学习任务</div>
                                <div style="margin-bottom: 8px;">🔥 <strong>连续学习:</strong> 3/7/14/30天里程碑</div>
                                <div style="font-size: 11px; opacity: 0.8; border-top: 1px solid rgba(255,255,255,0.3); padding-top: 8px;">
                                    每日最多2张 • 每周最多15张
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="score">🪙 0</div>
                </div>
            </div>
            <div class="checkbox-group">
                <label><input type="checkbox" id="new-words-checkbox" checked> 新</label>
                <label><input type="checkbox" id="review-words-checkbox" checked> 复</label>
            </div>
            <div id="daily-plan"></div>
            <button id="low-correct-rate-btn">
                导出生词
                <span class="sub-text">(历史正确率低于90%)</span>
            </button>
        </div>
        
        <div class="right-column">
            <div id="word-container">
                <img id="word-image" style="display:none;">
                <div id="image-status" class="image-status">🎨 生成中...</div>
                <div id="word-and-pronounce">
                    <span id="word"></span>
                    <span id="pronounce-button" onclick="playCurrentWordAudio()" title="播放发音" style="cursor: pointer;">🔊</span>
                </div>
            </div>
            <div id="hint">提示区域</div>

            <!-- 传统模式（星级>=3） -->
            <div id="traditional-mode">
                <input type="text" id="user-input" class="user-input-field" placeholder="请输入英文单词..." data-correct-answer="">
                <div id="smart-hints" class="mt-2"></div>
                <button onclick="checkSpelling()">检查</button>
            </div>

            <!-- 拼写模式（星级<3） -->
            <div id="spelling-mode" style="display: none;">
                <div id="spelling-container">
                    <div id="spelling-input-container">
                        <input type="text" id="spelling-input" placeholder="请拼写单词..."
                               autocomplete="off" spellcheck="false">
                        <button id="play-audio-spelling" onclick="playCurrentWordAudio()" title="播放发音">🔊</button>
                    </div>
                    <div id="spelling-feedback">
                        <div id="letter-feedback"></div>
                        <div id="progress-bar">
                            <div id="progress-fill"></div>
                        </div>
                    </div>
                    <div id="spelling-hints">
                        <div id="syllable-hint"></div>
                        <div id="length-hint"></div>
                    </div>
                </div>
            </div>

            <!-- Recognition模式（选择题模式） -->
            <div id="recognition-mode" style="display: none;">
                <div id="recognition-container">
                    <!-- 问题显示区域 -->
                    <div id="recognition-question">
                        <div id="question-text">请选择正确答案：</div>
                    </div>
                    
                    <!-- 选择题选项区域 -->
                    <div id="recognition-options">
                        <div class="option-button" data-option="A" onclick="selectRecognitionOption('A')">
                            <span class="option-label">A</span>
                            <span class="option-text" id="option-A-text"></span>
                        </div>
                        <div class="option-button" data-option="B" onclick="selectRecognitionOption('B')">
                            <span class="option-label">B</span>
                            <span class="option-text" id="option-B-text"></span>
                        </div>
                        <div class="option-button" data-option="C" onclick="selectRecognitionOption('C')">
                            <span class="option-label">C</span>
                            <span class="option-text" id="option-C-text"></span>
                        </div>
                        <div class="option-button" data-option="D" onclick="selectRecognitionOption('D')">
                            <span class="option-label">D</span>
                            <span class="option-text" id="option-D-text"></span>
                        </div>
                    </div>
                    
                    <!-- 确认按钮 -->
                    <div id="recognition-submit">
                        <button id="recognition-confirm-btn" onclick="confirmRecognitionAnswer()" disabled>确认答案</button>
                    </div>
                    
                    <!-- 反馈区域 -->
                    <div id="recognition-feedback" style="display: none;">
                        <div id="recognition-result"></div>
                        <div id="recognition-explanation"></div>
                        <button onclick="nextRecognitionWord()">下一题</button>
                    </div>
                </div>
            </div>

            <div class="action-buttons" data-testid="action-buttons">
                <button id="hint-button" data-testid="hint-button">提示</button>
                <button id="memory-help-button" data-testid="ai-help-button">AI帮帮记</button>
                <button id="audio-button" data-testid="audio-button" onclick="playCurrentWordAudio()">🔊 发音</button>
                <button onclick="addToVocabulary()">加入生词本</button>
            </div>
            <div id="feedback"></div>
            <div id="memory-help"></div>
            <div id="report-message"></div>
        </div>
    </div>
    
    <div class="low-correct-rate-container" id="low-correct-rate-container" style="display: none;">
        <h2>低正确率单词</h2>
        <table>
            <thead>
                <tr>
                    <th>单词</th>
                    <th>答案</th>
                    <th>正确次数</th>
                    <th>总尝试次数</th>
                    <th>正确率</th>
                </tr>
            </thead>
            <tbody id="low-correct-rate-words-table">
                <!-- Rows will be populated by JavaScript -->
            </tbody>
        </table>
    </div>

    <!-- 🚀 前后端分离版本：移除所有内联JavaScript，使用外部模块 -->
    
    <!-- 必需的外部依赖 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    
    <!-- 🔧 用户数据注入 -->
    <script>
        // 🔧 从服务器获取用户数据
        {% if user %}
        window.userInfo = {
            id: {{ user.id }},
            username: "{{ user.username }}",
            points: {{ user.points }},
            vouchers: {{ user.vouchers }}
        };
        console.log('🔍 服务器用户数据:', window.userInfo);
        {% else %}
        window.userInfo = {id: 0, username: '', points: 0, vouchers: 0};
        {% endif %}
        
        // 获取学习模式
        const learningMode = '{{ learning_mode if learning_mode else "daily_plan" }}';
        console.log('🎯 当前学习模式:', learningMode);
    </script>
    
    <!-- 🚀 前后端分离版本：主要业务逻辑通过API调用 -->
    <script>
        // 🔄 内联JavaScript确保立即执行，绕过缓存问题
        console.log('🔄 内联JavaScript开始执行 - 版本: 2025-07-11-003');

        // 重写submitAnswer函数中的星级分布更新逻辑
        function updateStarDistributionInline(newStarLevel, currentWordIndex) {
            console.log('🔄 内联更新星级分布:', { newStarLevel, currentWordIndex });

            if (typeof filteredWords !== 'undefined' && filteredWords[currentWordIndex]) {
                const oldStarLevel = filteredWords[currentWordIndex].star_level;
                filteredWords[currentWordIndex].star_level = newStarLevel;

                console.log('🔄 本地数据已更新:', {
                    word: filteredWords[currentWordIndex].word,
                    oldStarLevel: oldStarLevel,
                    newStarLevel: newStarLevel
                });

                // 立即更新UI
                if (typeof displayStudyPlan === 'function') {
                    displayStudyPlan();
                }

                // 强制更新星级数字
                updateStarCountsInUI();
            }
        }

        // 强制更新星级数字的函数
        function updateStarCountsInUI() {
            console.log('🔄 强制更新星级数字...');

            if (typeof filteredWords === 'undefined') {
                console.log('❌ filteredWords未定义');
                return;
            }

            // 计算星级分布
            const starCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
            filteredWords.forEach(word => {
                const starLevel = word.star_level || word.proficiency || 0;
                if (starLevel >= 1 && starLevel <= 5) {
                    starCounts[starLevel]++;
                }
            });

            console.log('📊 计算出的星级分布:', starCounts);

            // 更新每个星级的显示
            for (let star = 1; star <= 5; star++) {
                const checkbox = document.querySelector(`input[value="${star}"]`);
                if (checkbox && checkbox.parentElement) {
                    const label = checkbox.parentElement;
                    const isChecked = checkbox.checked;
                    label.innerHTML = `<input type="checkbox" class="star-checkbox" value="${star}" ${isChecked ? 'checked' : ''}> ${star}⭐: ${starCounts[star]}`;

                    // 重新绑定事件
                    const newCheckbox = label.querySelector('input');
                    if (newCheckbox && typeof updateStarFilters === 'function') {
                        newCheckbox.addEventListener('change', updateStarFilters);
                    }
                }
            }

            console.log('✅ 星级数字更新完成');
        }

        console.log('✅ 内联JavaScript加载完成');
    </script>
    <script src="/static/js/learning_logic.js?v={{ range(1000000, 9999999) | random }}"></script>
    
    <!-- 🔧 保留的前端UI逻辑模块 -->
    <script src="/static/js/encouragement.js"></script>
    <script src="/static/js/voucher_rewards.js"></script>
    <script src="/static/js/responsive.js"></script>
    <script src="/static/js/image_loader.js"></script>
    <!-- Pattern学习辅助功能 -->
    <script src="/static/js/pattern_helper.js"></script>
    <!-- 特征学习功能脚本 -->
    <script src="/static/js/feature-learning.js"></script>
    
    <!-- 🚀 简化的前端初始化脚本 -->
    <script>
        // 🔧 简化的用户交互初始化
        $(document).ready(function() {
            console.log('🚀 前后端分离版 learning.html 初始化完成');
            
            // 初始化用户交互状态
            window.userHasInteracted = false;
            window.pendingAutoPlayAudio = null;
            
            // 添加用户交互检测
            function enableAudioAfterInteraction() {
                if (!window.userHasInteracted) {
                    console.log('🎵 用户已交互，启用音频播放');
                    window.userHasInteracted = true;
                    
                    // 如果有待播放的音频，现在播放
                    if (window.pendingAutoPlayAudio) {
                        console.log('🎵 播放待播放音频:', window.pendingAutoPlayAudio);
                        playAudioSmart(window.pendingAutoPlayAudio);
                        window.pendingAutoPlayAudio = null;
                    }
                    
                    // 移除事件监听器
                    document.removeEventListener('click', enableAudioAfterInteraction);
                    document.removeEventListener('keydown', enableAudioAfterInteraction);
                    document.removeEventListener('touchstart', enableAudioAfterInteraction);
                }
            }
            
            // 监听用户交互事件
            document.addEventListener('click', enableAudioAfterInteraction);
            document.addEventListener('keydown', enableAudioAfterInteraction);
            document.addEventListener('touchstart', enableAudioAfterInteraction);
        });
        
        // 🔧 添加回车键事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 为传统模式输入框添加回车键监听
            const userInput = document.getElementById('user-input');
            if (userInput) {
                userInput.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter') {
                        event.preventDefault();
                        checkSpelling(); // 调用learning_logic.js中的实际函数
                    }
                });
            }

            // 为拼写模式输入框添加回车键监听
            const spellingInput = document.getElementById('spelling-input');
            if (spellingInput) {
                spellingInput.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter') {
                        event.preventDefault();
                        checkSpelling(); // 调用learning_logic.js中的实际函数
                    }
                });
            }
        });
        
        console.log('✅ 前后端分离版 learning.html 加载完成 - HTML文件从3431行缩减到约200行');
    </script>
</body>
</html>