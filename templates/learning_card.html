<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闹闹疯狂学单词 - 卡片学习</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/responsive.css">
    <link rel="stylesheet" href="/static/css/mobile.css">
    <style>
        /* 全局样式 - 活泼的儿童风格 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, "PingFang SC", "微软雅黑", sans-serif;
            background: url('/static/images/background.jpg') center/cover no-repeat fixed;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
        }

        /* 背景叠加层 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
            z-index: -1;
        }
        
        /* 主容器 */
        .container {
            width: 100%;
            max-width: 600px;
            padding: 20px;
            z-index: 10;
        }

        /* 顶部导航栏 */
        .top-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px 20px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .nav-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .nav-controls {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* 进度条 - 游戏化设计 */
        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            height: 30px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
            height: 100%;
            width: 0%;
            border-radius: 20px;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        /* 🌟 优化翻转卡片容器 - 大幅增加高度适应大型AI帮帮记 */
        .flip-card {
            width: 440px;  /* 进一步增加宽度 */
            height: 680px;  /* 大幅增加高度以适应大型AI帮帮记 */
            margin: 0 auto;
            perspective: 1000px;
        }
        
        .flip-card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.6s;
            transform-style: preserve-3d;
            cursor: pointer;
        }
        
        .flip-card-inner.flipped {
            transform: rotateY(180deg);
        }
        
        /* 🌟 优化卡片正反面 - 调整内边距和布局 */
        .flip-card-front, .flip-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            border-radius: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 35px 30px;  /* 优化内边距 */
            box-sizing: border-box;
        }
        
        .flip-card-front {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .flip-card-back {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            transform: rotateY(180deg);
        }
        
        /* 中文面样式 */
        .chinese-word {
            font-size: 80px;
            font-weight: bold;
            color: white;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }
        
        .flip-hint {
            font-size: 20px;
            color: rgba(255,255,255,0.9);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 英文面样式 */
        .english-word {
            font-size: 60px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            letter-spacing: 3px;
        }

        /* 单词图片 */
        .word-image {
            width: 120px;
            height: 120px;
            border-radius: 15px;
            object-fit: cover;
            margin-bottom: 20px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            display: block;
            max-width: 100%;
        }
        
        /* 发音按钮 */
        .audio-button {
            background: white;
            border: none;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            font-size: 40px;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            margin: 20px 0;
        }
        
        .audio-button:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }
        
        .audio-button:active {
            transform: scale(0.95);
        }
        
        /* 🌟 优化学习提示区域 - 匹配AI帮帮记的大尺寸 */
        .learning-tips {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 25px;
            margin-top: 25px;
            width: 100%;
            max-height: 450px;  /* 大幅增加高度以匹配内容 */
            overflow-y: auto;
            min-height: 320px;  /* 增加最小高度 */
            box-sizing: border-box;
        }
        
        .tip-section {
            margin: 15px 0;
            text-align: left;
        }
        
        .tip-title {
            font-size: 16px;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tip-content {
            font-size: 14px;
            color: #333;
            line-height: 1.6;
        }

        /* 🌟 优化AI记忆助手内容样式 - 扩大显示区域 */
        .memory-help-content {
            font-size: 15px;  /* 稍微增大字体 */
            line-height: 1.7;  /* 增加行间距 */
            color: #333;
            max-height: 400px;  /* 大幅增加高度 */
            min-height: 300px;  /* 设置最小高度 */
            overflow-y: auto;
            scroll-behavior: smooth;
            padding: 8px;  /* 添加内边距 */
            box-sizing: border-box;
        }

        .memory-help-content h1,
        .memory-help-content h2,
        .memory-help-content h3 {
            font-size: 16px;
            color: #667eea;
            margin: 10px 0 5px 0;
            font-weight: bold;
        }

        .memory-help-content h1 {
            font-size: 18px;
        }

        .memory-help-content h2 {
            font-size: 17px;
        }

        .memory-help-content ul {
            margin: 8px 0;
            padding-left: 20px;
        }

        .memory-help-content li {
            margin: 4px 0;
        }

        .memory-help-content strong {
            color: #5a67d8;
            font-weight: bold;
        }

        .memory-help-content em {
            color: #805ad5;
            font-style: italic;
        }

        .memory-help-content code {
            background: rgba(102, 126, 234, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
        }

        .memory-help-content a {
            color: #667eea;
            text-decoration: none;
        }

        .memory-help-content a:hover {
            text-decoration: underline;
        }

        .memory-help-content p {
            margin: 6px 0;
        }

        /* 滚动条样式 */
        .memory-help-content::-webkit-scrollbar {
            width: 4px;
        }

        .memory-help-content::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 2px;
        }

        .memory-help-content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 2px;
        }

        .memory-help-content::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.5);
        }
        
        /* 操作按钮 */
        .action-buttons {
            margin-top: 40px;
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }
        
        /* 动画效果 */
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }
        
        .bounce {
            animation: bounce 1s ease infinite;
        }
        
        .shake {
            animation: shake 0.5s ease;
        }
        
        /* 星星动画背景 */
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .star {
            position: absolute;
            background: white;
            clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
            animation: twinkle 3s ease-in-out infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0; transform: scale(0.5); }
            50% { opacity: 1; transform: scale(1); }
        }

        /* 完成庆祝动画 */
        .celebration {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 9999;
            display: none;
        }

        .celebration.active {
            display: block;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #f39c12;
            animation: confetti-fall 3s ease-out forwards;
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
        
        /* 🌟 响应式设计 - 适应大型AI帮帮记 */
        @media (max-width: 600px) {
            /* 移动端背景优化 */
            body {
                background-attachment: scroll;
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
            }
            
            .flip-card {
                width: 95vw;
                max-width: 420px;
                height: 580px;  /* 增加移动端高度 */
            }
            
            /* 移动端图片优化 */
            .word-image {
                width: 100px;
                height: 100px;
                margin-bottom: 15px;
                flex-shrink: 0;
            }
            
            .learning-tips {
                max-height: 380px;
                min-height: 280px;
                padding: 20px;
            }
            
            .memory-help-content {
                max-height: 320px;
                min-height: 240px;
                font-size: 14px;
            }
        }
        
        @media (max-width: 500px) {
            /* 小屏幕背景进一步优化 */
            body {
                background-attachment: scroll;
                background-image: url('/static/images/background.jpg');
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
            }
            
            .flip-card {
                width: 90vw;
                height: 520px;  /* 小屏幕也要保持合理高度 */
            }
            
            /* 小屏幕图片优化 */
            .word-image {
                width: 80px;
                height: 80px;
                margin-bottom: 10px;
                border-radius: 12px;
            }
            
            .learning-tips {
                max-height: 320px;
                min-height: 220px;
                padding: 15px;
            }
            
            .memory-help-content {
                max-height: 260px;
                min-height: 180px;
            }
            
            .chinese-word {
                font-size: 60px;
            }
            
            .english-word {
                font-size: 40px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 80%;
                max-width: 280px;
            }
        }
        
        /* 记忆方法弹窗样式 */
        .memory-method-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 30px;
            cursor: pointer;
            color: #999;
            transition: color 0.3s;
        }

        .close-btn:hover {
            color: #333;
        }

        .memory-method-textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        .memory-method-textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .btn-secondary {
            background: #e0e0e0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #d0d0d0;
        }

        /* 用户记忆方法显示样式 */
        .user-memory-method {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .user-memory-method h4 {
            color: #ff9800;
            margin: 0 0 10px 0;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-memory-method p {
            margin: 0;
            line-height: 1.6;
            color: #333;
        }
    </style>
</head>
<body>
    <!-- 星星背景 -->
    <div class="stars" id="stars"></div>
    
    <!-- 记忆方法弹窗 -->
    <div class="memory-method-modal" id="memoryMethodModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📝 我的记忆方法</h3>
                <button class="close-btn" onclick="closeMemoryMethodDialog()">×</button>
            </div>
            <div class="modal-body">
                <textarea id="memoryMethodInput" class="memory-method-textarea" 
                          placeholder="输入你的记忆方法，比如联想、谐音、图像记忆等..." 
                          rows="6"></textarea>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="saveMemoryMethod()">保存</button>
                <button class="btn btn-secondary" onclick="closeMemoryMethodDialog()">取消</button>
            </div>
        </div>
    </div>
    
    <!-- 完成庆祝动画 -->
    <div class="celebration" id="celebration"></div>
    
    <div class="container">
        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="nav-title">🎴 卡片学习模式</div>
            <div class="nav-controls">
                <button class="nav-btn" onclick="window.location.href='/learning'">传统模式</button>
                <button class="nav-btn" onclick="window.location.href='/dashboard'">返回首页</button>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill">
                <span id="progressText">准备中...</span>
            </div>
        </div>
        
        <!-- 翻转卡片 -->
        <div class="flip-card" id="flipCard">
            <div class="flip-card-inner" id="flipCardInner">
                <!-- 中文面（正面） -->
                <div class="flip-card-front">
                    <div class="chinese-word" id="chineseWord">加载中...</div>
                    <div class="flip-hint">
                        <span>👆 点击卡片看英文</span>
                    </div>
                </div>
                
                <!-- 英文面（背面） -->
                <div class="flip-card-back">
                    <img id="wordImage" class="word-image" src="" alt="单词图片" style="display: none;">
                    <div class="english-word" id="englishWord">loading...</div>
                    
                    <!-- 发音按钮 -->
                    <button class="audio-button" onclick="playAudio(event)">
                        🔊
                    </button>
                    
                    <!-- 学习提示 -->
                    <div class="learning-tips">
                        <div class="tip-section">
                            <div class="tip-title">
                                <span>🧠</span>
                                <span>AI帮帮记</span>
                            </div>
                            <div class="tip-content">
                                <div id="memoryHelpContent" class="memory-help-content">
                                    <p>👆 翻转卡片查看AI记忆技巧</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn btn-success" onclick="markAsLearned()" id="learnedBtn">
                学会了！✨
            </button>
            <button class="btn btn-warning" onclick="needMorePractice()" id="practiceBtn">
                需要多练习 🔄
            </button>
            <button class="btn btn-primary" onclick="showMemoryMethodDialog()" id="memoryMethodBtn">
                我的记忆方法 📝
            </button>
            <button class="btn btn-primary" onclick="completeSession()" style="display: none;" id="completeBtn">
                完成学习 🎯
            </button>
        </div>
    </div>
    
    <script>
        // 学习数据
        let currentIndex = 0;
        let learningWords = [];
        let isFlipped = false;
        let completedWords = [];
        
        // 生成星星背景
        function createStars() {
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 30; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.width = Math.random() * 20 + 10 + 'px';
                star.style.height = star.style.width;
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                starsContainer.appendChild(star);
            }
        }

        // 创建庆祝动画
        function createCelebration() {
            const celebration = document.getElementById('celebration');
            celebration.innerHTML = '';
            
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.backgroundColor = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#f0932b'][Math.floor(Math.random() * 5)];
                confetti.style.animationDelay = Math.random() * 2 + 's';
                celebration.appendChild(confetti);
            }
            
            celebration.classList.add('active');
            setTimeout(() => {
                celebration.classList.remove('active');
            }, 3000);
        }
        
        // 翻转卡片
        document.getElementById('flipCard').addEventListener('click', function() {
            const inner = document.getElementById('flipCardInner');
            isFlipped = !isFlipped;
            if (isFlipped) {
                inner.classList.add('flipped');
                // 翻转到背面时加载AI记忆助手
                loadMemoryHelp();
                // 修复移动端图片显示
                setTimeout(() => {
                    fixMobileImageDisplay();
                }, 300);
            } else {
                inner.classList.remove('flipped');
            }
        });
        
        // 播放音频
        function playAudio(event) {
            // 阻止事件冒泡，防止触发卡片翻转
            event.stopPropagation();
            
            const button = event.target;
            button.classList.add('bounce');
            
            // 获取当前单词
            const currentWord = learningWords[currentIndex];
            if (currentWord) {
                // 🔧 修复：使用英文答案构建音频文件路径
                const audioFileName = currentWord.answer.toLowerCase().replace(/\s+/g, '_');
                const audioPath = `/static/audio/words/${audioFileName}.mp3`;
                
                // 创建音频对象尝试播放
                const audio = new Audio(audioPath);
                
                audio.play().then(() => {
                    console.log('音频播放成功:', audioPath);
                }).catch(error => {
                    console.log('音频文件不存在或播放失败，尝试使用语音合成:', error);
                    // 如果音频文件不存在，使用语音合成
                    fallbackToSpeechSynthesis(currentWord.answer);
                });
            }
            
            setTimeout(() => {
                button.classList.remove('bounce');
            }, 1000);
        }

        // 语音合成后备方案
        function fallbackToSpeechSynthesis(word) {
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(word);
                utterance.lang = 'en-US';
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }
        }

        // 🌟 Markdown解析器 - 轻量级实现
        function parseMarkdown(text) {
            if (!text) return '';
            
            return text
                // 标题
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                
                // 粗体和斜体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                
                // 列表
                .replace(/^\* (.*$)/gim, '<li>$1</li>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                
                // 链接
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
                
                // 行内代码
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                
                // 换行
                .replace(/\n\n/g, '</p><p>')
                .replace(/\n/g, '<br>')
                
                // 包装段落
                .replace(/^(?!<[h|l|d])(.+)/gim, '<p>$1</p>')
                
                // 清理连续的列表项，包装在ul标签中
                .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
                
                // 清理多余的p标签
                .replace(/<p><\/p>/g, '')
                .replace(/<p>(<h[1-6]>)/g, '$1')
                .replace(/(<\/h[1-6]>)<\/p>/g, '$1');
        }

        // 加载AI记忆助手
        function loadMemoryHelp() {
            const currentWord = learningWords[currentIndex];
            if (!currentWord || currentWord.memoryHelpLoaded) return;
            
            const memoryHelpContent = document.getElementById('memoryHelpContent');
            memoryHelpContent.innerHTML = '<p>🤔 AI正在思考记忆技巧...</p>';
            
            fetch('/api/get_memory_help', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    word: currentWord.word   // 🔧 修复：传递中文含义用于查找
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.memory_help) {
                    // 🌟 使用Markdown渲染AI记忆助手内容
                    memoryHelpContent.innerHTML = parseMarkdown(data.memory_help);
                } else {
                    memoryHelpContent.innerHTML = parseMarkdown(`
### 📝 记忆技巧

**联想记忆：** ${currentWord.word} (中文) → ${currentWord.answer} (英文)

*试着将这个单词与你熟悉的事物联系起来！*

- 想象一个相关的场景
- 找到发音或字形的相似点  
- 创造一个有趣的故事
                    `);
                }
                currentWord.memoryHelpLoaded = true;
                
                // 加载用户记忆方法
                loadUserMemoryMethod();
            })
            .catch(error => {
                console.error('AI记忆助手加载失败:', error);
                memoryHelpContent.innerHTML = parseMarkdown(`
### 📝 记忆技巧

**联想记忆：** ${currentWord.word} (中文) → ${currentWord.answer} (英文)

*试着将这个单词与你熟悉的事物联系起来！*

- 想象一个相关的场景
- 找到发音或字形的相似点
- 创造一个有趣的故事
                `);
                // 即使AI加载失败也要加载用户记忆方法
                loadUserMemoryMethod();
            });
        }
        
        // 加载用户记忆方法
        function loadUserMemoryMethod() {
            const currentWord = learningWords[currentIndex];
            if (!currentWord) return;
            
            fetch('/api/get_memory_method', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    word_id: currentWord.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.memory_method) {
                    // 在学习提示区域添加用户记忆方法
                    const learningTips = document.querySelector('.flip-card-back .learning-tips');
                    const existingUserMethod = learningTips.querySelector('.user-memory-method');
                    
                    if (existingUserMethod) {
                        existingUserMethod.remove();
                    }
                    
                    const userMethodDiv = document.createElement('div');
                    userMethodDiv.className = 'user-memory-method';
                    userMethodDiv.innerHTML = `
                        <h4>
                            <span>💡</span>
                            <span>我的记忆方法</span>
                        </h4>
                        <p>${data.memory_method}</p>
                    `;
                    
                    learningTips.appendChild(userMethodDiv);
                }
            })
            .catch(error => {
                console.error('加载用户记忆方法失败:', error);
            });
        }
        
        // 显示记忆方法弹窗
        function showMemoryMethodDialog() {
            const currentWord = learningWords[currentIndex];
            if (!currentWord) return;
            
            const modal = document.getElementById('memoryMethodModal');
            const textarea = document.getElementById('memoryMethodInput');
            
            // 先加载已有的记忆方法
            fetch('/api/get_memory_method', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    word_id: currentWord.id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.memory_method) {
                    textarea.value = data.memory_method;
                } else {
                    textarea.value = '';
                }
            })
            .catch(error => {
                console.error('加载记忆方法失败:', error);
                textarea.value = '';
            });
            
            modal.style.display = 'flex';
            setTimeout(() => textarea.focus(), 100);
        }
        
        // 关闭记忆方法弹窗
        function closeMemoryMethodDialog() {
            const modal = document.getElementById('memoryMethodModal');
            modal.style.display = 'none';
        }
        
        // 保存记忆方法
        function saveMemoryMethod() {
            const currentWord = learningWords[currentIndex];
            if (!currentWord) return;
            
            const memoryMethod = document.getElementById('memoryMethodInput').value.trim();
            
            if (!memoryMethod) {
                alert('请输入记忆方法');
                return;
            }
            
            fetch('/api/save_memory_method', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    word_id: currentWord.id,
                    memory_method: memoryMethod
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeMemoryMethodDialog();
                    // 重新加载用户记忆方法显示
                    loadUserMemoryMethod();
                    // 显示成功提示
                    alert('记忆方法已保存！');
                } else {
                    alert('保存失败，请重试');
                }
            })
            .catch(error => {
                console.error('保存记忆方法失败:', error);
                alert('保存失败，请重试');
            });
        }
        
        // 标记为已学会
        function markAsLearned() {
            const currentWord = learningWords[currentIndex];
            if (!currentWord) return;
            
            // 记录学习结果
            recordLearningResult(currentWord.id, true);
            
            // 添加到已完成列表
            completedWords.push({...currentWord, learned: true});
            
            // 显示鼓励动画
            createCelebration();
            
            // 进入下一个单词
            nextWord();
        }
        
        // 需要更多练习
        function needMorePractice() {
            const currentWord = learningWords[currentIndex];
            if (!currentWord) return;
            
            // 记录学习结果（需要更多练习）
            recordLearningResult(currentWord.id, false);
            
            // 添加到已完成列表
            completedWords.push({...currentWord, learned: false});
            
            // 进入下一个单词
            nextWord();
        }

        // 记录学习结果到后端
        function recordLearningResult(wordId, isLearned) {
            fetch('/api/record_card_learning', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    word_id: wordId,
                    is_learned: isLearned,
                    mode: 'card_learning'
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('学习结果已记录:', data);
            })
            .catch(error => {
                console.error('记录学习结果失败:', error);
            });
        }
        
        // 下一个单词
        function nextWord() {
            currentIndex++;
            if (currentIndex < learningWords.length) {
                loadCard(currentIndex);
            } else {
                // 所有单词学习完成
                showCompletionSummary();
            }
        }

        // 显示完成总结
        function showCompletionSummary() {
            document.getElementById('learnedBtn').style.display = 'none';
            document.getElementById('practiceBtn').style.display = 'none';
            document.getElementById('completeBtn').style.display = 'block';
            
            // 更新卡片内容显示总结
            const front = document.querySelector('.flip-card-front');
            const back = document.querySelector('.flip-card-back');
            
            const learnedCount = completedWords.filter(w => w.learned).length;
            const totalCount = completedWords.length;
            
            front.innerHTML = `
                <div class="chinese-word">🎉 完成!</div>
                <div class="flip-hint">
                    <span>👆 点击查看学习总结</span>
                </div>
            `;
            
            back.innerHTML = `
                <div class="english-word" style="font-size: 40px;">学习总结</div>
                <div class="learning-tips" style="margin-top: 20px;">
                    <div class="tip-section">
                        <div class="tip-title">
                            <span>📊</span>
                            <span>学习统计</span>
                        </div>
                        <div class="tip-content">
                            <p><strong>总单词数:</strong> ${totalCount} 个</p>
                            <p><strong>已掌握:</strong> ${learnedCount} 个</p>
                            <p><strong>需要练习:</strong> ${totalCount - learnedCount} 个</p>
                        </div>
                    </div>
                    <div class="tip-section">
                        <div class="tip-title">
                            <span>🎯</span>
                            <span>学习建议</span>
                        </div>
                        <div class="tip-content">
                            <p>${learnedCount === totalCount ? 
                                '太棒了！你已经掌握了所有单词！' : 
                                '继续加油！多练习那些还没掌握的单词。'}</p>
                        </div>
                    </div>
                </div>
            `;
            
            // 重置翻转状态
            isFlipped = false;
            document.getElementById('flipCardInner').classList.remove('flipped');
            
            // 显示庆祝动画
            createCelebration();
        }

        // 完成学习会话
        function completeSession() {
            // 可以跳转到学习报告页面或者返回首页
            if (confirm('恭喜完成卡片学习！是否返回首页？')) {
                window.location.href = '/dashboard';
            }
        }
        
        // AI图片生成功能
        async function generateImageForCard(englishWord, chineseMeaning, imgElement) {
            try {
                console.log('🎨 开始为卡片生成图片:', englishWord, chineseMeaning);
                
                // 显示生成状态
                showImageGenerationStatus(imgElement, '正在生成图片...');
                
                // 调用后端图片生成API
                const response = await fetch('/api/generate_image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        word: englishWord,
                        meaning: chineseMeaning
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success) {
                        console.log('✅ AI图片生成成功:', result);
                        
                        // 加载新生成的图片
                        const newImageUrl = result.image_url + '?t=' + Date.now();
                        imgElement.src = newImageUrl;
                        imgElement.style.display = 'block';
                        
                        // 显示成功状态
                        showImageGenerationStatus(imgElement, '图片生成成功！', 'success');
                        
                    } else {
                        console.log('❌ AI图片生成失败:', result.message);
                        showImageGenerationStatus(imgElement, '图片生成失败', 'error');
                        imgElement.style.display = 'none';
                    }
                } else {
                    console.log('❌ 图片生成API调用失败:', response.status);
                    showImageGenerationStatus(imgElement, '图片生成服务暂时不可用', 'error');
                    imgElement.style.display = 'none';
                }
                
            } catch (error) {
                console.error('❌ 图片生成异常:', error);
                showImageGenerationStatus(imgElement, '图片生成异常', 'error');
                imgElement.style.display = 'none';
            }
        }
        
        // 移动端图片加载检测和修复
        function fixMobileImageDisplay() {
            const wordImage = document.getElementById('wordImage');
            if (!wordImage) return;
            
            // 检查图片是否正确显示
            if (wordImage.src && wordImage.complete) {
                if (wordImage.naturalWidth === 0) {
                    console.log('🔧 移动端图片显示异常，尝试修复');
                    wordImage.style.display = 'block';
                    wordImage.style.visibility = 'visible';
                    wordImage.style.opacity = '1';
                    
                    // 强制重新加载
                    const originalSrc = wordImage.src;
                    wordImage.src = '';
                    setTimeout(() => {
                        wordImage.src = originalSrc + '?t=' + Date.now();
                    }, 100);
                }
            }
        }
        
        // 显示图片生成状态
        function showImageGenerationStatus(imgElement, message, type = 'info') {
            // 创建或更新状态显示
            let statusElement = document.getElementById('image-generation-status');
            
            if (!statusElement) {
                statusElement = document.createElement('div');
                statusElement.id = 'image-generation-status';
                statusElement.style.cssText = `
                    position: absolute;
                    top: 10px;
                    left: 50%;
                    transform: translateX(-50%);
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: bold;
                    z-index: 1000;
                    max-width: 200px;
                    text-align: center;
                `;
                
                // 将状态元素添加到图片容器
                const imageContainer = imgElement.parentElement;
                if (imageContainer) {
                    imageContainer.style.position = 'relative';
                    imageContainer.appendChild(statusElement);
                }
            }
            
            // 设置样式和内容
            const styles = {
                info: { background: 'rgba(33, 150, 243, 0.9)', color: 'white' },
                success: { background: 'rgba(76, 175, 80, 0.9)', color: 'white' },
                error: { background: 'rgba(244, 67, 54, 0.9)', color: 'white' }
            };
            
            const style = styles[type] || styles.info;
            statusElement.style.background = style.background;
            statusElement.style.color = style.color;
            statusElement.textContent = `🎨 ${message}`;
            
            // 自动隐藏成功和错误消息
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    if (statusElement && statusElement.parentElement) {
                        statusElement.remove();
                    }
                }, 3000);
            }
        }
        
        // 加载卡片数据
        function loadCard(index) {
            const word = learningWords[index];
            
            // 重置翻转状态
            isFlipped = false;
            document.getElementById('flipCardInner').classList.remove('flipped');
            
            // 🔧 修复：更新内容字段映射
            document.getElementById('chineseWord').textContent = word.word;    // word字段是中文
            document.getElementById('englishWord').textContent = word.answer;  // answer字段是英文
            
            // 🔧 修复：加载单词图片（使用英文answer字段）
            const wordImage = document.getElementById('wordImage');
            const imageName = word.answer.toLowerCase().replace(/\s+/g, '_');
            const imagePath = `/static/images/words/${imageName}.jpg`;
            
            // 智能图片加载 - 移动端优化版本
            const testImage = new Image();
            testImage.onload = function() {
                // 图片加载成功
                wordImage.src = imagePath;
                wordImage.style.display = 'block';
                wordImage.style.visibility = 'visible';
                wordImage.style.opacity = '1';
                console.log('✅ 图片加载成功:', imagePath);
            };
            testImage.onerror = function() {
                // 图片加载失败，尝试AI生成
                console.log('❌ 图片不存在，尝试AI生成:', imagePath);
                generateImageForCard(word.answer, word.word, wordImage);
            };
            // 设置图片初始状态
            wordImage.style.visibility = 'hidden';
            wordImage.style.opacity = '0';
            wordImage.style.transition = 'opacity 0.3s ease';
            testImage.src = imagePath;
            
            // 重置AI记忆助手
            document.getElementById('memoryHelpContent').innerHTML = '<p>👆 翻转卡片查看AI记忆技巧</p>';
            word.memoryHelpLoaded = false;
            
            // 清除之前的用户记忆方法
            const existingUserMethod = document.querySelector('.user-memory-method');
            if (existingUserMethod) {
                existingUserMethod.remove();
            }
            
            // 更新进度
            const progress = ((index + 1) / learningWords.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = `${index + 1}/${learningWords.length}`;
            
            // 延迟检查移动端图片显示
            setTimeout(() => {
                if (window.innerWidth <= 768) {
                    fixMobileImageDisplay();
                }
            }, 500);
        }
        
        // 初始化
        window.onload = async function() {
            createStars();
            
            // 从API获取学习计划
            try {
                const response = await fetch('/api/daily_learning_plan', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success && result.words && result.words.length > 0) {
                    learningWords = result.words;
                    loadCard(0);
                } else {
                    // 没有单词时的提示
                    alert('今天没有需要学习的单词！请先导入单词或查看其他学习计划。');
                    window.location.href = '/dashboard';
                }
            } catch (error) {
                console.error('获取学习计划失败:', error);
                alert('加载失败，请刷新页面重试');
                window.location.href = '/dashboard';
            }
        };
    </script>
</body>
</html> 