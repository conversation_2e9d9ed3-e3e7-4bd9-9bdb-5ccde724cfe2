{"timestamp": "2025-07-25T21:56:12.776208", "total_alerts": 3, "alerts_by_severity": {"CRITICAL": 1, "HIGH": 1, "MEDIUM": 1, "LOW": 0}, "alerts": [{"timestamp": "2025-07-25T21:56:12.775302", "rule_name": "MECE_COMPLIANCE_LOW", "severity": "HIGH", "message": "MECE合规率过低: 2.2% < 95.0%", "current_value": 2.18978102189781, "threshold": 95.0, "suggestions": ["运行 generate_missing_text_resources.py 修复 0 个缺失文本", "运行 resource_sync_monitor.py 自动生成 36 个缺失音频", "执行 fix_resource_naming.py 标准化现有资源命名"]}, {"timestamp": "2025-07-25T21:56:12.775309", "rule_name": "RESOURCE_INTEGRITY_LOW", "severity": "MEDIUM", "message": "资源完整性过低: 96.2% < 98.0%", "current_value": 96.24608967674662, "threshold": 98.0, "suggestions": []}, {"timestamp": "2025-07-25T21:56:12.775313", "rule_name": "CORRUPTED_FILES_DETECTED", "severity": "CRITICAL", "message": "检测到损坏文件: 902 个文件无法读取", "current_value": 902, "threshold": 0, "suggestions": ["备份损坏文件到 backups/ 目录", "重新生成损坏的资源文件", "检查存储设备是否存在硬件问题"]}]}