{"last_update": "2025-07-25T21:56:12.777050", "current_status": {"mece_compliance_rate": 2.18978102189781, "resource_integrity_rate": 96.24608967674662, "total_words": 959, "perfect_matches": 21, "issues_count": 938}, "resource_breakdown": {"missing_audio": 36, "missing_text": 0, "orphaned_audio": 115, "orphaned_text": 63, "corrupted_files": 902}, "performance_metrics": {"total_size_mb": 1.645766258239746, "avg_audio_size_kb": 18.26376488095238, "avg_text_size_kb": 1.3573780875260688, "audit_duration_seconds": 0.1400909423828125}, "alerts_summary": {"total_alerts": 3, "critical_alerts": 1, "high_alerts": 1, "active_issues": ["MECE合规率过低: 2.2% < 95.0%", "检测到损坏文件: 902 个文件无法读取"]}, "trends": {"compliance_history": [0.0, 2.18978102189781, 2.18978102189781], "compliance_trend": "improving"}, "recommendations": [{"priority": "CRITICAL", "action": "fix_corrupted_files", "description": "立即修复 902 个损坏文件", "command": "python scripts/fix_corrupted_resources.py"}, {"priority": "HIGH", "action": "generate_missing_audio", "description": "生成 36 个缺失音频资源", "command": "python scripts/resource_sync_monitor.py"}, {"priority": "MEDIUM", "action": "cleanup_orphaned_files", "description": "清理 178 个孤立文件", "command": "python scripts/cleanup_orphaned_resources.py"}]}