/* 🚀 前后端分离版本 - learning页面专用样式 */
/* 从learning.html内联样式中提取的CSS，避免重复定义 */

/* 基础布局样式 */
body {
    background-image: url('/static/images/background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
}

.dashboard-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    padding: var(--spacing-lg, 16px);
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius-xl, 12px);
    overflow: auto;
    max-height: 100vh;
    gap: var(--spacing-lg, 16px);
}

.left-column {
    flex: 0 0 280px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.right-column {
    flex: 1;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 头部区域样式 */
.header-container {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 12px !important;
    width: 100% !important;
}

.header-container h1 {
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1D1D1F !important;
    flex: 1 !important;
}

/* 积分显示样式 */
#score {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #333 !important;
    cursor: pointer !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    flex: 0 0 auto !important;
}

/* 返回按钮样式 */
.back-to-dashboard {
    background: #F2F2F7;
    color: #48484A;
    border: 1px solid #D1D1D6;
    padding: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    display: inline-block;
    transition: all 0.2s ease;
    margin-bottom: 12px;
    align-self: flex-start;
}

.back-to-dashboard:hover {
    background: #E5E5EA;
    border-color: #C7C7CC;
    transform: translateY(-1px);
    text-decoration: none;
}

/* 复选框区域样式 */
.checkbox-group {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 16px;
    gap: 16px;
}

.checkbox-group label {
    font-size: 14px;
    color: #48484A;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.checkbox-group input[type="checkbox"] {
    width: 12px;
    height: 12px;
    accent-color: #007AFF;
}

/* 今日计划样式 */
#daily-plan {
    font-size: 13px;
    text-align: left;
    margin-bottom: 12px;
}

#daily-plan h2 {
    margin: 0 0 8px 0;
    font-size: 15px;
    font-weight: 600;
    color: #1D1D1F;
}

#daily-plan p {
    margin: 4px 0;
    line-height: 1.4;
    color: #48484A;
    font-size: 13px;
}

#daily-plan a {
    font-size: 12px;
    color: #007AFF;
    text-decoration: none;
}

#daily-plan a:hover {
    text-decoration: underline;
}

/* 星级筛选复选框样式 */
.star-filter-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin: 8px 0;
}

.star-filter-item {
    display: flex;
    align-items: center;
}

.star-filter-item label {
    font-size: 12px;
    color: #48484A;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    padding: 2px 0;
    width: 100%;
}

.star-filter-item input[type="checkbox"] {
    width: 16px !important;
    height: 16px !important;
    margin: 0 !important;
    padding: 0 !important;
    appearance: auto !important;
    -webkit-appearance: checkbox !important;
    -moz-appearance: checkbox !important;
    cursor: pointer;
}

/* 导出按钮样式 */
#low-correct-rate-btn {
    width: 100%;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    background: #F2F2F7;
    color: #48484A;
    border: 1px solid #D1D1D6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1.3;
    text-align: center;
    white-space: normal;
    height: auto;
    min-height: 36px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

#low-correct-rate-btn:hover {
    background: #E5E5EA;
    border-color: #C7C7CC;
    transform: translateY(-1px);
}

#low-correct-rate-btn .sub-text {
    font-size: 10px;
    color: #8E8E93;
    margin-top: 2px;
    font-weight: 400;
}

/* 单词显示区域 */
#word-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    position: relative;
}

#word-container img {
    width: 100px;
    height: auto;
    margin-bottom: 10px;
    transition: opacity 0.3s ease, filter 0.3s ease;
}

#word-and-pronounce {
    display: flex;
    align-items: center;
}

#pronounce-button {
    margin-left: 10px;
    cursor: pointer;
}

/* 图片状态指示器 */
.image-status {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    display: none;
}

.image-status.generating {
    background: #FF9800;
    display: block;
}

.image-status.success {
    background: #4CAF50;
    display: block;
}

.image-status.error {
    background: #F44336;
    display: block;
}

/* 输入和按钮样式 */
input[type="text"], select, button {
    width: 100%;
    padding: 10px;
    margin: 5px 0;
    box-sizing: border-box;
    font-size: 16px;
    height: 44px;
}

button {
    cursor: pointer;
}

/* 传统模式输入样式 */
#traditional-mode {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
    width: 100%;
}

#traditional-mode input[type="text"] {
    width: 100%;
    height: 44px;
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 10px;
    border: 1px solid #D1D1D6;
    box-sizing: border-box;
    background: #FFFFFF;
    transition: all 0.2s ease;
}

#traditional-mode input[type="text"]:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

#traditional-mode button {
    width: 100%;
    height: 44px;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 10px;
    border: 1px solid #D1D1D6;
    background: #F2F2F7;
    color: #48484A;
    cursor: pointer;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

#traditional-mode button:hover {
    background: #E5E5EA;
    border-color: #C7C7CC;
    transform: translateY(-1px);
}

/* 功能按钮样式 */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
    width: 100%;
}

.action-buttons button {
    width: 100%;
    height: 44px;
    padding: 12px 16px;
    font-size: 15px;
    font-weight: 500;
    border-radius: 10px;
    border: 1px solid #D1D1D6;
    background: #F2F2F7;
    color: #48484A;
    cursor: pointer;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.action-buttons button:hover {
    background: #E5E5EA;
    border-color: #C7C7CC;
    transform: translateY(-1px);
}

/* 拼写模式样式 */
#spelling-container {
    width: 100%;
    max-width: 400px;
    margin: 20px auto;
}

#spelling-input-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

#spelling-input {
    flex: 1;
    padding: 12px;
    font-size: 18px;
    border: 2px solid #ddd;
    border-radius: 8px;
    margin-right: 10px;
}

#play-audio-spelling {
    padding: 12px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
}

#spelling-feedback {
    margin-bottom: 15px;
}

#letter-feedback {
    font-size: 24px;
    font-family: monospace;
    text-align: center;
    margin-bottom: 10px;
    min-height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.letter-box {
    display: inline-block;
    min-width: 32px;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    margin: 2px;
    padding: 6px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.letter-correct {
    color: #4CAF50;
    background: #E8F5E8;
    border: 2px solid #4CAF50;
    animation: bounce 0.4s ease;
}

.letter-incorrect {
    color: #F44336;
    background: #FFEBEE;
    border: 2px solid #F44336;
    animation: shake 0.3s ease-in-out;
}

.letter-pending {
    color: #999;
    background: #f9f9f9;
    border: 2px dashed #ddd;
    font-size: 16px;
}

#progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

#progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    width: 0%;
    transition: width 0.3s ease;
}

#spelling-hints {
    text-align: center;
    color: #666;
    font-size: 14px;
}

#syllable-hint, #length-hint {
    margin: 5px 0;
}

/* 反馈区域样式 */
#feedback.correct {
    color: green;
    font-size: 24px;
}

#feedback.incorrect {
    color: red;
    font-size: 24px;
}

/* 提示区域样式 */
#hint {
    display: block;
    margin-top: 10px;
    font-style: italic;
    color: #888;
    text-align: center;
    margin-bottom: 12px;
    font-size: 13px;
}

/* AI记忆帮助区域样式 */
#memory-help {
    margin-top: 20px;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: left;
}

#memory-help h1, #memory-help h2, #memory-help h3 {
    margin: 0;
    padding: 0;
}

#memory-help ul {
    margin: 5px 0;
    padding-left: 20px;
}

#memory-help p {
    margin: 5px 0;
}

/* 低正确率单词表格样式 */
.low-correct-rate-container {
    margin-top: 20px;
    width: 100%;
    max-width: 800px;
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.low-correct-rate-container h2 {
    margin-top: 0;
}

.low-correct-rate-container table {
    width: 100%;
    border-collapse: collapse;
}

.low-correct-rate-container th, .low-correct-rate-container td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.low-correct-rate-container th {
    background-color: #f2f2f2;
}

/* 动画效果 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 手机端优化 */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
        gap: 8px;
        padding: 8px;
        max-width: 100%;
    }

    .left-column {
        flex: none;
        order: 1;
        padding: 12px;
        margin-bottom: 0;
    }

    .right-column {
        flex: none;
        order: 2;
        padding: 12px;
    }

    .header-container h1 {
        font-size: 16px !important;
    }

    #score {
        font-size: 14px !important;
    }

    .checkbox-group {
        gap: 12px;
    }

    .checkbox-group label {
        font-size: 12px;
    }

    #daily-plan {
        font-size: 11px;
    }

    #daily-plan h2 {
        font-size: 13px;
    }

    #word {
        font-size: 24px;
    }

    #hint {
        font-size: 14px;
    }

    .action-buttons {
        gap: 6px;
    }

    .action-buttons button {
        height: 44px;
        font-size: 15px;
    }

    #traditional-mode input[type="text"] {
        height: 44px;
        font-size: 16px;
    }

    #traditional-mode button {
        height: 44px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 6px;
        gap: 6px;
    }

    .left-column,
    .right-column {
        padding: 10px;
    }

    .header-container h1 {
        font-size: 14px;
    }

    #score {
        font-size: 12px;
    }

    .checkbox-group label {
        font-size: 11px;
    }

    #daily-plan {
        font-size: 10px;
    }

    #daily-plan h2 {
        font-size: 12px;
    }

    #word {
        font-size: 20px;
    }

    .action-buttons button,
    #traditional-mode input[type="text"],
    #traditional-mode button {
        height: 48px;
        font-size: 16px;
    }
}

/* Recognition模式（选择题）样式 */
#recognition-mode {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

#recognition-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

#recognition-question {
    text-align: center;
    margin-bottom: 25px;
}

#question-text {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

#recognition-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 25px;
}

.option-button {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    min-height: 60px;
}

.option-button:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.option-button.selected {
    background: linear-gradient(135deg, #e8f5e8 0%, #c5e1a5 100%);
    border-color: #4caf50;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.option-button.correct {
    background: linear-gradient(135deg, #e8f5e8 0%, #a5d6a7 100%);
    border-color: #4caf50;
    color: #2e7d32;
}

.option-button.incorrect {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border-color: #f44336;
    color: #c62828;
}

.option-label {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.option-button.selected .option-label {
    background: #4caf50;
}

.option-button.correct .option-label {
    background: #4caf50;
}

.option-button.incorrect .option-label {
    background: #f44336;
}

.option-text {
    flex: 1;
    font-weight: 500;
}

#recognition-submit {
    text-align: center;
    margin-bottom: 20px;
}

#recognition-confirm-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

#recognition-confirm-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

#recognition-confirm-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

#recognition-feedback {
    text-align: center;
    padding: 20px;
    background: rgba(248, 249, 250, 0.95);
    border-radius: 10px;
    margin-top: 15px;
}

#recognition-result {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

#recognition-result.correct {
    color: #4caf50;
}

#recognition-result.incorrect {
    color: #f44336;
}

/* 🚀 多选题样式 */
#recognition-result.partial {
    color: #ff9800;
}

#recognition-explanation {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.4;
}

#recognition-feedback button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

#recognition-feedback button:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    transform: translateY(-1px);
}

/* 移动端适配 */
@media (max-width: 768px) {
    #recognition-container {
        padding: 15px;
    }
    
    .option-button {
        padding: 12px 15px;
        font-size: 14px;
        min-height: 50px;
    }
    
    .option-label {
        width: 28px;
        height: 28px;
        margin-right: 12px;
        font-size: 14px;
    }
    
    #question-text {
        font-size: 16px;
    }
    
    #recognition-confirm-btn {
        padding: 10px 25px;
        font-size: 14px;
    }
}