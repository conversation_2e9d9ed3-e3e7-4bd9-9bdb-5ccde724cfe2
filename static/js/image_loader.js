/**
 * 智能图片加载器
 * 支持动态生成和缓存单词图片
 */

class ImageLoader {
    constructor() {
        this.loadingImages = new Set(); // 正在加载的图片
        this.failedImages = new Set();  // 加载失败的图片
        this.cache = new Map();         // 图片缓存
    }

    /**
     * 加载单词图片 - 优化版本（优先使用缓存，避免学习时等待）
     * @param {string} word - 单词
     * @param {HTMLImageElement} imgElement - 图片元素
     * @param {function} onSuccess - 成功回调
     * @param {function} onError - 失败回调
     * @param {boolean} allowGeneration - 是否允许生成图片（默认false，学习时不生成）
     */
    async loadWordImage(word, imgElement, onSuccess = null, onError = null, allowGeneration = false) {
        if (!word || !imgElement) {
            console.error('Invalid parameters for loadWordImage');
            return false;
        }

        // 防止重复加载
        if (this.loadingImages.has(word)) {
            console.log(`Image for "${word}" is already loading`);
            return false;
        }

        // 检查缓存
        if (this.cache.has(word)) {
            const cachedUrl = this.cache.get(word);
            this.setImageSrc(imgElement, cachedUrl, onSuccess);
            return true;
        }

        try {
            this.loadingImages.add(word);

            // 🔧 修复：优先尝试多种格式，支持WebP等新格式
            const possibleFormats = ['jpg', 'webp', 'png', 'gif'];
            let foundImageUrl = null;
            
            for (const format of possibleFormats) {
                const testUrl = `/static/images/words/${word}.${format}?t=${Date.now()}`;
                const imageExists = await this.testImageLoad(testUrl);
                
                if (imageExists) {
                    foundImageUrl = testUrl;
                    console.log(`✅ 找到图片: ${word}.${format}`);
                    break;
                }
            }

            if (foundImageUrl) {
                // 图片存在，直接使用
                this.setImageSrc(imgElement, foundImageUrl, onSuccess);
                this.cache.set(word, foundImageUrl);
                console.log(`✅ Image loaded from cache: ${word}`);
            } else {
                // 图片不存在，立即进行智能生成
                console.log(`📷 Image not found for: ${word}, starting AI generation`);
                this.generateImageIfNeeded(word, imgElement, onSuccess, onError);
            }

        } catch (error) {
            console.error(`Error loading image for "${word}":`, error);
            this.showPlaceholder(imgElement, word);
            this.failedImages.add(word);
            if (onError) onError(word);
        } finally {
            this.loadingImages.delete(word);
        }

        return true;
    }

    /**
     * 测试图片是否可以加载（不显示在页面上）
     */
    async testImageLoad(imageUrl) {
        return new Promise((resolve) => {
            const testImg = new Image();
            testImg.onload = () => resolve(true);
            testImg.onerror = () => resolve(false);
            testImg.src = imageUrl;

            // 设置超时，避免长时间等待
            setTimeout(() => resolve(false), 3000);
        });
    }

    /**
     * 检查图片是否存在
     */
    async checkImageExists(word) {
        try {
            const response = await fetch('/check_image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ word: word })
            });

            if (response.ok) {
                const data = await response.json();
                return data.exists;
            }
            return false;
        } catch (error) {
            console.error('Error checking image existence:', error);
            return false;
        }
    }

    /**
     * 智能图片生成 - 当图片不存在时自动生成
     */
    async generateImageIfNeeded(word, imgElement, onSuccess, onError) {
        try {
            console.log('🎨 开始AI图片生成:', word);
            
            // 显示生成状态
            this.showGenerationStatus(imgElement, '正在生成图片...');
            
            // 获取中文含义（如果可用）
            let chineseMeaning = '';
            const currentWordData = window.filteredWords && window.filteredWords[window.currentWordIndex];
            if (currentWordData) {
                chineseMeaning = currentWordData.word || '';
            }
            
            // 调用后端图片生成API
            const response = await fetch('/api/generate_image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    word: word,
                    meaning: chineseMeaning
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.success) {
                    console.log('✅ AI图片生成成功:', result);
                    
                    // 加载新生成的图片
                    const newImageUrl = result.image_url + '?t=' + Date.now(); // 避免缓存
                    this.setImageSrc(imgElement, newImageUrl, onSuccess);
                    this.cache.set(word, newImageUrl);
                    
                    // 显示成功状态
                    this.showGenerationStatus(imgElement, '图片生成成功！', 'success');
                    
                } else {
                    console.log('❌ AI图片生成失败:', result.message);
                    this.showGenerationStatus(imgElement, '图片生成失败', 'error');
                    this.showPlaceholder(imgElement, word);
                    if (onError) onError(word);
                }
            } else {
                console.log('❌ 图片生成API调用失败:', response.status);
                this.showGenerationStatus(imgElement, '图片生成服务暂时不可用', 'error');
                this.showPlaceholder(imgElement, word);
                if (onError) onError(word);
            }
            
        } catch (error) {
            console.error('❌ 图片生成异常:', error);
            this.showGenerationStatus(imgElement, '图片生成异常', 'error');
            this.showPlaceholder(imgElement, word);
            if (onError) onError(word);
        }
    }

    /**
     * 显示图片生成状态
     */
    showGenerationStatus(imgElement, message, type = 'info') {
        // 创建状态覆盖层
        const statusOverlay = document.createElement('div');
        statusOverlay.className = 'image-generation-status';
        statusOverlay.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            z-index: 10;
            min-width: 120px;
        `;
        
        // 设置样式
        const styles = {
            info: { background: 'rgba(33, 150, 243, 0.9)', color: 'white' },
            success: { background: 'rgba(76, 175, 80, 0.9)', color: 'white' },
            error: { background: 'rgba(244, 67, 54, 0.9)', color: 'white' }
        };
        
        const style = styles[type] || styles.info;
        statusOverlay.style.background = style.background;
        statusOverlay.style.color = style.color;
        statusOverlay.textContent = `🎨 ${message}`;
        
        // 确保父元素有相对定位
        const parent = imgElement.parentElement;
        if (parent) {
            parent.style.position = 'relative';
            
            // 移除旧的状态覆盖层
            const oldOverlay = parent.querySelector('.image-generation-status');
            if (oldOverlay) oldOverlay.remove();
            
            // 添加新的状态覆盖层
            parent.appendChild(statusOverlay);
            
            // 自动移除成功和错误状态
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    if (statusOverlay.parentElement) {
                        statusOverlay.remove();
                    }
                }, 3000);
            }
        }
    }

    /**
     * 生成图片（保留原有接口兼容性）
     */
    async generateImage(word) {
        try {
            const response = await fetch('/api/generate_image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ word: word })
            });

            if (response.ok) {
                const data = await response.json();
                return data.success;
            }
            return false;
        } catch (error) {
            console.error('Error generating image:', error);
            return false;
        }
    }

    /**
     * 设置图片源 - 移动端优化版本
     */
    setImageSrc(imgElement, src, onSuccess = null) {
        imgElement.onload = () => {
            // 移动端专用显示修复
            imgElement.style.display = 'block';
            imgElement.style.visibility = 'visible';
            imgElement.style.opacity = '1';
            imgElement.style.filter = 'none';
            
            // 移动端额外检查
            if (window.innerWidth <= 768) {
                setTimeout(() => {
                    this.forceMobileImageDisplay(imgElement);
                }, 100);
            }
            
            if (onSuccess) onSuccess(src);
            console.log('✅ 图片源设置成功 (移动端优化):', src);
        };

        imgElement.onerror = () => {
            console.log('❌ 图片加载错误，显示占位符');
            this.showPlaceholder(imgElement, 'Error');
        };

        imgElement.src = src;
    }
    
    /**
     * 强制移动端图片显示
     */
    forceMobileImageDisplay(imgElement) {
        if (!imgElement) return;
        
        // 强制设置所有显示属性
        imgElement.style.cssText += `
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            width: 120px;
            height: auto;
            max-width: 100%;
            margin: 0 auto 10px auto;
            border-radius: 8px;
        `;
        
        console.log('🔧 强制移动端图片显示已应用');
    }

    /**
     * 显示加载状态
     */
    showLoadingState(imgElement) {
        imgElement.style.opacity = '0.6';
        imgElement.style.filter = 'blur(2px)';
        imgElement.src = this.createLoadingPlaceholder();
    }

    /**
     * 显示占位符 - 移动端优化版本
     */
    showPlaceholder(imgElement, word) {
        imgElement.style.display = 'block';
        imgElement.style.visibility = 'visible';
        imgElement.style.opacity = '1';
        imgElement.style.filter = 'none';
        imgElement.src = this.createPlaceholder(word);
        
        // 移动端额外处理
        if (window.innerWidth <= 768) {
            this.forceMobileImageDisplay(imgElement);
        }
        
        console.log('📷 占位符已显示 (移动端优化):', word);
    }

    /**
     * 创建加载占位符
     */
    createLoadingPlaceholder() {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');

        // 渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 400, 300);
        gradient.addColorStop(0, '#E3F2FD');
        gradient.addColorStop(1, '#BBDEFB');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 300);

        // 加载文字
        ctx.fillStyle = '#666';
        ctx.font = '20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('🎨 生成中...', 200, 150);

        return canvas.toDataURL();
    }

    /**
     * 创建占位符图片
     */
    createPlaceholder(word) {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');

        // 背景
        ctx.fillStyle = '#F5F5F5';
        ctx.fillRect(0, 0, 400, 300);

        // 边框
        ctx.strokeStyle = '#DDD';
        ctx.lineWidth = 2;
        ctx.strokeRect(1, 1, 398, 298);

        // 图标
        ctx.fillStyle = '#999';
        ctx.font = '48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('🖼️', 200, 120);

        // 文字
        ctx.fillStyle = '#666';
        ctx.font = '16px Arial';
        ctx.fillText(word || 'Image', 200, 180);
        ctx.font = '12px Arial';
        ctx.fillText('图片生成中...', 200, 220);

        return canvas.toDataURL();
    }

    /**
     * 预加载图片
     */
    async preloadImages(words) {
        const promises = words.map(word => this.checkImageExists(word));
        const results = await Promise.all(promises);

        const missingWords = words.filter((word, index) => !results[index]);

        if (missingWords.length > 0) {
            console.log(`🎨 Pre-generating ${missingWords.length} missing images...`);

            // 批量生成图片（限制并发数）
            const batchSize = 3;
            for (let i = 0; i < missingWords.length; i += batchSize) {
                const batch = missingWords.slice(i, i + batchSize);
                const batchPromises = batch.map(word => this.generateImage(word));
                await Promise.all(batchPromises);
            }
        }
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.cache.clear();
        this.failedImages.clear();
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            cached: this.cache.size,
            loading: this.loadingImages.size,
            failed: this.failedImages.size
        };
    }
}

// 全局实例
window.imageLoader = new ImageLoader();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageLoader;
}
