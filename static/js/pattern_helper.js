/**
 * Pattern学习辅助功能
 * 提供单词pattern提示和相似单词推荐
 */

class PatternHelper {
    constructor() {
        this.apiBase = '/api';
        this.currentWordId = null;
        this.currentPatternData = null;
        this.isVisible = false;
        this.isPanelOpen = false;
        this.interactionCount = 0;
        
        this.init();
    }
    
    init() {
        this.createPatternUI();
        this.bindEvents();
        
        // 监听单词切换事件
        document.addEventListener('wordChanged', (event) => {
            this.onWordChanged(event.detail);
        });
        
        // 监听学习会话开始事件
        document.addEventListener('learningSessionStarted', () => {
            this.sessionId = 'session_' + Date.now();
        });
    }
    
    createPatternUI() {
        // 创建浮动图标
        const floatingIcon = document.createElement('div');
        floatingIcon.id = 'pattern-floating-icon';
        floatingIcon.className = 'pattern-floating-icon hidden';
        floatingIcon.innerHTML = `
            <div class="icon-wrapper">
                <span class="icon">🦄</span>
                <div class="badge hidden">0</div>
            </div>
        `;
        
        // 创建右侧滑出面板
        const slidePanel = document.createElement('div');
        slidePanel.id = 'pattern-slide-panel';
        slidePanel.className = 'pattern-slide-panel';
        slidePanel.innerHTML = `
            <div class="panel-header">
                <h4>🧠 语言学专家推荐</h4>
                <div class="expert-badge">专业语言学分析</div>
                <button class="panel-close-btn" title="关闭">×</button>
            </div>
            <div class="panel-content">
                <div class="pattern-recommendations">
                    <!-- 推荐内容将在这里动态填充 -->
                </div>
            </div>
            <div class="pattern-feedback">
                <span class="feedback-label">这些提示对你有帮助吗？</span>
                <div class="feedback-buttons">
                    <button class="feedback-btn helpful" data-type="helpful">👍 有帮助</button>
                    <button class="feedback-btn not-helpful" data-type="not_helpful">👎 没帮助</button>
                </div>
            </div>
        `;
        
        // 插入到页面中
        const body = document.body;
        body.appendChild(floatingIcon);
        body.appendChild(slidePanel);
        
        this.floatingIcon = floatingIcon;
        this.slidePanel = slidePanel;
    }
    
    bindEvents() {
        // 浮动图标点击事件
        this.floatingIcon.addEventListener('click', () => {
            this.openPanel();
        });
        
        // 关闭面板按钮
        this.slidePanel.querySelector('.panel-close-btn').addEventListener('click', () => {
            this.closePanel();
        });
        
        // 反馈按钮
        this.slidePanel.querySelectorAll('.feedback-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.recordFeedback(e.target.dataset.type);
            });
        });
        
        // 点击相似单词
        this.slidePanel.addEventListener('click', (e) => {
            if (e.target.classList.contains('similar-word')) {
                this.recordInteraction('click');
                this.highlightSimilarWord(e.target);
            }
        });
        
        // 点击面板外部关闭
        document.addEventListener('click', (e) => {
            if (this.isPanelOpen && 
                !this.slidePanel.contains(e.target) && 
                !this.floatingIcon.contains(e.target)) {
                this.closePanel();
            }
        });
    }
    
    async onWordChanged(wordData) {
        if (!wordData || !wordData.id) return;
        
        this.currentWordId = wordData.id;
        this.interactionCount = 0;
        
        // 立即加载数据，但不显示面板
        this.loadPatternSuggestions();
    }
    
    async loadPatternSuggestions() {
        if (!this.currentWordId) return;
        
        try {
            const response = await fetch(`${this.apiBase}/word_pattern_suggestions/${this.currentWordId}`, {
                method: 'GET',
                credentials: 'same-origin'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success && result.data) {
                this.currentPatternData = result.data;
                this.renderPatternSuggestions(result.data);
                this.showFloatingIcon();
                this.recordInteraction('view');
            } else {
                console.log('没有找到相关的pattern建议');
                this.hideFloatingIcon();
            }
            
        } catch (error) {
            console.error('加载pattern建议失败:', error);
            this.hideFloatingIcon();
        }
    }
    
    renderPatternSuggestions(data) {
        const contentDiv = this.slidePanel.querySelector('.pattern-recommendations');
        const badge = this.floatingIcon.querySelector('.badge');
        
        if (!data.has_recommendations) {
            contentDiv.innerHTML = '<div class="no-patterns">暂无相似单词提示</div>';
            badge.classList.add('hidden');
            return;
        }
        
        // 检查是否是语言学专家推荐
        const isLinguisticExpert = data.recommendations.some(rec => 
            rec.pattern_info && rec.pattern_info.is_linguistic_expert
        );
        
        if (isLinguisticExpert) {
            this.renderLinguisticExpertRecommendations(data, contentDiv, badge);
            return;
        }
        
        // 传统推荐渲染逻辑
        this.renderTraditionalRecommendations(data, contentDiv, badge);
    }
    
    renderLinguisticExpertRecommendations(data, contentDiv, badge) {
        let totalWords = 0;
        let html = `
            <div class="expert-recommendation-header">
                <div class="expert-indicator">
                    <span class="expert-icon">👨‍🎓</span>
                    <span class="expert-text">基于语言学专家标注的准确推荐</span>
                </div>
            </div>
        `;
        
        // 渲染每个推荐组
        data.recommendations.forEach((rec, index) => {
            const categoryIcon = this.getLinguisticCategoryIcon(rec.pattern_info.pattern_type);
            totalWords += rec.similar_words.length;
            
            html += `
                <div class="linguistic-recommendation-group" data-category="${rec.pattern_info.pattern_type}">
                    <div class="category-header">
                        <span class="category-icon">${categoryIcon}</span>
                        <span class="category-name">${rec.pattern_info.pattern_name}</span>
                        <span class="educational-value">${rec.pattern_info.educational_value}</span>
                    </div>
                    <div class="linguistic-explanation">
                        ${rec.recommendation_reason}
                    </div>
                    ${rec.pattern_info.linguistic_principle ? `
                        <div class="linguistic-principle">
                            <span class="principle-label">🧠 学习原理：</span>
                            ${rec.pattern_info.linguistic_principle}
                        </div>
                    ` : ''}
                    <div class="similar-words-grid">
                        ${rec.similar_words.map(word => `
                            <div class="linguistic-similar-word" data-word-id="${word.word_id}">
                                <div class="word-text">
                                    <strong>${word.english_word}</strong>
                                    <span class="word-meaning">${word.chinese_meaning}</span>
                                </div>
                                <div class="word-meta">
                                    <span class="proficiency-level">${Math.round(word.proficiency)}%熟练度</span>
                                    <span class="status-indicator ${word.learning_status}">${this.getStatusText(word.learning_status)}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        });
        
        contentDiv.innerHTML = html;
        
        // 更新徽章
        if (totalWords > 0) {
            badge.textContent = totalWords;
            badge.classList.remove('hidden');
        } else {
            badge.classList.add('hidden');
        }
    }
    
    renderTraditionalRecommendations(data, contentDiv, badge) {
        // 按认知层次分组
        const groupedByLevel = this.groupRecommendationsByLevel(data.recommendations);
        let totalWords = 0;
        let html = '';
        
        // 按认知层次顺序渲染
        const levelOrder = ['basic', 'intermediate', 'advanced'];
        const levelInfo = {
            'basic': { name: '基础感知层', icon: '🎯', description: '拼写发音基础' },
            'intermediate': { name: '语义关联层', icon: '🧠', description: '词义理解深化' },
            'advanced': { name: '高级应用层', icon: '🚀', description: '语法搭配提升' }
        };
        
        levelOrder.forEach(level => {
            if (!groupedByLevel[level] || groupedByLevel[level].length === 0) return;
            
            const levelConfig = levelInfo[level];
            const levelRecommendations = groupedByLevel[level];
            
            // 按维度分组
            const groupedByDimension = this.groupRecommendationsByDimension(levelRecommendations);
            
            html += `
                <div class="cognitive-level-section" data-level="${level}">
                    <div class="level-header">
                        <span class="level-icon">${levelConfig.icon}</span>
                        <span class="level-name">${levelConfig.name}</span>
                        <span class="level-description">${levelConfig.description}</span>
                    </div>
            `;
            
            // 按维度顺序渲染
            const dimensionOrder = ['orthography', 'semantic', 'morphology', 'collocation'];
            const dimensionInfo = {
                'orthography': { name: '拼写发音', icon: '📝', color: '#4CAF50' },
                'semantic': { name: '词义关联', icon: '💭', color: '#2196F3' },
                'morphology': { name: '构词变形', icon: '🔧', color: '#FF9800' },
                'collocation': { name: '搭配用法', icon: '🎯', color: '#9C27B0' }
            };
            
            dimensionOrder.forEach(dimension => {
                if (!groupedByDimension[dimension] || groupedByDimension[dimension].length === 0) return;
                
                const dimensionConfig = dimensionInfo[dimension];
                const dimensionRecommendations = groupedByDimension[dimension];
                
                html += `
                    <div class="dimension-section" data-dimension="${dimension}">
                        <div class="dimension-header" style="border-left-color: ${dimensionConfig.color}">
                            <span class="dimension-icon">${dimensionConfig.icon}</span>
                            <span class="dimension-name">${dimensionConfig.name}</span>
                        </div>
                `;
                
                // 渲染该维度下的所有推荐
                dimensionRecommendations.forEach((rec, index) => {
                    const isFirst = index === 0;
                    totalWords += rec.similar_words.length;
                    
                    const typeIcon = this.getPatternTypeIcon(rec.pattern_info.pattern_type);
                    const conceptTag = rec.pattern_info.is_concept_integrated ? 
                        `<span class="concept-tag">🦄 ${rec.pattern_info.concept_group}</span>` : '';
                    
                    html += `
                        <div class="pattern-group ${isFirst ? 'primary' : ''}" data-pattern-type="${rec.pattern_info.pattern_type}">
                            <div class="pattern-info">
                                <div class="pattern-type">
                                    <span class="pattern-type-icon">${typeIcon}</span>
                                    <span class="pattern-name">${rec.pattern_info.pattern_name || rec.pattern_info.pattern_type}</span>
                                    ${conceptTag}
                                </div>
                                <div class="pattern-reason">${rec.recommendation_reason}</div>
                                ${rec.pattern_info.match_reason ? `<div class="pattern-description">匹配原因：${rec.pattern_info.match_reason}</div>` : ''}
                            </div>
                            <div class="similar-words">
                                ${rec.similar_words.map(word => `
                                    <div class="similar-word" data-word-id="${word.word_id}">
                                        <strong>${word.english_word}</strong>
                                        <div class="word-meaning">${word.chinese_meaning}</div>
                                        <div class="word-status">${this.getStatusText(word.learning_status)} 
                                            <span class="proficiency-badge">${Math.round(word.proficiency)}%</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                });
                
                html += `</div>`; // 关闭 dimension-section
            });
            
            html += `</div>`; // 关闭 cognitive-level-section
        });
        
        contentDiv.innerHTML = html;
        
        // 更新徽章数字
        if (totalWords > 0) {
            badge.textContent = totalWords;
            badge.classList.remove('hidden');
        } else {
            badge.classList.add('hidden');
        }
    }
    
    groupRecommendationsByLevel(recommendations) {
        const groups = {
            'basic': [],
            'intermediate': [],
            'advanced': []
        };
        
        recommendations.forEach(rec => {
            const level = rec.pattern_info.cognitive_level || 'basic';
            if (groups[level]) {
                groups[level].push(rec);
            }
        });
        
        return groups;
    }
    
    groupRecommendationsByDimension(recommendations) {
        const groups = {
            'orthography': [],
            'semantic': [],
            'morphology': [],
            'collocation': []
        };
        
        recommendations.forEach(rec => {
            const dimension = rec.pattern_info.dimension_category || 'semantic';
            if (groups[dimension]) {
                groups[dimension].push(rec);
            }
        });
        
        return groups;
    }
    
    showFloatingIcon() {
        this.floatingIcon.classList.remove('hidden');
        this.isVisible = true;
        
        // 添加淡入动画
        setTimeout(() => {
            this.floatingIcon.classList.add('visible');
        }, 10);
    }
    
    hideFloatingIcon() {
        this.floatingIcon.classList.remove('visible');
        this.isVisible = false;
        
        setTimeout(() => {
            this.floatingIcon.classList.add('hidden');
        }, 300);
    }
    
    openPanel() {
        this.slidePanel.classList.add('open');
        this.isPanelOpen = true;
        document.body.classList.add('pattern-panel-open');
        this.recordInteraction('open_panel');
    }
    
    closePanel() {
        this.slidePanel.classList.remove('open');
        this.isPanelOpen = false;
        document.body.classList.remove('pattern-panel-open');
    }
    
    
    highlightSimilarWord(element) {
        // 移除其他高亮
        this.slidePanel.querySelectorAll('.similar-word.highlighted').forEach(el => {
            el.classList.remove('highlighted');
        });
        
        // 添加高亮
        element.classList.add('highlighted');
        
        // 3秒后移除高亮
        setTimeout(() => {
            element.classList.remove('highlighted');
        }, 3000);
    }
    
    async recordInteraction(type) {
        if (!this.currentPatternData || !this.currentWordId) return;
        
        this.interactionCount++;
        
        // 获取第一个pattern的ID作为主要pattern
        const primaryPattern = this.currentPatternData.recommendations[0];
        if (!primaryPattern) return;
        
        const patternId = primaryPattern.pattern_info.pattern_id;
        
        try {
            await fetch(`${this.apiBase}/pattern_interaction`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    pattern_id: patternId,
                    word_id: this.currentWordId,
                    interaction_type: type,
                    session_id: this.sessionId
                })
            });
        } catch (error) {
            console.error('记录交互失败:', error);
        }
    }
    
    async recordFeedback(feedbackType) {
        await this.recordInteraction(feedbackType);
        
        // 显示感谢提示
        const feedbackDiv = this.patternCard.querySelector('.pattern-feedback');
        const originalHTML = feedbackDiv.innerHTML;
        
        feedbackDiv.innerHTML = '<span class="feedback-thanks">感谢你的反馈！ 🙏</span>';
        
        // 3秒后恢复原样
        setTimeout(() => {
            feedbackDiv.innerHTML = originalHTML;
            this.bindFeedbackEvents();
        }, 3000);
    }
    
    bindFeedbackEvents() {
        this.patternCard.querySelectorAll('.feedback-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.recordFeedback(e.target.dataset.type);
            });
        });
    }
    
    getPatternTypeIcon(patternType) {
        const icons = {
            // 拼写发音维度 (orthography)
            'letter_combo': '🔤',
            'phonetic': '🔊',
            'rhyme': '🎵',
            'syllable': '✨',
            'first_letter': '🅰️',
            'silent_letter': '🤫',
            'word_length': '📏',
            
            // 词义关联维度 (semantic)
            'semantic': '🧠',
            'theme': '🎯',
            'synonym': '🔄',
            'antonym': '↔️',
            
            // 构词变形维度 (morphology)
            'morphology': '🔧',
            'root': '🌳',
            'prefix': '⬅️',
            'suffix': '➡️',
            'adjective_forms': '📊',
            'plural_forms': '🔢',
            'verb_forms': '⚡',
            
            // 搭配用法维度 (collocation)
            'collocation': '🎯',
            'usage_pattern': '💡',
            'contextual': '📖',
            'grammar': '📚',
            
            // 其他类型
            'similar_spelling': '🔍',
            'meaning_groups': '🎨'
        };
        return icons[patternType] || '🦄';
    }
    
    getDimensionIcon(dimension) {
        const icons = {
            'orthography': '📝',
            'semantic': '💭', 
            'morphology': '🔧',
            'collocation': '🎯'
        };
        return icons[dimension] || '📝';
    }
    
    getCognitiveLevelIcon(level) {
        const icons = {
            'basic': '🎯',
            'intermediate': '🧠',
            'advanced': '🚀'
        };
        return icons[level] || '🎯';
    }
    
    getStatusText(status) {
        const statusTexts = {
            'new': '新词',
            'review': '复习',
            'attention': '生词本'
        };
        return statusTexts[status] || status;
    }
    
    getLinguisticCategoryIcon(categoryType) {
        const icons = {
            // 前缀类别
            'true_prefix': '🔤',
            'prefix': '⬅️',
            
            // 语义类别
            'semantic_family': '👨‍👩‍👧‍👦',
            'semantic_emotion': '❤️',
            'semantic_color': '🌈',
            'semantic_basic_adjectives': '📝',
            'semantic_time': '⏰',
            'semantic_movement': '🏃',
            'semantic_daily_activities': '🏠',
            
            // 学习组类别
            'cluster_family_members': '👨‍👩‍👧‍👦',
            'cluster_emotion_adjectives': '😊',
            'cluster_color_words': '🎨',
            'cluster_opposite_pairs': '⚖️',
            'cluster_size_adjectives': '📏',
            
            // 对比类别
            'contrast': '⚖️',
            'antonym': '🔄',
            
            // 词汇关系
            'morphology': '🔧',
            'etymology': '📚'
        };
        return icons[categoryType] || '🔍';
    }
    
    // 公开方法：手动触发pattern建议
    loadSuggestionsForWord(wordId) {
        this.currentWordId = wordId;
        this.loadPatternSuggestions();
    }
    
    // 公开方法：检查是否支持pattern功能
    isPatternSupportEnabled() {
        return this.currentPatternData && this.currentPatternData.has_recommendations;
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.patternHelper === 'undefined') {
        window.patternHelper = new PatternHelper();
    }
});

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PatternHelper;
}