// 🚀 前后端分离版本 - learning_logic.js (重构后)
// 从2420行缩减到约300行，业务逻辑移至后端API

// 🔧 保留的前端状态变量
let dailyWords = [];
let filteredWords = []; // 过滤后的单词列表
let currentWordIndex = 0; // 当前单词索引
let score = 0; // 本地积分计数器（用于显示实时变化）
let userPoints = 0; // 数据库中的实际积分
let userVouchers = 0; // 数据库中的实际购物券
let previousWordIndex = -1;

// 🎯 5星单词学习控制变量
window.includeFiveStarsInLearning = false; // 默认不包含5星单词在学习循环中

// 🔧 测试函数已移除
let starFilters = [1, 2, 3, 4, 5]; // 星级筛选：数组包含要显示的星级，默认显示全部

// 获取学习模式 - 从HTML模板中获取，避免重复声明
// const learningMode 已在HTML模板中声明

// 🔧 保留的音频设置（前端UI逻辑）
const positiveSound = new Audio('/static/audio/positive.wav');
const negativeSound = new Audio('/static/audio/negative.wav');
positiveSound.volume = 0.3;
negativeSound.volume = 0.3;

// 🔧 页面初始化（保留前端交互）
$(document).ready(function() {
    $('#score').click(generateReport);
    $('#new-words-checkbox').change(filterWords);
    $('#review-words-checkbox').change(filterWords);
    $('#low-correct-rate-btn').click(fetchLowCorrectRateWords);
    
    // 🚀 绑定右侧按钮事件
    $('#hint-button').click(getHint);
    $('#memory-help-button').click(getMemoryHelp);
    
    // 🎯 初始化：使用新的前后端分离API
    loadUserPoints();
    fetchDailyPlan();
});

// 🔧 保留的前端UI更新函数
function updateScore() {
    document.getElementById('score').textContent = `🪙 ${score}`;
}

// 🚀 使用后端API - 替代原来的 updateUserPoints() 业务逻辑
async function updateUserPoints(pointsChange, reason = '学习获得') {
    try {
        const response = await fetch('/api/update_user_points', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                points_change: pointsChange,
                reason: reason
            })
        });

        const data = await response.json();
        console.log('🔍 积分更新API响应:', data); // 调试日志

        if (data.success) {
            const oldPoints = data.data.old_points;
            const newPoints = data.data.new_points;

            userPoints = newPoints;
            score = userPoints; // 同步本地计数器
            updateScore();
            console.log(`✅ 积分更新: ${oldPoints} -> ${newPoints} (${reason})`);

            // 🔧 保留前端动画效果
            showPointsAnimation(pointsChange, reason);
        } else {
            console.error('❌ 更新积分失败:', data.error || data.message);
        }
    } catch (error) {
        console.error('❌ 更新积分异常:', error);
    }
}

// 🚀 使用后端API - 替代原来的 updateUserVouchers() 业务逻辑
async function updateUserVouchers(vouchersChange, reason = '学习获得') {
    try {
        const response = await fetch('/api/voucher/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                vouchers_change: vouchersChange,
                reason: reason
            })
        });

        const data = await response.json();
        if (data.success) {
            userVouchers = data.new_vouchers;
            // 🔧 保留前端UI更新
            updateVoucherIndicator();
            console.log(`✅ 购物券更新: ${data.old_vouchers || 0} -> ${data.new_vouchers} (${reason})`);

            // 🔧 保留前端动画效果
            if (vouchersChange > 0) {
                showVoucherAnimation(vouchersChange, reason);
            }
        } else {
            console.error('❌ 更新购物券失败:', data.message || data.error);
        }
    } catch (error) {
        console.error('❌ 更新购物券异常:', error);
    }
}

// 🚀 使用后端API - 替代原来的 loadUserPoints() 业务逻辑
async function loadUserPoints() {
    try {
        const response = await fetch('/api/user_status');
        const data = await response.json();

        if (data && data.success) {
            // 🎯 修复：正确解析嵌套的API响应结构
            const responseData = data.data || data;
            const userInfo = responseData.user || responseData;

            userPoints = userInfo.points || 0;
            userVouchers = userInfo.vouchers || 0;
            score = userPoints; // 同步本地积分计数器

            updateScore();
            updateVoucherIndicator();
            console.log(`✅ 加载用户数据: 积分=${userPoints}, 购物券=${userVouchers}`);
            console.log('🔍 API响应数据结构:', data);
        } else {
            console.error('❌ 加载用户积分失败:', data.error || data.message);
        }
    } catch (error) {
        console.error('❌ 加载用户积分异常:', error);
    }
}

// 🚀 使用后端API - 替代原来的 fetchDailyPlan() 业务逻辑
async function fetchDailyPlan() {
    console.log('🚀 开始获取每日计划...');

    try {
        // 获取URL中的日期参数
        const urlParams = new URLSearchParams(window.location.search);
        const dateParam = urlParams.get('date');

        // 🚀 使用新的后端API
        const apiUrl = dateParam ? `/api/daily_learning_plan?date=${dateParam}` : '/api/daily_learning_plan';
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('🔍 API响应状态:', response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ API响应错误:', response.status, errorText);
            document.getElementById('hint').textContent = `❌ API错误 ${response.status}: ${errorText}`;
            return;
        }

        const data = await response.json();
        console.log('🔍 API响应数据:', data);

        if (data.success) {
            // 修正：API直接返回daily_words数组
            dailyWords = data.daily_words || [];
            console.log(`📝 单词总数: ${dailyWords.length}`, data);

            // 💡 Pattern功能：触发学习会话开始事件
            try {
                document.dispatchEvent(new CustomEvent('learningSessionStarted'));
                console.log('💡 学习会话开始事件已触发');
            } catch (error) {
                console.log('💡 学习会话开始事件触发失败:', error);
            }

            // 🔧 保留前端UI逻辑：洗牌和显示
            shuffleDailyWords();
            filterWords();
            displayStudyPlan();

            // 🎯 修复：检查是否已显示学习完成页面，如果是则不调用fetchWord
            const completionPage = document.getElementById('learning-completed');
            if (!completionPage) {
                fetchWord();
            } else {
                console.log('⚠️ 学习完成页面已显示，跳过fetchWord调用');
            }
        } else {
            console.error('❌ 获取每日计划失败:', data.error || data.message);
            document.getElementById('hint').textContent = `❌ ${data.error || data.message || '加载失败，请刷新页面重试'}`;
        }
    } catch (error) {
        console.error('❌ 获取每日计划异常:', error);
        document.getElementById('hint').textContent = '❌ 网络错误，请检查连接后重试';
    }
}

// 🔧 保留前端逻辑 - 单词洗牌
function shuffleDailyWords() {
    for (let i = dailyWords.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [dailyWords[i], dailyWords[j]] = [dailyWords[j], dailyWords[i]];
    }
}

// 🔧 简化的前端过滤逻辑 - 直接使用前端过滤
function filterWords() {
    const newWordsCheckbox = document.getElementById('new-words-checkbox').checked;
    const reviewWordsCheckbox = document.getElementById('review-words-checkbox').checked;

    console.log(`🔍 过滤条件: 新词=${newWordsCheckbox}, 复习词=${reviewWordsCheckbox}, 星级=${starFilters.join(',')}`);
    console.log('🔍 原始数据样本:', dailyWords.slice(0, 2));

    // 直接使用前端过滤逻辑
    filteredWords = dailyWords.filter(word => {
        const wordStarLevel = word.star_level || word.proficiency || 0;

        // 🎯 核心逻辑：5星单词完全排除，除非明确选择复习5星单词
        if (wordStarLevel === 5) {
            if (!window.includeFiveStarsInLearning) {
                console.log(`🎯 排除5星单词: ${word.word} (${word.answer}) - 当日已完成，未选择复习`);
                return false;
            }
            // 如果选择了复习5星单词，则继续后续过滤逻辑
            console.log(`🎯 包含5星单词: ${word.word} (${word.answer}) - 用户选择复习5星单词`);
        }

        // 按新词/复习词筛选
        let passTypeFilter = false;
        if (newWordsCheckbox && (word.item_type === 'new' || word.is_new)) passTypeFilter = true;
        if (reviewWordsCheckbox && (word.item_type === 'review' || !word.is_new)) passTypeFilter = true;
        // 如果两个都没选中，不显示任何单词

        if (!passTypeFilter) return false;

        // 按星级筛选（只有在包含5星单词的情况下才应用星级过滤）
        if (starFilters.length > 0 && !starFilters.includes(wordStarLevel)) return false;

        return true;
    });

    console.log(`✅ 过滤结果: ${filteredWords.length}个单词`);
    console.log('🔍 过滤后的单词样本:', filteredWords.slice(0, 2));

    // 🎯 检测是否所有单词都达到5星且未选择复习
    const allFiveStars = dailyWords.every(word => (word.star_level || word.proficiency || 0) === 5);
    if (allFiveStars && !window.includeFiveStarsInLearning && filteredWords.length === 0) {
        console.log('🎉 所有单词都达到5星，当日学习完成！');
        showLearningCompleted();
        return;
    }

    // 🔧 重置单词索引
    currentWordIndex = 0;

    // 🔧 保留前端显示逻辑
    displayStudyPlan();
    
    // 🔧 重新获取并显示第一个单词
    fetchWord();
}

// 🎉 显示学习完成状态
function showLearningCompleted() {
    console.log('🎉 显示学习完成状态');

    // 🎯 修复：防止重复显示
    const existingCompletion = document.getElementById('learning-completed');
    if (existingCompletion) {
        console.log('⚠️ 学习完成页面已存在，跳过重复显示');
        return;
    }

    // 🎯 修复：确保DOM已加载，使用延迟执行
    function doShowCompletion() {
        const learningArea = document.querySelector('.right-column');
        if (!learningArea) {
            console.error('❌ 找不到学习区域元素 (.right-column)');
            // 如果还没找到，再等一下
            setTimeout(doShowCompletion, 100);
            return;
        }

        console.log('✅ 找到学习区域元素:', learningArea);

        const fiveStarCount = dailyWords.filter(word => (word.star_level || word.proficiency || 0) === 5).length;

        // 🎯 修复：直接替换学习区域内容，而不是隐藏后添加新元素
        learningArea.innerHTML = `
            <div id="learning-completed" style="
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            ">
                <h2 style="margin: 0 0 15px 0; font-size: 28px;">🎉 恭喜！当日学习完成！</h2>
                <p style="margin: 10px 0; font-size: 18px;">所有 ${fiveStarCount} 个单词都已达到5⭐</p>
                <p style="margin: 10px 0; font-size: 16px; opacity: 0.9;">根据数据模型：5星 = "当日完成"</p>
                <div style="margin-top: 20px;">
                    <button onclick="location.reload()" style="
                        background: white;
                        color: #4CAF50;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 16px;
                        font-weight: bold;
                        cursor: pointer;
                        margin-right: 10px;
                    ">刷新页面</button>
                    <button onclick="startFiveStarReview();" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: 2px solid white;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 16px;
                        font-weight: bold;
                        cursor: pointer;
                    ">复习5⭐单词</button>
                </div>
            </div>
        `;
    }

    // 🎯 修复：立即尝试显示，如果失败则延迟重试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', doShowCompletion);
    } else {
        doShowCompletion();
    }
}

// 🎯 开始5星复习功能
function startFiveStarReview() {
    console.log('🌟 开始5星复习模式');

    // 使用URL参数传递复习状态
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('review5star', 'true');

    // 重新加载页面以恢复原始学习界面
    window.location.href = currentUrl.toString();
}

// 🔧 删除了fallbackFilterWords函数，直接使用filterWords函数

// 🚀 使用后端API - 替代原来的 updateStarFilters() 业务逻辑
function updateStarFilters() {
    // 获取所有选中的星级复选框
    const checkboxes = document.querySelectorAll('.star-checkbox');
    starFilters = [];

    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            starFilters.push(parseInt(checkbox.value));
        }
    });

    console.log(`⭐ 星级筛选更新: ${starFilters.length > 0 ? starFilters.join(',') + '⭐' : '无选择'}`);

    // 🚀 重新获取过滤结果
    filterWords();

    // 重置当前单词索引，确保从筛选后的单词开始
    currentWordIndex = 0;

    // 显示筛选结果
    const filteredCount = filteredWords.length;
    const starText = starFilters.length > 0 ? `${starFilters.join(',')}⭐` : '无星级选择';
    console.log(`✅ 筛选结果: ${starText} - ${filteredCount}个单词`);

    // 如果有筛选结果，显示第一个单词
    if (filteredCount > 0) {
        // 重置提示级别，因为这是新的筛选会话
        resetHintLevel();
        // 清除AI帮帮记内容，防止泄密
        clearMemoryHelp();
        fetchWord();
    } else {
        // 没有符合条件的单词 - 🔧 保留前端UI逻辑
        if (starFilters.length === 0) {
            document.getElementById('word').textContent = `请选择要学习的星级`;
            document.getElementById('hint').textContent = `勾选上方的星级复选框来选择要学习的单词`;
        } else {
            document.getElementById('word').textContent = `没有${starText}的单词`;
            document.getElementById('hint').textContent = `请选择其他星级组合`;
        }
        document.getElementById('word-image').style.display = 'none';
        document.getElementById('traditional-mode').style.display = 'none';
        document.getElementById('spelling-mode').style.display = 'none';
    }
}

// 🔧 保留前端UI逻辑函数（这些不需要移到后端）
function showPointsAnimation(pointsChange, reason) {
    // 积分变化动画效果 - 纯前端UI逻辑
    console.log(`🎬 显示积分动画: ${pointsChange > 0 ? '+' : ''}${pointsChange} (${reason})`);

    // 创建动画元素
    const animationElement = document.createElement('div');
    animationElement.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24px;
        font-weight: bold;
        color: ${pointsChange > 0 ? '#4CAF50' : '#f44336'};
        background: rgba(255, 255, 255, 0.9);
        padding: 10px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        pointer-events: none;
        animation: pointsAnimation 2s ease-out forwards;
    `;

    animationElement.textContent = `${pointsChange > 0 ? '+' : ''}${pointsChange} 积分`;

    // 添加CSS动画
    if (!document.getElementById('points-animation-style')) {
        const style = document.createElement('style');
        style.id = 'points-animation-style';
        style.textContent = `
            @keyframes pointsAnimation {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
                100% { opacity: 0; transform: translate(-50%, -80%) scale(1); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(animationElement);

    // 2秒后移除元素
    setTimeout(() => {
        if (animationElement.parentNode) {
            animationElement.parentNode.removeChild(animationElement);
        }
    }, 2000);
}

function showVoucherAnimation(vouchersChange, reason) {
    // 购物券变化动画效果 - 纯前端UI逻辑
    console.log(`🎬 显示购物券动画: +${vouchersChange} (${reason})`);

    // 创建购物券动画元素
    const animationElement = document.createElement('div');
    animationElement.style.cssText = `
        position: fixed;
        top: 25%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 28px;
        font-weight: bold;
        color: #007bff;
        background: rgba(255, 255, 255, 0.95);
        padding: 20px 30px;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,123,255,0.3);
        z-index: 10001;
        pointer-events: none;
        animation: voucherAnimation 3s ease-out forwards;
        border: 2px solid #007bff;
        text-align: center;
    `;

    animationElement.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center;">
            🎫 <span style="margin-left: 10px;">+${vouchersChange} 张购物券</span>
        </div>
        <div style="font-size: 16px; color: #666; margin-top: 8px;">
            ${reason}
        </div>
    `;

    // 添加CSS动画
    if (!document.getElementById('voucher-animation-style')) {
        const style = document.createElement('style');
        style.id = 'voucher-animation-style';
        style.textContent = `
            @keyframes voucherAnimation {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -70%) scale(0.8); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(animationElement);

    // 3秒后移除元素
    setTimeout(() => {
        if (animationElement.parentNode) {
            animationElement.parentNode.removeChild(animationElement);
        }
    }, 3000);
}

function showAnswerAnimation(isCorrect, correctAnswer, consecutiveCount = 0, starChange = null) {
    // 简化的答案动画效果 - 只显示星级变化和连击数
    console.log(`🎬 显示答案动画: ${isCorrect ? '✅ 正确' : '❌ 错误'}, 连击数: ${consecutiveCount}`);

    if (!isCorrect) {
        // 答错时显示简单的错误提示
        const errorElement = document.createElement('div');
        errorElement.style.cssText = `
            position: fixed;
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 36px;
            font-weight: bold;
            color: #f44336;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            z-index: 10000;
            pointer-events: none;
            animation: answerResult 1.5s ease-out forwards;
            text-align: center;
        `;
        errorElement.textContent = '❌ 错误';
        document.body.appendChild(errorElement);

        setTimeout(() => {
            if (errorElement.parentNode) errorElement.parentNode.removeChild(errorElement);
        }, 1500);
        return;
    }

    // 答对时显示绿色背景闪烁
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(76, 175, 80, 0.15);
        z-index: 9999;
        pointer-events: none;
        animation: answerFlash 0.8s ease-out;
    `;

    // 创建连击显示元素
    const streakElement = document.createElement('div');
    streakElement.style.cssText = `
        position: fixed;
        top: 30%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 42px;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        z-index: 10000;
        pointer-events: none;
        animation: streakAnimation 2s ease-out forwards;
        text-align: center;
    `;

    // 根据连击数显示不同的内容和颜色
    if (consecutiveCount >= 15) {
        streakElement.innerHTML = `
            <div style="color: #FF1744; font-size: 48px;">🔥 LEGENDARY!</div>
            <div style="color: #FF6B35; font-size: 28px; margin-top: 8px;">
                ${consecutiveCount} 连击！
            </div>
        `;
        playStreakSound('legendary');
    } else if (consecutiveCount >= 10) {
        streakElement.innerHTML = `
            <div style="color: #FF6B35; font-size: 44px;">🔥 EXCELLENT!</div>
            <div style="color: #FF8A50; font-size: 26px; margin-top: 8px;">
                ${consecutiveCount} 连击！
            </div>
        `;
        playStreakSound('excellent');
    } else if (consecutiveCount >= 7) {
        streakElement.innerHTML = `
            <div style="color: #FF9800; font-size: 40px;">⚡ GREAT!</div>
            <div style="color: #FFA726; font-size: 24px; margin-top: 8px;">
                ${consecutiveCount} 连击
            </div>
        `;
        playStreakSound('great');
    } else if (consecutiveCount >= 5) {
        streakElement.innerHTML = `
            <div style="color: #4CAF50; font-size: 36px;">💫 GOOD!</div>
            <div style="color: #66BB6A; font-size: 22px; margin-top: 8px;">
                ${consecutiveCount} 连击
            </div>
        `;
        playStreakSound('good');
    } else if (consecutiveCount >= 3) {
        streakElement.innerHTML = `
            <div style="color: #2196F3; font-size: 32px;">⭐ 连击 ${consecutiveCount}</div>
        `;
        playStreakSound('streak');
    } else if (consecutiveCount >= 2) {
        streakElement.innerHTML = `
            <div style="color: #4CAF50; font-size: 28px;">✨ 连击 ${consecutiveCount}</div>
        `;
        playStreakSound('double');
    } else {
        // 单次正确，不显示连击
        streakElement.innerHTML = `
            <div style="color: #4CAF50; font-size: 24px;">⭐ +1</div>
        `;
    }

    // 添加CSS动画样式
    if (!document.getElementById('streak-animation-style')) {
        const style = document.createElement('style');
        style.id = 'streak-animation-style';
        style.textContent = `
            @keyframes answerFlash {
                0% { opacity: 0; }
                50% { opacity: 1; }
                100% { opacity: 0; }
            }
            @keyframes streakAnimation {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -70%) scale(0.8); }
            }
            @keyframes answerResult {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.3); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(overlay);
    document.body.appendChild(streakElement);

    // 2秒后移除元素
    setTimeout(() => {
        if (overlay.parentNode) overlay.parentNode.removeChild(overlay);
        if (streakElement.parentNode) streakElement.parentNode.removeChild(streakElement);
    }, 2000);
}

// 播放连击声音效果
function playStreakSound(type) {
    console.log(`🎵 playStreakSound 被调用，类型: ${type}`);

    // 根据连击类型播放不同的声音
    const soundMap = {
        'double': 'double_kill.mp3',
        'streak': 'triple_kill.mp3',
        'good': 'multi_kill.mp3',
        'great': 'mega_kill.mp3',
        'excellent': 'ultra_kill.mp3',
        'legendary': 'monster_kill.mp3'
    };

    const soundFile = soundMap[type];
    console.log(`🎵 映射的音频文件: ${soundFile}`);

    if (soundFile) {
        const audioUrl = `/static/audio/streak/${soundFile}`;
        console.log(`🔊 准备播放连击声音: ${audioUrl}`);

        // 创建音频对象并播放
        const audio = new Audio(audioUrl);
        audio.volume = 0.7; // 设置音量为70%

        // 添加事件监听器
        audio.onloadstart = () => console.log(`🎵 开始加载音频: ${soundFile}`);
        audio.oncanplay = () => console.log(`🎵 音频可以播放: ${soundFile}`);
        audio.onplay = () => console.log(`🎵 音频开始播放: ${soundFile}`);
        audio.onerror = (e) => console.error(`❌ 音频加载错误: ${soundFile}`, e);

        // 播放音频，如果失败则回退到默认音效
        audio.play().then(() => {
            console.log(`✅ 连击音效播放成功: ${soundFile}`);
        }).catch(error => {
            console.error(`⚠️ 连击音效播放失败，回退到默认音效: ${error.message}`);
            // 连击音效播放失败时，播放默认的正确音效
            playAnswerSound(true);
        });
    } else {
        // 如果没有对应的音效文件，播放默认音效
        console.log(`🔊 没有找到对应音效，播放默认音效: ${type}`);
        playAnswerSound(true);
    }
}

function updateVoucherIndicator() {
    // 购物券显示更新 - 纯前端UI逻辑
    const voucherElements = document.querySelectorAll('#voucher-count');
    voucherElements.forEach(element => {
        element.textContent = userVouchers;
    });
}

// 🔧 从 learning.html 中移过来的必需函数（保留前端显示逻辑）
// filteredWords 已在文件顶部声明

function displayStudyPlan() {
    console.log('📊 displayStudyPlan() 开始执行...');
    const dailyPlan = document.getElementById('daily-plan');
    let starCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
    let overdueCount = 0;

    console.log('📊 当前filteredWords数量:', filteredWords.length);
    console.log('📊 当前dailyWords数量:', dailyWords.length);

    // 🎯 统计星级分布时，只根据新/复筛选，不考虑5星选择
    const newWordsCheckbox = document.getElementById('new-words-checkbox').checked;
    const reviewWordsCheckbox = document.getElementById('review-words-checkbox').checked;
    
    // 用于统计的单词列表（包含所有5星单词）
    const wordsForStats = dailyWords.filter(word => {
        // 按新词/复习词筛选
        let passTypeFilter = false;
        if (newWordsCheckbox && (word.item_type === 'new' || word.is_new)) passTypeFilter = true;
        if (reviewWordsCheckbox && (word.item_type === 'review' || !word.is_new)) passTypeFilter = true;
        
        if (!passTypeFilter) return false;
        
        // 星级筛选（统计时始终包含所有星级）
        if (starFilters.length > 0 && !starFilters.includes(word.star_level || word.proficiency || 0)) return false;
        
        return true;
    });
    
    wordsForStats.forEach((word, index) => {
        const starLevel = word.star_level || word.proficiency || 0;
        starCounts[starLevel]++;
        if (word.is_overdue) {
            overdueCount++;
        }

        // 只打印前3个单词的详细信息
        if (index < 3) {
            console.log(`📊 单词${index + 1}: ${word.word} (${word.answer}) - 星级: ${starLevel}`);
        }
    });

    console.log('📊 实际星级分布:', starCounts);

    // 🎯 显示统计的单词数（包含5星）和实际学习的单词数
    const totalWords = wordsForStats.length;
    const learningWords = filteredWords.length;

    dailyPlan.innerHTML = `<h2>今日计划</h2>
        <p>总单词数: ${totalWords}</p>
        <div class="star-filter-container">
            <div class="star-filter-item">
                <label><input type="checkbox" class="star-checkbox" value="5" ${starFilters.includes(5) ? 'checked' : ''}> 5⭐: ${starCounts[5]}</label>
            </div>
            <div class="star-filter-item">
                <label><input type="checkbox" class="star-checkbox" value="4" ${starFilters.includes(4) ? 'checked' : ''}> 4⭐: ${starCounts[4]}</label>
            </div>
            <div class="star-filter-item">
                <label><input type="checkbox" class="star-checkbox" value="3" ${starFilters.includes(3) ? 'checked' : ''}> 3⭐: ${starCounts[3]}</label>
            </div>
            <div class="star-filter-item">
                <label><input type="checkbox" class="star-checkbox" value="2" ${starFilters.includes(2) ? 'checked' : ''}> 2⭐: ${starCounts[2]}</label>
            </div>
            <div class="star-filter-item">
                <label><input type="checkbox" class="star-checkbox" value="1" ${starFilters.includes(1) ? 'checked' : ''}> 1⭐: ${starCounts[1]}</label>
            </div>
        </div>`;

    if (overdueCount > 0) {
        dailyPlan.innerHTML += `<p>顺延单词: ${overdueCount} <span style="font-size:12px;color:#666;">(未按时学习的单词)</span></p>`;
    }

    // 显示5⭐完成状态和可选复习提示
    const fiveStarWords = dailyWords.filter(word => (word.star_level || word.proficiency || 0) === 5);

    if (fiveStarWords.length > 0) {
        const completedCount = fiveStarWords.length;
        const currentlyIncluding = window.includeFiveStarsInLearning || false;

        console.log('📊 5星单词状态:', {
            fiveStarCount: completedCount,
            currentlyIncluding: currentlyIncluding,
            globalVariable: window.includeFiveStarsInLearning
        });

        dailyPlan.innerHTML += `<div style="background: #e8f5e8; padding: 10px; border-radius: 8px; margin-top: 10px; color: #2e7d32;">
            🎉 已完成 ${completedCount} 个单词达到5⭐！
            <br><small style="color: #666; font-size: 12px;">📋 数据模型：5星 = "当日完成" → 可选择复习</small>
            <br><br>
            <div style="margin-bottom: 10px;">
                <label style="font-size: 14px; color: #2e7d32; cursor: pointer; display: block;" onclick="console.log('🖱️ 标签被点击');">
                    <input type="checkbox" id="include-five-stars" ${currentlyIncluding ? 'checked' : ''} style="margin-right: 5px;" onclick="console.log('🖱️ 复选框被点击', this.checked);">
                    选择复习5⭐单词 (${completedCount}个)
                </label>
            </div>

        </div>`;

        // 添加5⭐复习选项的事件监听器（不与星级分布复选框联动）
        setTimeout(() => {
            const fiveStarCheckbox = document.getElementById('include-five-stars');
            if (fiveStarCheckbox) {
                // 移除之前的事件监听器（如果有）
                fiveStarCheckbox.removeEventListener('change', window.fiveStarCheckboxHandler);

                // 创建新的事件处理器
                window.fiveStarCheckboxHandler = function() {
                    console.log(`🌟 5⭐复习模式: ${this.checked ? '开启' : '关闭'}`);

                    // 设置全局变量控制5星单词是否参与学习循环
                    window.includeFiveStarsInLearning = this.checked;

                    // 🎯 如果选中了复习5星单词，需要重新加载页面以恢复学习界面
                    if (this.checked) {
                        // 使用URL参数传递状态，与右侧按钮保持一致
                        const currentUrl = new URL(window.location);
                        currentUrl.searchParams.set('review5star', 'true');
                        window.location.href = currentUrl.toString();
                    } else {
                        // 取消复习5星单词，移除URL参数并重新加载
                        const currentUrl = new URL(window.location);
                        currentUrl.searchParams.delete('review5star');
                        window.location.href = currentUrl.toString();
                    }
                };

                // 绑定新的事件监听器
                fiveStarCheckbox.addEventListener('change', window.fiveStarCheckboxHandler);

                console.log('✅ 5星复习复选框事件绑定完成');
            } else {
                console.log('❌ 找不到5星复习复选框');
            }
        }, 100);
    }

    // 获取当前页面的日期参数
    const urlParams = new URLSearchParams(window.location.search);
    const dateParam = urlParams.get('date');
    const dailyWordsUrl = dateParam ? `/daily_words?date=${dateParam}` : '/daily_words';

    dailyPlan.innerHTML += `<p><a href="${dailyWordsUrl}" target="_blank" style="color:blue;text-decoration:underline;">查看详细计划</a></p>`;

    // 添加星级筛选复选框的事件监听器
    setTimeout(() => {
        const starCheckboxes = document.querySelectorAll('.star-checkbox');
        starCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateStarFilters);
        });
    }, 100);
}

// 🔧 保留其他前端需要的函数引用
function generateReport() {
    // 保留前端报告生成逻辑
    console.log('📊 生成前端学习报告...');
}

function fetchLowCorrectRateWords() {
    // 保留前端错误率统计逻辑
    console.log('📊 获取低正确率单词...');
}

// 提示级别管理
let currentHintLevel = 0;
const maxHintLevel = 5;

function resetHintLevel() {
    // 重置提示级别
    currentHintLevel = 0;
    console.log('🔄 重置提示级别');
}

// 清除AI帮帮记内容 - 防止泄密
function clearMemoryHelp() {
    const memoryHelpElement = document.getElementById('memory-help');
    if (memoryHelpElement) {
        memoryHelpElement.innerHTML = '';
        console.log('🧹 已清除AI帮帮记内容');
    }
}

// 🔧 前端UI逻辑：获取并显示当前单词
function fetchWord() {
    console.log('🔄 获取下一个单词');

    // 🔧 修复问题2：每次获取新单词时重置提示级别
    resetHintLevel();

    // 🔧 修复AI帮帮记泄密问题：每次切换单词时清除AI帮帮记内容
    clearMemoryHelp();

    if (!filteredWords || filteredWords.length === 0) {
        console.log('❌ 没有可用的单词');

        // 🎯 检测是否所有单词都达到5星且未选择复习
        const allFiveStars = dailyWords.every(word => (word.star_level || word.proficiency || 0) === 5);
        if (allFiveStars && !window.includeFiveStarsInLearning) {
            console.log('🎉 所有单词都达到5星，当日学习完成！');
            showLearningCompleted();
            return;
        }

        document.getElementById('word').textContent = '没有可学习的单词';
        document.getElementById('hint').textContent = '请调整筛选条件';
        return;
    }

    // 🔧 新增：处理被标记移除的5星单词
    while (filteredWords.length > 0) {
        // 确保currentWordIndex在有效范围内
        if (typeof currentWordIndex === 'undefined' || currentWordIndex >= filteredWords.length) {
            currentWordIndex = 0;
        }
        
        const currentWord = filteredWords[currentWordIndex];
        
        // 检查当前单词是否被标记移除
        if (currentWord && currentWord._should_remove) {
            console.log(`🎯 移除标记的5星单词: ${currentWord.word}`);
            filteredWords.splice(currentWordIndex, 1);
            
            // 调整索引，避免跳过下一个单词
            if (currentWordIndex >= filteredWords.length) {
                currentWordIndex = 0;
            }
            
            console.log(`🔄 移除5星单词后，剩余单词数: ${filteredWords.length}`);
            
            // 如果还有单词，继续循环检查下一个
            if (filteredWords.length === 0) {
                break;
            }
            continue;
        }
        
        // 找到有效单词，跳出循环
        break;
    }
    
    // 重新检查是否还有可用单词
    if (!filteredWords || filteredWords.length === 0) {
        console.log('❌ 处理5星单词后没有可用的单词');
        
        // 🎯 检测是否所有单词都达到5星且未选择复习
        const allFiveStars = dailyWords.every(word => (word.star_level || word.proficiency || 0) === 5);
        if (allFiveStars && !window.includeFiveStarsInLearning) {
            console.log('🎉 所有单词都达到5星，当日学习完成！');
            showLearningCompleted();
            return;
        }

        document.getElementById('word').textContent = '没有可学习的单词';
        document.getElementById('hint').textContent = '请调整筛选条件';
        return;
    }

    const currentWord = filteredWords[currentWordIndex];
    console.log(`📖 显示单词 ${currentWordIndex + 1}/${filteredWords.length}:`, currentWord);
    console.log('🔍 单词数据结构:', {
        word: currentWord.word,           // 中文含义
        answer: currentWord.answer,       // 英文单词
        id: currentWord.id,              // 单词ID
        item_type: currentWord.item_type  // 类型：new/review
    });

    // 🔧 拼写模式：显示中文，让用户输入英文
    const chineseText = currentWord.word || '未知单词';        // 中文含义
    const englishText = currentWord.answer || '';             // 英文单词（正确答案）

    document.getElementById('word').textContent = chineseText;
    document.getElementById('hint').textContent = '请输入对应的英文单词';

    console.log('✅ 单词显示更新:', chineseText, '(正确答案:', englishText, ')');

    // 更新图片（如果有）
    const wordImage = document.getElementById('word-image');
    if (wordImage) {
        // 使用图片API加载单词图片
        loadWordImageFromAPI(englishText, wordImage);
    }

    // 🔊 自动播放单词发音
    setTimeout(() => {
        playCurrentWordAudio();
    }, 500); // 延迟500ms播放，确保页面元素已更新

    // 更新进度显示
    updateProgressDisplay();
    
    // 🚀 新增：根据learning_requirement选择学习模式
    selectLearningModeByRequirement(currentWord);
    
    // 🧠 特征学习功能：更新用户输入框的正确答案属性
    try {
        const userInputField = document.getElementById('user-input');
        if (userInputField) {
            userInputField.setAttribute('data-correct-answer', englishText);
            console.log('🧠 特征学习：已更新用户输入框的正确答案属性:', englishText);
        }
    } catch (error) {
        console.log('🧠 特征学习：更新输入框属性失败:', error);
    }

    // 💡 Pattern功能：触发单词切换事件
    try {
        document.dispatchEvent(new CustomEvent('wordChanged', {
            detail: {
                id: currentWord.id,
                english_word: currentWord.answer,
                chinese_meaning: currentWord.word,
                item_type: currentWord.item_type,
                star_level: currentWord.star_level
            }
        }));
        console.log('💡 Pattern事件已触发:', currentWord.answer);
    } catch (error) {
        console.log('💡 Pattern事件触发失败:', error);
    }
}

/**
 * 根据单词的learning_requirement选择学习模式
 * @param {Object} wordData - 当前单词数据
 */
function selectLearningModeByRequirement(wordData) {
    const learningRequirement = wordData.learning_requirement || 'spelling';
    const starLevel = wordData.star_level || 3;
    
    console.log(`🎯 选择学习模式: learning_requirement=${learningRequirement}, star_level=${starLevel}`);
    console.log('🔍 完整单词数据:', JSON.stringify(wordData, null, 2));
    console.log('🔍 star_level原始值:', wordData.star_level, '类型:', typeof wordData.star_level);
    
    // 隐藏所有学习模式
    document.getElementById('traditional-mode').style.display = 'none';
    document.getElementById('spelling-mode').style.display = 'none';
    document.getElementById('recognition-mode').style.display = 'none';
    
    if (learningRequirement === 'recognition') {
        // Recognition模式：显示选择题界面
        showRecognitionMode(wordData);
    } else {
        // Spelling模式：根据星级选择传统模式或拼写模式
        if (starLevel >= 3) {
            // 传统模式（星级>=3）
            document.getElementById('traditional-mode').style.display = 'block';
            document.getElementById('user-input').focus();
            console.log('✅ 显示传统模式（星级>=3）');
        } else {
            // 拼写模式（星级<3）
            document.getElementById('spelling-mode').style.display = 'block';
            document.getElementById('spelling-input').focus();
            console.log('✅ 显示拼写模式（星级<3）');
            
            // 🚀 为拼写模式设置逐字母监听
            setupLetterByLetterSpelling(wordData);
        }
    }
}

// 🔧 前端UI逻辑：更新进度显示
function updateProgressDisplay() {
    if (filteredWords && filteredWords.length > 0) {
        const progress = `${currentWordIndex + 1}/${filteredWords.length}`;
        console.log(`📊 学习进度: ${progress}`);

        // 如果页面有进度显示元素，更新它
        const progressElement = document.getElementById('progress');
        if (progressElement) {
            progressElement.textContent = progress;
        }
    }
}

// 🔄 实时更新星级分布
async function updateStarDistribution() {
    try {
        console.log('🔄 更新星级分布...');

        // 重新获取每日计划数据
        const response = await fetch('/api/daily_learning_plan');
        const result = await response.json();

        if (result.success) {
            console.log('📊 获取到最新数据:', result);

            // 🔧 关键修复：更新本地filteredWords数组
            if (result.words && Array.isArray(result.words)) {
                // 更新全局单词数据
                dailyWords = result.words;

                // 重新应用过滤器
                filterWords();

                console.log('📊 更新后的filteredWords:', filteredWords.length, '个单词');
                console.log('📊 当前单词星级分布:', getLocalStarDistribution());
            }

            // 更新全局数据
            if (window.dailyPlanData) {
                window.dailyPlanData = result;
            }

            // 重新显示学习计划（包含星级分布）
            displayStudyPlan();

            console.log('✅ 星级分布更新成功');
        } else {
            console.log('⚠️ 星级分布更新失败:', result.message);
        }
    } catch (error) {
        console.error('❌ 更新星级分布异常:', error);
    }
}

// 🔧 辅助函数：获取本地星级分布统计
function getLocalStarDistribution() {
    const starCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
    filteredWords.forEach(word => {
        const starLevel = word.star_level || word.proficiency || 0;
        if (starLevel >= 1 && starLevel <= 5) {
            starCounts[starLevel]++;
        }
    });
    return starCounts;
}

// 🔧 强制更新星级分布UI
function forceUpdateStarDistribution() {
    console.log('🔄 强制更新星级分布UI...');

    // 获取当前星级分布
    const starCounts = getLocalStarDistribution();
    console.log('📊 当前星级分布:', starCounts);

    // 更新每个星级的显示数字
    const starElements = {
        5: document.querySelector('input[value="5"]')?.parentElement,
        4: document.querySelector('input[value="4"]')?.parentElement,
        3: document.querySelector('input[value="3"]')?.parentElement,
        2: document.querySelector('input[value="2"]')?.parentElement,
        1: document.querySelector('input[value="1"]')?.parentElement
    };

    Object.keys(starElements).forEach(star => {
        const element = starElements[star];
        if (element) {
            const label = element.querySelector('label');
            if (label) {
                const checkbox = label.querySelector('input');
                const isChecked = checkbox ? checkbox.checked : false;
                label.innerHTML = `<input type="checkbox" class="star-checkbox" value="${star}" ${isChecked ? 'checked' : ''}> ${star}⭐: ${starCounts[star]}`;

                // 重新绑定事件监听器
                const newCheckbox = label.querySelector('input');
                if (newCheckbox) {
                    newCheckbox.addEventListener('change', function() {
                        updateStarFilters();
                    });
                }
            }
        }
    });

    console.log('✅ 星级分布UI更新完成');
}

// 🚀 添加缺失的关键功能函数

/**
 * 设置逐字母拼写功能
 * @param {Object} wordData - 当前单词数据
 */
function setupLetterByLetterSpelling(wordData) {
    const correctAnswer = wordData.answer || '';
    const spellingInput = document.getElementById('spelling-input');
    const letterFeedback = document.getElementById('letter-feedback');
    const progressFill = document.getElementById('progress-fill');
    const lengthHint = document.getElementById('length-hint');
    
    console.log('🔤 设置逐字母拼写功能:', {
        word: wordData.word,
        answer: correctAnswer,
        starLevel: wordData.star_level
    });
    
    // 显示单词长度提示
    if (lengthHint) {
        lengthHint.textContent = `单词长度: ${correctAnswer.length} 个字母`;
    }
    
    // 移除之前的事件监听器（避免重复绑定）
    if (spellingInput._letterByLetterHandler) {
        spellingInput.removeEventListener('input', spellingInput._letterByLetterHandler);
    }
    
    // 创建新的事件处理器
    spellingInput._letterByLetterHandler = function(event) {
        handleLetterByLetterInput(event, correctAnswer);
    };
    
    // 添加输入事件监听器
    spellingInput.addEventListener('input', spellingInput._letterByLetterHandler);
    
    // 添加键盘事件监听器（处理退格键等）
    if (spellingInput._letterByLetterKeyHandler) {
        spellingInput.removeEventListener('keydown', spellingInput._letterByLetterKeyHandler);
    }
    
    spellingInput._letterByLetterKeyHandler = function(event) {
        // 延迟处理，确保输入值已更新
        setTimeout(() => {
            handleLetterByLetterInput(event, correctAnswer);
        }, 10);
    };
    
    spellingInput.addEventListener('keydown', spellingInput._letterByLetterKeyHandler);
    
    // 清空输入框并初始化显示
    spellingInput.value = '';
    updateLetterFeedback('', correctAnswer);
    updateSpellingProgressBar('', correctAnswer);
    
    // 聚焦到输入框
    spellingInput.focus();
    
    console.log('✅ 逐字母拼写功能设置完成');
}

/**
 * 处理逐字母输入
 * @param {Event} event - 输入事件
 * @param {string} correctAnswer - 正确答案
 */
function handleLetterByLetterInput(event, correctAnswer) {
    const userInput = event.target.value;
    const correctLower = correctAnswer.toLowerCase();
    const userLower = userInput.toLowerCase();
    
    console.log('🔤 逐字母输入处理:', {
        userInput: userInput,
        correctAnswer: correctAnswer,
        progress: `${userInput.length}/${correctAnswer.length}`
    });
    
    // 更新字母反馈显示
    updateLetterFeedback(userInput, correctAnswer);
    
    // 更新进度条
    updateSpellingProgressBar(userInput, correctAnswer);
    
    // 检查是否完成拼写
    if (userInput.length === correctAnswer.length) {
        if (userLower === correctLower) {
            console.log('🎉 拼写完成且正确！');
            // 显示完成状态
            showSpellingCompletedFeedback(true);
            // 延迟自动提交
            setTimeout(() => {
                checkSpelling();
            }, 800);
        } else {
            console.log('❌ 拼写完成但有错误');
            showSpellingCompletedFeedback(false);
        }
    }
    
    // 防止输入超过单词长度
    if (userInput.length > correctAnswer.length) {
        event.target.value = userInput.substring(0, correctAnswer.length);
        handleLetterByLetterInput(event, correctAnswer);
    }
}

/**
 * 更新字母反馈显示
 * @param {string} userInput - 用户输入
 * @param {string} correctAnswer - 正确答案
 */
function updateLetterFeedback(userInput, correctAnswer) {
    const letterFeedback = document.getElementById('letter-feedback');
    if (!letterFeedback) return;
    
    const correctLower = correctAnswer.toLowerCase();
    const userLower = userInput.toLowerCase();
    
    let feedbackHTML = '';
    
    for (let i = 0; i < correctAnswer.length; i++) {
        const correctChar = correctAnswer[i]; // 保持原始大小写
        const correctCharLower = correctLower[i];
        const userChar = userInput[i] || '';
        const userCharLower = userLower[i] || '';
        
        let letterClass = '';
        let displayChar = '';
        
        if (i < userInput.length) {
            // 用户已经输入了这个位置
            if (userCharLower === correctCharLower) {
                letterClass = 'letter-correct';
                displayChar = correctChar; // 显示正确答案的原始大小写
            } else {
                letterClass = 'letter-incorrect';
                displayChar = userChar; // 显示用户输入的字符
            }
        } else {
            // 用户还没有输入这个位置
            letterClass = 'letter-pending';
            displayChar = '_';
        }
        
        feedbackHTML += `<span class="letter-box ${letterClass}">${displayChar}</span>`;
    }
    
    letterFeedback.innerHTML = feedbackHTML;
}

/**
 * 更新拼写进度条
 * @param {string} userInput - 用户输入
 * @param {string} correctAnswer - 正确答案
 */
function updateSpellingProgressBar(userInput, correctAnswer) {
    const progressFill = document.getElementById('progress-fill');
    if (!progressFill) return;
    
    const progress = (userInput.length / correctAnswer.length) * 100;
    progressFill.style.width = `${Math.min(progress, 100)}%`;
    
    // 根据正确性改变进度条颜色
    const correctLower = correctAnswer.toLowerCase();
    const userLower = userInput.toLowerCase();
    
    let allCorrect = true;
    for (let i = 0; i < userInput.length; i++) {
        if (userLower[i] !== correctLower[i]) {
            allCorrect = false;
            break;
        }
    }
    
    if (userInput.length === 0) {
        progressFill.style.backgroundColor = '#e0e0e0';
    } else if (allCorrect) {
        progressFill.style.backgroundColor = '#4CAF50'; // 绿色 - 全部正确
    } else {
        progressFill.style.backgroundColor = '#ff9800'; // 橙色 - 有错误
    }
}

/**
 * 显示拼写完成反馈
 * @param {boolean} isCorrect - 是否完全正确
 */
function showSpellingCompletedFeedback(isCorrect) {
    const letterFeedback = document.getElementById('letter-feedback');
    if (!letterFeedback) return;
    
    // 添加完成状态的视觉反馈
    letterFeedback.style.border = isCorrect ? '2px solid #4CAF50' : '2px solid #f44336';
    letterFeedback.style.borderRadius = '8px';
    letterFeedback.style.padding = '5px';
    
    // 显示完成消息
    const completionMessage = document.createElement('div');
    completionMessage.style.cssText = `
        text-align: center;
        margin-top: 10px;
        font-weight: bold;
        color: ${isCorrect ? '#4CAF50' : '#f44336'};
        font-size: 14px;
    `;
    completionMessage.textContent = isCorrect ? '🎉 拼写正确！' : '❌ 请检查拼写';
    
    // 移除之前的消息
    const existingMessage = letterFeedback.parentElement.querySelector('.completion-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    completionMessage.className = 'completion-message';
    letterFeedback.parentElement.appendChild(completionMessage);
    
    // 如果不正确，3秒后移除反馈样式
    if (!isCorrect) {
        setTimeout(() => {
            letterFeedback.style.border = '';
            letterFeedback.style.borderRadius = '';
            letterFeedback.style.padding = '';
            if (completionMessage.parentElement) {
                completionMessage.remove();
            }
        }, 3000);
    }
}

// 检查拼写功能
async function checkSpelling() {
    const userInput = document.getElementById('user-input').value.trim();
    const spellingInput = document.getElementById('spelling-input').value.trim();
    const inputValue = userInput || spellingInput;
    
    if (!inputValue) {
        document.getElementById('hint').textContent = '请输入答案';
        return;
    }

    if (!filteredWords || filteredWords.length === 0 || currentWordIndex >= filteredWords.length) {
        document.getElementById('hint').textContent = '没有当前单词';
        return;
    }

    const currentWord = filteredWords[currentWordIndex];
    const wordId = currentWord.id;                           // 使用正确的ID字段
    const correctAnswer = currentWord.answer || '';          // 英文单词是正确答案
    const chineseText = currentWord.word || '';              // 中文含义

    console.log('🔍 检查答案:', inputValue, '正确答案:', correctAnswer);
    console.log('📝 答案验证API调用参数:', { wordId, inputValue, correctAnswer, chineseText });

    try {
        // 🔧 修复跨日期问题：获取当前学习计划的日期
        const urlParams = new URLSearchParams(window.location.search);
        const planDate = urlParams.get('date') || new Date().toISOString().split('T')[0]; // 默认今天

        console.log('📅 使用学习计划日期:', planDate);

        // 🚀 使用后端API验证答案
        const response = await fetch('/api/submit_answer', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                word_id: wordId,
                user_input: inputValue,  // 修正参数名：user_answer -> user_input
                duration_seconds: 5, // 临时值
                plan_date: planDate  // 🔧 新增：传递学习计划日期
            })
        });

        const result = await response.json();
        console.log('🔍 API响应结果:', result); // 调试日志
        console.log('🔍 当前单词索引:', currentWordIndex, '总单词数:', filteredWords.length);

        if (result.success) {
            // 🔧 修正：从嵌套的data对象中获取数据
            const isCorrect = result.data?.is_correct || false;
            const apiCorrectAnswer = result.data?.correct_answer || correctAnswer;
            const consecutiveCount = result.data?.consecutive_correct || 0;
            const voucherResult = result.data?.voucher_result || {};

            console.log('🔍 答案验证结果:', {
                isCorrect,
                apiCorrectAnswer,
                userInput: inputValue,
                consecutiveCount,
                voucherResult
            });

            // 🔧 调试：检查连击数和购物券数据
            console.log('🎯 连击数据:', consecutiveCount);
            console.log('🎫 购物券数据:', voucherResult);

            // 更新UI反馈
            document.getElementById('feedback').innerHTML =
                `<div style="color: ${isCorrect ? 'green' : 'red'}; font-weight: bold;">
                    ${isCorrect ? '✅ 正确！' : '❌ 错误，正确答案是: ' + apiCorrectAnswer}
                </div>`;

            // 🔧 立即更新本地单词数据
            if (result.data && result.data.new_star_level !== undefined && filteredWords[currentWordIndex]) {
                const oldStarLevel = filteredWords[currentWordIndex].star_level;
                const newStarLevel = result.data.new_star_level;

                // 更新当前单词的星级
                filteredWords[currentWordIndex].star_level = newStarLevel;

                // 同时更新dailyWords数组中对应的单词
                const wordId = filteredWords[currentWordIndex].id;
                const dailyWordIndex = dailyWords.findIndex(w => w.id === wordId);
                if (dailyWordIndex !== -1) {
                    dailyWords[dailyWordIndex].star_level = newStarLevel;
                }

                console.log('🔄 本地单词数据已更新:', {
                    word: filteredWords[currentWordIndex].word,
                    wordId: wordId,
                    oldStarLevel: oldStarLevel,
                    newStarLevel: newStarLevel,
                    dailyWordUpdated: dailyWordIndex !== -1
                });

                // 🔧 修复问题1：如果单词达到5星且未选择复习5星单词，标记移除但延迟处理
                if (newStarLevel === 5 && !window.includeFiveStarsInLearning) {
                    console.log(`🎯 单词达到5星，标记从学习列表中移除: ${filteredWords[currentWordIndex].word}`);
                    
                    // 🔧 标记当前单词已达5星，但不立即移除，避免与Recognition模式冲突
                    if (filteredWords[currentWordIndex]) {
                        filteredWords[currentWordIndex]._should_remove = true;
                        console.log(`🔄 标记5星单词待移除，当前单词索引: ${currentWordIndex}`);
                    }
                }

                // 🔄 立即更新星级分布显示
                console.log('🔄 答题后立即更新星级分布显示...');

                // 备用方法
                displayStudyPlan();
                forceUpdateStarDistribution();

                console.log('🔄 更新后的星级分布:', getLocalStarDistribution());
            }

            // 🎬 添加答对/答错动画效果（包含连击数）
            showAnswerAnimation(isCorrect, apiCorrectAnswer, consecutiveCount);

            // 🎫 处理购物券奖励
            if (voucherResult && voucherResult.vouchers_earned > 0) {
                // 显示购物券动画
                showVoucherAnimation(voucherResult.vouchers_earned, voucherResult.reason);

                // 更新界面上的购物券数字 - 重新获取最新数据
                setTimeout(async () => {
                    await loadUserPoints(); // 重新加载用户信息，确保购物券数字是最新的
                }, 1000); // 延迟1秒更新，让动画先播放
            }

            // 🔊 播放音效 - 优先播放连击音效，回退到默认音效
            if (isCorrect && consecutiveCount >= 2) {
                // 答对且有连击时，连击音效已在动画中播放
                console.log('🎵 答对，连击音效已在动画中播放，连击数:', consecutiveCount);
            } else {
                // 单次答对或答错时，播放默认音效
                console.log('🎵 播放默认音效，连击数:', consecutiveCount, '是否正确:', isCorrect);
                playAnswerSound(isCorrect);
            }

            // 清空输入框
            document.getElementById('user-input').value = '';
            document.getElementById('spelling-input').value = '';

            // 更新积分
            if (isCorrect) {
                updateUserPoints(10, '答对单词');
            } else {
                updateUserPoints(-20, '答错单词');
            }

            // 延迟后显示下一个单词
            setTimeout(() => {
                // 🔧 修复：不再在这里处理单词移除逻辑，交给fetchWord()统一处理
                // 单词已被标记移除（如果达到5星），fetchWord()会自动处理
                
                // ⚠️ 注意：5星单词不会立即移除，而是被标记为_should_remove
                // fetchWord()会在显示下一个单词时检查并移除标记的单词
                currentWordIndex++;

                fetchWord();
                document.getElementById('feedback').innerHTML = '';
            }, 2000);
        } else {
            console.error('❌ API返回失败:', result);
            document.getElementById('feedback').innerHTML =
                `<div style="color: red;">提交失败: ${result.message || result.error?.message || '未知错误'}</div>`;
        }
    } catch (error) {
        console.error('❌ 提交答案异常:', error);
        document.getElementById('feedback').innerHTML = '<div style="color: red;">网络错误，请重试</div>';
    }
}

// 添加到生词本功能
async function addToVocabulary() {
    if (!filteredWords || filteredWords.length === 0 || currentWordIndex >= filteredWords.length) {
        document.getElementById('feedback').innerHTML =
            '<div style="color: orange;">没有当前单词</div>';
        return;
    }

    const currentWord = filteredWords[currentWordIndex];
    const wordId = currentWord.id;                    // 使用正确的ID字段
    const englishWord = currentWord.answer || '';     // 英文单词
    const chineseWord = currentWord.word || '';       // 中文含义

    console.log('📚 添加生词本API调用参数:', { wordId, englishWord, chineseWord });

    // 显示加载状态
    document.getElementById('feedback').innerHTML =
        '<div style="color: blue;">📚 正在添加到生词本...</div>';

    try {
        const response = await fetch('/add_to_vocabulary', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                word_id: wordId
            })
        });

        const result = await response.json();
        console.log('📚 生词本API响应:', result);

        if (result.success) {
            document.getElementById('feedback').innerHTML =
                `<div style="color: green; font-weight: bold;">✅ 已添加到生词本</div>`;

            // 显示添加成功动画
            showVocabularyAnimation(englishWord, chineseWord);

            setTimeout(() => {
                document.getElementById('feedback').innerHTML = '';
            }, 3000);
        } else {
            // 处理错误信息，确保显示字符串而不是对象
            let errorMessage = '添加失败';
            if (result.message) {
                errorMessage = result.message;
            } else if (result.error) {
                if (typeof result.error === 'string') {
                    errorMessage = result.error;
                } else if (result.error.message) {
                    errorMessage = result.error.message;
                } else {
                    errorMessage = '添加失败：' + JSON.stringify(result.error);
                }
            }

            document.getElementById('feedback').innerHTML =
                `<div style="color: orange;">⚠️ ${errorMessage}</div>`;

            setTimeout(() => {
                document.getElementById('feedback').innerHTML = '';
            }, 3000);
        }
    } catch (error) {
        console.error('❌ 添加生词库异常:', error);
        document.getElementById('feedback').innerHTML =
            '<div style="color: red;">网络错误，请重试</div>';

        setTimeout(() => {
            document.getElementById('feedback').innerHTML = '';
        }, 3000);
    }
}

// 生词库添加成功动画
function showVocabularyAnimation(englishWord, chineseWord) {
    console.log(`🎬 显示生词库动画: ${englishWord} - ${chineseWord}`);

    // 创建动画元素
    const animationElement = document.createElement('div');
    animationElement.style.cssText = `
        position: fixed;
        top: 30%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 20px;
        font-weight: bold;
        color: #2196F3;
        background: rgba(255, 255, 255, 0.95);
        padding: 15px 25px;
        border-radius: 12px;
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        z-index: 10000;
        pointer-events: none;
        animation: vocabularyAnimation 2.5s ease-out forwards;
        border-left: 4px solid #2196F3;
    `;

    animationElement.innerHTML = `
        <div style="display: flex; align-items: center;">
            📚 <span style="margin-left: 8px;">已添加到生词库</span>
        </div>
        <div style="font-size: 14px; color: #666; margin-top: 5px;">
            ${englishWord} - ${chineseWord}
        </div>
    `;

    // 添加CSS动画
    if (!document.getElementById('vocabulary-animation-style')) {
        const style = document.createElement('style');
        style.id = 'vocabulary-animation-style';
        style.textContent = `
            @keyframes vocabularyAnimation {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -60%) scale(0.9); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(animationElement);

    // 2.5秒后移除元素
    setTimeout(() => {
        if (animationElement.parentNode) {
            animationElement.parentNode.removeChild(animationElement);
        }
    }, 2500);
}

// 移动端图片显示检测和修复
function checkMobileImageDisplay() {
    if (window.innerWidth <= 768) {
        const wordImage = document.getElementById('word-image');
        if (wordImage && wordImage.src && wordImage.complete) {
            // 检查图片是否真的显示了
            if (wordImage.naturalWidth === 0 || window.getComputedStyle(wordImage).display === 'none') {
                console.log('🔧 检测到移动端图片显示问题，尝试修复...');
                
                // 强制显示
                wordImage.style.display = 'block';
                wordImage.style.visibility = 'visible';
                wordImage.style.opacity = '1';
                
                // 如果imageLoader可用，使用它的修复方法
                if (window.imageLoader && typeof window.imageLoader.forceMobileImageDisplay === 'function') {
                    window.imageLoader.forceMobileImageDisplay(wordImage);
                }
            }
        }
    }
}

// 智能图片加载功能 - 集成自动生成能力和移动端优化
async function loadWordImageFromAPI(word, imgElement) {
    try {
        console.log('🖼️ 智能加载单词图片:', word);

        // 首先检查是否有智能图片加载器
        if (typeof window.imageLoader === 'undefined') {
            window.imageLoader = new ImageLoader();
        }

        // 使用智能图片加载器（启用自动生成）
        const success = await window.imageLoader.loadWordImage(
            word, 
            imgElement, 
            () => {
                // 成功回调
                imgElement.style.display = 'block';
                console.log('✅ 智能图片加载成功:', word);
                
                // 延迟检查移动端显示
                setTimeout(() => {
                    checkMobileImageDisplay();
                }, 200);
            },
            (failedWord) => {
                // 失败回调
                imgElement.style.display = 'none';
                console.log('❌ 智能图片加载失败:', failedWord);
            },
            true  // 允许自动生成
        );

        if (!success) {
            console.log('⚠️ 图片加载器返回失败');
            imgElement.style.display = 'none';
        }

    } catch (error) {
        console.error('❌ 智能图片加载异常:', error);
        imgElement.style.display = 'none';
        
        // 降级到占位符
        if (imgElement) {
            imgElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zNWVtIj7lm77niYfkuI3lrZjlnKg8L3RleHQ+PC9zdmc+';
            imgElement.style.display = 'block';
            
            // 移动端显示检查
            setTimeout(() => {
                checkMobileImageDisplay();
            }, 100);
        }
    }
}

// 智能音频播放功能 - 处理用户交互限制
function playAudioSmart(audioSrc) {
    console.log('🎵 智能播放音频:', audioSrc);

    // 检查用户是否已交互
    if (!window.userHasInteracted) {
        console.log('🎵 用户未交互，延迟播放');
        window.pendingAutoPlayAudio = audioSrc;
        return;
    }

    // 直接播放音频
    const audio = new Audio(audioSrc);
    audio.volume = 0.5;
    audio.play().then(() => {
        console.log('✅ 智能音频播放成功');
    }).catch(error => {
        console.error('❌ 智能音频播放失败:', error);
    });
}

// 播放答对/答错音效
function playAnswerSound(isCorrect) {
    // 检查用户是否已交互
    if (!window.userHasInteracted) {
        console.log('🎵 用户未交互，跳过音效播放');
        return;
    }

    const soundFile = isCorrect ? 'positive.wav' : 'negative.wav';
    const audioUrl = `/static/audio/${soundFile}`;

    console.log('🔊 播放答案音效:', { isCorrect, soundFile, audioUrl });

    const audio = new Audio(audioUrl);
    audio.volume = 0.6; // 稍微大声一点
    audio.play().then(() => {
        console.log('✅ 答案音效播放成功');
    }).catch(error => {
        console.error('❌ 答案音效播放失败:', error);
    });
}

// 全局音频播放状态管理
let isAudioPlaying = false;

// 音频播放功能
function playCurrentWordAudio() {
    // 防重复点击机制
    if (isAudioPlaying) {
        console.log('🎵 音频正在播放，跳过重复请求');
        return;
    }
    
    if (!filteredWords || filteredWords.length === 0 || currentWordIndex >= filteredWords.length) {
        console.log('❌ 没有当前单词');
        return;
    }

    const currentWord = filteredWords[currentWordIndex];
    const word = currentWord.answer || '';  // 使用英文单词

    // 处理文件名：替换空格为下划线，转为小写
    const fileName = word.toLowerCase().replace(/\s+/g, '_');

    // 优先尝试mp3格式
    let audioUrl = `/static/audio/words/${fileName}.mp3`;

    console.log('🎵 播放音频:', { word, fileName, audioUrl });

    // 检查用户是否已交互
    if (!window.userHasInteracted) {
        console.log('🎵 用户未交互，延迟播放');
        window.pendingAutoPlayAudio = audioUrl;
        return;
    }

    const audio = new Audio(audioUrl);
    audio.volume = 0.5;

    // 设置播放状态
    isAudioPlaying = true;

    // 播放结束后重置状态
    audio.onended = () => {
        isAudioPlaying = false;
        console.log('🎵 音频播放结束，重置状态');
    };

    // 播放出错时重置状态
    audio.onerror = () => {
        isAudioPlaying = false;
        console.log('🔇 音频播放失败或文件不存在:', fileName);
        console.log('🎵 尝试TTS自动生成音频:', word);
        generateAudioIfNeeded(word, fileName, audioUrl);
    };

    audio.play().then(() => {
        console.log('✅ 音频播放成功');
    }).catch(error => {
        isAudioPlaying = false;
        console.error('❌ 音频播放失败:', error);
        console.log('🎵 播放失败，可能是文件损坏，尝试TTS生成:', word);
        generateAudioIfNeeded(word, fileName, audioUrl);
    });
}

// 智能音频生成函数 - 当音频不存在时自动调用TTS
async function generateAudioIfNeeded(word, fileName, originalAudioUrl) {
    try {
        console.log('🎵 开始TTS音频生成:', { word, fileName });
        
        // 显示生成状态（可选）
        showAudioGenerationStatus('正在生成音频...');
        
        // 调用后端TTS生成API
        const response = await fetch('/api/generate_audio', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                word: word
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ TTS音频生成成功:', result);
                
                // 隐藏生成状态
                hideAudioGenerationStatus();
                
                // 播放新生成的音频
                const newAudio = new Audio(result.audio_url + '?t=' + Date.now()); // 添加时间戳避免缓存
                newAudio.volume = 0.5;
                
                // 播放结束后重置状态
                newAudio.onended = () => {
                    isAudioPlaying = false;
                    console.log('🎵 TTS音频播放结束，重置状态');
                };
                
                newAudio.onload = () => {
                    console.log('✅ 新音频加载成功，开始播放');
                };
                
                newAudio.onerror = () => {
                    isAudioPlaying = false;
                    console.log('❌ 新生成的音频播放失败');
                    showAudioGenerationStatus('音频生成失败', 'error');
                };
                
                newAudio.play().then(() => {
                    console.log('✅ TTS生成的音频播放成功');
                    showAudioGenerationStatus('音频生成并播放成功！', 'success');
                }).catch(error => {
                    isAudioPlaying = false;
                    console.error('❌ TTS音频播放失败:', error);
                    showAudioGenerationStatus('音频播放失败', 'error');
                });
                
            } else {
                isAudioPlaying = false;
                console.log('❌ TTS生成失败:', result.message);
                showAudioGenerationStatus(`生成失败: ${result.message}`, 'error');
            }
        } else {
            isAudioPlaying = false;
            console.log('❌ TTS API调用失败:', response.status);
            showAudioGenerationStatus('TTS服务暂时不可用', 'error');
        }
        
    } catch (error) {
        isAudioPlaying = false;
        console.error('❌ TTS生成异常:', error);
        showAudioGenerationStatus('音频生成异常', 'error');
    }
}

// 显示音频生成状态
function showAudioGenerationStatus(message, type = 'info') {
    // 创建或更新状态显示
    let statusElement = document.getElementById('audio-generation-status');
    
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.id = 'audio-generation-status';
        statusElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
            transition: all 0.3s ease;
            max-width: 300px;
        `;
        document.body.appendChild(statusElement);
    }
    
    // 设置样式和内容
    const styles = {
        info: { background: '#e3f2fd', color: '#1976d2', border: '1px solid #bbdefb' },
        success: { background: '#e8f5e8', color: '#2e7d32', border: '1px solid #c8e6c8' },
        error: { background: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' }
    };
    
    const style = styles[type] || styles.info;
    statusElement.style.background = style.background;
    statusElement.style.color = style.color;
    statusElement.style.border = style.border;
    statusElement.textContent = `🎵 ${message}`;
    
    // 自动隐藏成功和错误消息
    if (type === 'success' || type === 'error') {
        setTimeout(() => {
            hideAudioGenerationStatus();
        }, 3000);
    }
}

// 隐藏音频生成状态
function hideAudioGenerationStatus() {
    const statusElement = document.getElementById('audio-generation-status');
    if (statusElement) {
        statusElement.remove();
    }
}

// 生成渐进式掩码提示
function generateProgressiveHint(word, level) {
    if (!word || level < 1 || level > maxHintLevel) {
        return word;
    }

    const wordLength = word.length;
    const showCount = Math.ceil((wordLength * level * 20) / 100); // 每级显示20%的字母

    // 创建字母位置数组
    const positions = Array.from({length: wordLength}, (_, i) => i);

    // 随机选择要显示的位置
    const showPositions = [];
    for (let i = 0; i < Math.min(showCount, wordLength); i++) {
        const randomIndex = Math.floor(Math.random() * positions.length);
        showPositions.push(positions[randomIndex]);
        positions.splice(randomIndex, 1);
    }

    // 生成掩码字符串
    let maskedWord = '';
    for (let i = 0; i < wordLength; i++) {
        if (showPositions.includes(i)) {
            maskedWord += word[i];
        } else {
            maskedWord += '_';
        }
    }

    return maskedWord;
}

// 渐进式提示功能 - 5级提示系统
function getHint() {
    if (!filteredWords || filteredWords.length === 0 || currentWordIndex >= filteredWords.length) {
        document.getElementById('hint').textContent = '没有当前单词';
        return;
    }

    // 检查是否已达到最大提示级别
    if (currentHintLevel >= maxHintLevel) {
        document.getElementById('hint').innerHTML =
            `<div style="color: #666; font-weight: bold;">💡 已达到最大提示级别 (${maxHintLevel}/${maxHintLevel})</div>`;
        return;
    }

    const currentWord = filteredWords[currentWordIndex];
    const englishWord = currentWord.answer || '';  // 英文单词

    // 增加提示级别
    currentHintLevel++;

    // 生成渐进式掩码
    const maskedWord = generateProgressiveHint(englishWord, currentHintLevel);
    const progress = Math.round((currentHintLevel / maxHintLevel) * 100);

    console.log(`🔍 渐进式提示: ${englishWord} -> ${maskedWord} (级别${currentHintLevel}/${maxHintLevel})`);

    document.getElementById('hint').innerHTML =
        `<div style="color: #333; font-weight: normal;">
            💡 提示 (${currentHintLevel}/${maxHintLevel}): <span style="font-size: 18px; letter-spacing: 2px; font-weight: bold;">${maskedWord}</span>
            <br><small style="color: #666;">进度: ${progress}% - 每次点击显示更多字母</small>
        </div>`;

    console.log(`✅ 提示显示成功: 级别${currentHintLevel}/${maxHintLevel}, 内容: ${maskedWord}`);
}

// 简单的Markdown渲染函数
function renderMarkdown(text) {
    // 确保输入是字符串
    if (!text || typeof text !== 'string') {
        console.log('🔍 renderMarkdown输入:', typeof text, text);
        return String(text || '');
    }

    return text
        // 标题
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        // 粗体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // 斜体
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // 代码块
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        // 行内代码
        .replace(/`(.*?)`/g, '<code>$1</code>')
        // 链接
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
        // 列表项
        .replace(/^\* (.*$)/gim, '<li>$1</li>')
        .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
        // 换行
        .replace(/\n/g, '<br>');
}

// AI帮帮记功能 - 使用本地缓存文件
async function getMemoryHelp() {
    if (!filteredWords || filteredWords.length === 0 || currentWordIndex >= filteredWords.length) {
        document.getElementById('memory-help').textContent = '没有当前单词';
        return;
    }

    const currentWord = filteredWords[currentWordIndex];
    const englishWord = currentWord.answer || '';  // 英文单词
    const chineseWord = currentWord.word || '';    // 中文含义

    console.log('🤖 AI帮帮记本地缓存查找:', { englishWord, chineseWord });

    // 显示加载状态
    document.getElementById('memory-help').innerHTML =
        '<div style="color: blue;">🤖 正在加载记忆帮助...</div>';

    try {
        // 标准化文件名：替换空格为下划线
        const standardFileName = englishWord.replace(/\s+/g, '_');

        // 尝试标准化的文件格式（优先级从高到低）
        const possibleFiles = [
            `${standardFileName}.txt`,           // 标准化名称 + .txt
            `${standardFileName}.md`,            // 标准化名称 + .md
            `${standardFileName.toLowerCase()}.txt`,  // 小写版本 + .txt
            `${standardFileName.toLowerCase()}.md`    // 小写版本 + .md
        ];

        let memoryContent = '';
        let foundFile = '';

        // 依次尝试每个可能的文件
        for (const file of possibleFiles) {
            try {
                const response = await fetch(`/static/cache/memory_help/${encodeURIComponent(file)}`);
                if (response.ok) {
                    memoryContent = await response.text();
                    foundFile = file;
                    console.log('✅ 找到缓存文件:', foundFile);
                    break;
                }
            } catch (e) {
                // 继续尝试下一个文件
                console.log(`❌ 尝试文件失败: ${file}`, e);
                continue;
            }
        }

        if (memoryContent) {
            const renderedContent = renderMarkdown(memoryContent);

            document.getElementById('memory-help').innerHTML =
                `<div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #0066cc;">
                    <h4 style="color: #0066cc; margin: 0 0 12px 0; display: flex; align-items: center;">
                        🤖 AI记忆帮助
                        <span style="font-size: 12px; font-weight: normal; color: #666; margin-left: 10px;">
                            (${englishWord} - ${chineseWord})
                        </span>
                    </h4>
                    <div style="line-height: 1.6; color: #333;">${renderedContent}</div>
                    <div style="font-size: 10px; color: #999; margin-top: 8px;">
                        📁 来源: ${foundFile}
                    </div>
                </div>`;

            console.log('✅ AI帮帮记显示成功，来源:', foundFile);
        } else {
            document.getElementById('memory-help').innerHTML =
                `<div style="color: orange;">
                    ⚠️ 未找到单词 "${englishWord}" 的记忆帮助文件
                    <br><small style="color: #666;">尝试的文件: ${possibleFiles.join(', ')}</small>
                </div>`;
            console.log('❌ 未找到缓存文件，尝试的文件:', possibleFiles);
        }
    } catch (error) {
        console.error('❌ 获取AI帮助异常:', error);
        document.getElementById('memory-help').innerHTML =
            '<div style="color: red;">加载记忆帮助失败，请重试</div>';
    }
}

console.log('✅ 前后端分离版 learning_logic.js 加载完成');
console.log('🔄 JavaScript版本: 2025-07-11-002 (星级分布实时更新版)');

// 🚀 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📚 学习页面加载完成，开始初始化...');
    console.log('🔄 当前时间:', new Date().toLocaleString());

    // 🎯 检查URL参数，设置5星复习模式
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('review5star') === 'true') {
        console.log('🌟 检测到5星复习模式参数，启用5星复习');
        window.includeFiveStarsInLearning = true;

        // 延迟更新复选框状态（等待DOM创建完成）
        setTimeout(() => {
            const checkbox = document.getElementById('include-five-stars');
            if (checkbox) {
                checkbox.checked = true;
                console.log('✅ 已同步左侧复选框状态为选中');
            }
        }, 500);
    }

    // 🔧 用户交互检测（已移除，不再需要）
    // setupUserInteractionDetection();

    // 🚀 使用后端API获取每日计划
    fetchDailyPlan();

    // 🚀 使用后端API获取用户状态
    loadUserPoints();
});

// ================== Recognition模式（选择题）相关函数 ==================

// Recognition模式的全局变量
let currentRecognitionData = null;
let selectedRecognitionOptions = [];  // 🚀 支持多选：改为数组

/**
 * 显示Recognition模式的学习界面
 * @param {Object} wordData - 当前单词数据
 */
function showRecognitionMode(wordData) {
    console.log('🎯 进入Recognition模式:', wordData);
    
    // 隐藏其他学习模式
    document.getElementById('traditional-mode').style.display = 'none';
    document.getElementById('spelling-mode').style.display = 'none';
    
    // 显示Recognition模式
    document.getElementById('recognition-mode').style.display = 'block';
    
    // 获取选择题数据并显示
    fetchRecognitionQuestion(wordData);
}

/**
 * 从后端获取Recognition模式的选择题数据
 * @param {Object} wordData - 当前单词数据
 */
async function fetchRecognitionQuestion(wordData) {
    try {
        const response = await fetch('/api/recognition/question', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                word_id: wordData.id,
                question_type: Math.random() < 0.5 ? 'cn_to_en' : 'en_to_cn' // 随机选择题型
            })
        });

        const data = await response.json();
        if (data.success) {
            currentRecognitionData = data.data;
            displayRecognitionQuestion();
        } else {
            console.error('❌ 获取选择题失败:', data.error);
            // 降级到拼写模式
            showSpellingMode(wordData);
        }
    } catch (error) {
        console.error('❌ 获取选择题异常:', error);
        // 降级到拼写模式
        showSpellingMode(wordData);
    }
}

/**
 * 显示拼写模式（当Recognition模式失败时的降级方案）
 * @param {Object} wordData - 当前单词数据
 */
function showSpellingMode(wordData) {
    console.log('🔄 降级到拼写模式');
    
    // 隐藏所有学习模式
    document.getElementById('traditional-mode').style.display = 'none';
    document.getElementById('spelling-mode').style.display = 'none';
    document.getElementById('recognition-mode').style.display = 'none';
    
    // 根据星级决定显示传统模式还是拼写模式
    const starLevel = wordData.star_level || 3;
    
    if (starLevel >= 3) {
        // 传统模式（星级>=3）
        document.getElementById('traditional-mode').style.display = 'block';
        document.getElementById('user-input').focus();
        console.log('✅ 显示传统模式（星级>=3）');
    } else {
        // 拼写模式（星级<3）
        document.getElementById('spelling-mode').style.display = 'block';
        document.getElementById('spelling-input').focus();
        console.log('✅ 显示拼写模式（星级<3）');
    }
}

/**
 * 显示Recognition选择题（支持多选）
 */
function displayRecognitionQuestion() {
    if (!currentRecognitionData) return;
    
    const { question, question_type, options, correct_answers, is_multiple_choice, total_meanings } = currentRecognitionData;
    
    // 🚀 根据是否多选设置问题文本
    let questionText;
    if (is_multiple_choice) {
        questionText = question_type === 'cn_to_en' 
            ? `请选择"${question}"的英文：`
            : `请选择"${question}"的所有中文意思（多选，共${total_meanings}个含义）：`;
    } else {
        questionText = question_type === 'cn_to_en' 
            ? `请选择"${question}"的英文：`
            : `请选择"${question}"的中文意思：`;
    }
    
    document.getElementById('question-text').textContent = questionText;
    
    // 🚀 添加多选提示
    const hintElement = document.getElementById('hint');
    if (is_multiple_choice) {
        if (question_type === 'en_to_cn') {
            hintElement.innerHTML = `<div style="color: #ff6b35; font-weight: bold;">📌 多选题：该单词有${total_meanings}个含义，请全部选中</div>`;
        } else {
            hintElement.textContent = '请从下方选项中选择正确的英文';
        }
        // 显示英文单词（避免答案泄露的问题在多选题中不明显）
        document.getElementById('word').textContent = question;
    } else {
        // 根据题型调整单词显示区域，避免答案泄露
        if (question_type === 'en_to_cn') {
            document.getElementById('word').textContent = question;
            hintElement.textContent = '请从下方选项中选择正确的中文意思';
        } else {
            document.getElementById('word').textContent = question;
            hintElement.textContent = '请从下方选项中选择正确的英文';
        }
    }
    
    // 设置选项
    console.log('🔍 开始设置选项:', options);
    options.forEach((option, index) => {
        const optionKey = String.fromCharCode(65 + index); // A, B, C, D
        const optionElement = document.getElementById(`option-${optionKey}-text`);
        const optionButton = document.querySelector(`[data-option="${optionKey}"]`);
        
        console.log(`🔍 设置选项 ${optionKey}:`, {
            option: option,
            element: optionElement,
            button: optionButton
        });
        
        if (optionElement) {
            optionElement.textContent = option;
        } else {
            console.error(`❌ 找不到选项元素: option-${optionKey}-text`);
        }
        
        if (optionButton) {
            // 确保选项按钮可见
            optionButton.style.display = 'flex';
            optionButton.style.visibility = 'visible';
        } else {
            console.error(`❌ 找不到选项按钮: [data-option="${optionKey}"]`);
        }
    });
    
    // 隐藏没有使用的选项
    const allOptionKeys = ['A', 'B', 'C', 'D'];
    allOptionKeys.slice(options.length).forEach(optionKey => {
        const optionButton = document.querySelector(`[data-option="${optionKey}"]`);
        if (optionButton) {
            optionButton.style.display = 'none';
            console.log(`🙈 隐藏未使用的选项: ${optionKey}`);
        }
    });
    
    // 重置选择状态
    resetRecognitionOptions();
    
    console.log('✅ Recognition题目显示完成:', {
        question: questionText,
        isMultiple: is_multiple_choice,
        correctAnswers: correct_answers,
        optionCount: options.length
    });
}

/**
 * 重置选择题的选择状态（支持多选）
 */
function resetRecognitionOptions() {
    selectedRecognitionOptions = [];  // 🚀 重置为空数组
    document.getElementById('recognition-confirm-btn').disabled = true;
    document.getElementById('recognition-feedback').style.display = 'none';
    
    // 重置所有选项的样式
    const options = ['A', 'B', 'C', 'D'];
    options.forEach(option => {
        const button = document.querySelector(`[data-option="${option}"]`);
        if (button) {
            button.classList.remove('selected', 'correct', 'incorrect');
        }
    });
}

/**
 * 选择Recognition选项（支持多选）
 * @param {string} option - 选项 A/B/C/D
 */
function selectRecognitionOption(option) {
    console.log('🎯 选择选项:', option);
    
    const currentButton = document.querySelector(`[data-option="${option}"]`);
    if (!currentButton) return;
    
    const isMultipleChoice = currentRecognitionData && currentRecognitionData.is_multiple_choice;
    
    if (isMultipleChoice) {
        // 🚀 多选模式：切换选择状态
        const index = selectedRecognitionOptions.indexOf(option);
        if (index > -1) {
            // 取消选择
            selectedRecognitionOptions.splice(index, 1);
            currentButton.classList.remove('selected');
        } else {
            // 添加选择
            selectedRecognitionOptions.push(option);
            currentButton.classList.add('selected');
        }
        
        console.log('🎯 多选当前选择:', selectedRecognitionOptions);
    } else {
        // 🎯 单选模式：取消之前选择，设置新选择
        // 取消之前的选择
        selectedRecognitionOptions.forEach(prevOption => {
            const prevButton = document.querySelector(`[data-option="${prevOption}"]`);
            if (prevButton) {
                prevButton.classList.remove('selected');
            }
        });
        
        // 设置新选择
        selectedRecognitionOptions = [option];
        currentButton.classList.add('selected');
        
        console.log('🎯 单选当前选择:', selectedRecognitionOptions);
    }
    
    // 🚀 启用确认按钮（只要有选择）
    document.getElementById('recognition-confirm-btn').disabled = selectedRecognitionOptions.length === 0;
}

/**
 * 确认Recognition答案（支持多选）
 */
async function confirmRecognitionAnswer() {
    if (!selectedRecognitionOptions.length || !currentRecognitionData) return;
    
    // 🔍 详细的调试信息输出
    console.log('🔍=== Recognition答题调试信息 ===');
    console.log('当前题目数据:', currentRecognitionData);
    console.log('用户选择的字母:', selectedRecognitionOptions);
    console.log('选项数组:', currentRecognitionData?.options);
    console.log('正确答案数组:', currentRecognitionData?.correct_answers);
    console.log('是否多选题:', currentRecognitionData?.is_multiple_choice);
    
    // 🔍 显示详细的选项转换过程
    if (selectedRecognitionOptions.length > 0 && currentRecognitionData?.options) {
        console.log('🔍 选项转换过程:');
        selectedRecognitionOptions.forEach(letter => {
            const index = letter.charCodeAt(0) - 65;
            const text = currentRecognitionData.options[index];
            const isCorrect = currentRecognitionData.correct_answers.includes(text);
            console.log(`  选择 ${letter} (索引${index}) -> "${text}" ${isCorrect ? '✅正确' : '❌错误'}`);
        });
        
        // 🔍 显示所有选项的对应关系
        console.log('🔍 所有选项对应关系:');
        currentRecognitionData.options.forEach((option, index) => {
            const letter = String.fromCharCode(65 + index);
            const isCorrect = currentRecognitionData.correct_answers.includes(option);
            const isSelected = selectedRecognitionOptions.includes(letter);
            console.log(`  ${letter}: "${option}" ${isCorrect ? '✅正确' : '❌干扰'} ${isSelected ? '👆已选' : ''}`);
        });
    }
    console.log('🔍=== 调试信息结束 ===');
    
    const { correct_answers, options, is_multiple_choice } = currentRecognitionData;
    
    // 🚀 提交答案到后端（包含所有必要信息）
    await submitRecognitionAnswer();
    
    // 🚀 显示反馈（传递更多信息）
    showRecognitionFeedback();
}

/**
 * 提交Recognition答案到后端（支持多选）
 */
async function submitRecognitionAnswer() {
    if (!currentRecognitionData) return;
    
    try {
        const startTime = window.currentWordStartTime || Date.now();
        const duration = (Date.now() - startTime) / 1000;
        
        // 🚀 构建支持多选的请求数据
        const { correct_answers, is_multiple_choice, options } = currentRecognitionData;
        
        // 🚀 转换选项字母为实际文本
        const selectedTexts = selectedRecognitionOptions.map(optionLetter => {
            const index = optionLetter.charCodeAt(0) - 65; // A=0, B=1, C=2, D=3
            return options[index];
        });
        
        console.log('🔍 提交数据:', {
            selectedOptions: selectedRecognitionOptions,
            selectedTexts: selectedTexts,
            correctAnswers: correct_answers,
            isMultiple: is_multiple_choice
        });
        
        const response = await fetch('/api/recognition/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                word_id: currentRecognitionData.word_id,
                selected_options: selectedTexts,  // 🚀 发送选项文本数组
                correct_answers: correct_answers,  // 🚀 发送正确答案数组
                is_multiple_choice: is_multiple_choice,  // 🚀 标识多选题
                duration_seconds: duration,
                question_type: currentRecognitionData.question_type
            })
        });

        const data = await response.json();
        if (data.success) {
            console.log('✅ Recognition答案提交成功:', data.data);
            
            // 🚀 保存提交结果供反馈显示使用 - 修复数据结构
            window.lastRecognitionResult = data;
            
            // 更新用户积分和星级
            if (data.data.new_star_level !== undefined) {
                updateLocalWordData(data.data.new_star_level);
            }
            
            // 更新积分显示
            if (data.data.points_change) {
                updateScoreDisplay(data.data.points_change);
            }
            
            // 播放音频
            try {
                const currentWord = filteredWords[currentWordIndex];
                if (currentWord && currentWord.answer) {
                    const audioSrc = `/static/audio/words/${currentWord.answer.toLowerCase().replace(/\s+/g, '_')}.mp3`;
                    playAudioSmart(audioSrc);
                }
            } catch (audioError) {
                console.log('🎵 音频播放失败:', audioError);
            }
            
            // 🚀 设置自动跳转逻辑
            window.recognitionAutoNext = () => {
                // 🔧 修复：不再在这里处理单词移除逻辑，交给fetchWord()统一处理
                // 单词已被标记移除（如果达到5星），fetchWord()会自动处理
                
                // ⚠️ 注意：5星单词不会立即移除，而是被标记为_should_remove
                // fetchWord()会在显示下一个单词时检查并移除标记的单词
                currentWordIndex++;
                
                fetchWord();
            };
            
        } else {
            console.error('❌ Recognition答案提交失败:', data.error);
        }
    } catch (error) {
        console.error('❌ Recognition答案提交异常:', error);
    }
}

/**
 * 显示Recognition反馈（支持多选）
 */
function showRecognitionFeedback() {
    const { correct_answers, options, is_multiple_choice } = currentRecognitionData;
    const result = window.lastRecognitionResult || {};
    
    // 🔧 增强数据提取逻辑，确保获取到明确的is_correct值
    let is_correct = null;
    let points_change = 0;
    let responseData = null;
    
    // 1. 尝试从result.data中提取
    if (result.data && typeof result.data === 'object') {
        responseData = result.data;
        is_correct = responseData.is_correct;
        points_change = responseData.points_change || 0;
    }
    // 2. 如果result.data不存在，从result根级别提取（兼容性处理）
    else if (result && typeof result === 'object') {
        responseData = result;
        is_correct = result.is_correct;
        points_change = result.points_change || 0;
    }
    
    // 3. 严格验证is_correct的类型
    if (typeof is_correct !== 'boolean') {
        console.error('🚨 数据校验失败: is_correct不是boolean类型', {
            'result': result,
            'result.data': result.data,
            'is_correct原始值': is_correct,
            'is_correct类型': typeof is_correct,
            'responseData': responseData
        });
        
        // 显示明确的错误信息
        const resultElement = document.getElementById('recognition-result');
        const explanationElement = document.getElementById('recognition-explanation');
        
        resultElement.textContent = '⚠️ 系统数据异常';
        resultElement.className = 'uncertain';
        explanationElement.innerHTML = `
            系统接收到无效的判断结果数据。<br>
            <small>调试信息: is_correct=${is_correct} (${typeof is_correct})</small><br>
            请刷新页面重试，如果问题持续存在请联系管理员。
        `;
        
        // 显示反馈区域并立即返回
        document.getElementById('recognition-feedback').style.display = 'block';
        return;
    }
    
    // 🔍 详细的反馈数据日志（仅在数据有效时输出）
    console.log('🔍=== Recognition反馈调试信息 ===');
    console.log('后端返回结果完整数据:', result);
    console.log('提取的响应数据:', responseData);
    console.log('数据结构检查:', {
        'result.success': result.success,
        'result.data存在': !!result.data,
        'responseData.is_correct': responseData?.is_correct,
        '最终is_correct': is_correct,
        '最终points_change': points_change,
        'is_correct类型': typeof is_correct
    });
    console.log('用户选择:', selectedRecognitionOptions);
    console.log('正确答案文本:', correct_answers);
    
    // 🚀 构建正确选项字母数组
    const correctOptionLetters = correct_answers.map(correctText => {
        const index = options.findIndex(opt => opt === correctText);
        const letter = String.fromCharCode(65 + index);
        console.log(`正确答案 "${correctText}" 对应选项 ${letter} (索引${index})`);
        return letter;
    });
    
    console.log('🔍 反馈数据汇总:', {
        selected: selectedRecognitionOptions,
        correct: correctOptionLetters,
        correctTexts: correct_answers,
        backendIsCorrect: is_correct,
        points: points_change
    });
    
    // 🔍 检查前端逻辑判断与后端是否一致
    const frontendLogicCorrect = is_multiple_choice 
        ? (new Set(selectedRecognitionOptions)).size === correctOptionLetters.length && 
          selectedRecognitionOptions.every(opt => correctOptionLetters.includes(opt))
        : selectedRecognitionOptions.length === 1 && correctOptionLetters.includes(selectedRecognitionOptions[0]);
    
    console.log('🔍 前端逻辑判断:', frontendLogicCorrect);
    console.log('🔍 后端实际判断:', is_correct);
    console.log('🔍 判断是否一致:', frontendLogicCorrect === is_correct);
    
    if (frontendLogicCorrect !== is_correct) {
        console.warn('⚠️ 发现前后端判断不一致！这可能是bug的根源');
        console.warn('⚠️ 将优先使用后端判断结果');
    }
    console.log('🔍=== 反馈调试信息结束 ===');
    
    // 🚀 标记所有选项的正确性（支持多选）
    const allOptions = ['A', 'B', 'C', 'D'];
    allOptions.forEach(option => {
        const button = document.querySelector(`[data-option="${option}"]`);
        if (button) {
            // 正确答案标绿
            if (correctOptionLetters.includes(option)) {
                button.classList.add('correct');
            }
            // 错误选择标红（用户选了但不正确）
            else if (selectedRecognitionOptions.includes(option)) {
                button.classList.add('incorrect');
            }
        }
    });
    
    // 🚀 显示结果（严格按照后端判断结果显示）
    const resultElement = document.getElementById('recognition-result');
    const explanationElement = document.getElementById('recognition-explanation');
    
    // ✅ 现在is_correct已经通过严格校验，确保是boolean类型
    if (is_correct === true) {
        resultElement.textContent = '✅ 回答正确！';
        resultElement.className = 'correct';
        if (is_multiple_choice) {
            explanationElement.textContent = `太棒了！你正确选择了所有${correct_answers.length}个含义。积分 +${points_change || 10}`;
        } else {
            explanationElement.textContent = `继续保持，你做得很好！积分 +${points_change || 10}`;
        }
    } else if (is_correct === false) {
        // 🔧 不论是单选还是多选，错误就是错误，显示统一的错误信息
        resultElement.textContent = '❌ 回答错误';
        resultElement.className = 'incorrect';
        
        if (is_multiple_choice) {
            // 多选题：显示详细的分析
            const selectedCorrect = selectedRecognitionOptions.filter(opt => correctOptionLetters.includes(opt));
            const selectedIncorrect = selectedRecognitionOptions.filter(opt => !correctOptionLetters.includes(opt));
            const missedCorrect = correctOptionLetters.filter(opt => !selectedRecognitionOptions.includes(opt));
            
            explanationElement.innerHTML = `
                ✅ 正确选择: ${selectedCorrect.join(', ') || '无'}<br>
                ❌ 错误选择: ${selectedIncorrect.join(', ') || '无'}<br>
                ⭕ 遗漏选择: ${missedCorrect.join(', ') || '无'}<br>
                💡 正确答案: ${correctOptionLetters.join(', ')} (${correct_answers.join(', ')})<br>
                积分 ${points_change >= 0 ? '+' : ''}${points_change || -20}
            `;
        } else {
            // 单选题：显示正确答案
            explanationElement.innerHTML = `
                💡 正确答案是 ${correctOptionLetters[0]}: ${correct_answers[0]}<br>
                积分 ${points_change >= 0 ? '+' : ''}${points_change || -20}
            `;
        }
    }
    
    // 显示反馈区域
    document.getElementById('recognition-feedback').style.display = 'block';
    
    console.log(`📊 Recognition反馈: ${is_correct ? '正确' : '错误'}, 多选:${is_multiple_choice}, 积分:${points_change}`);
    
    // 2秒后自动跳转到下一题
    setTimeout(() => {
        if (window.recognitionAutoNext && typeof window.recognitionAutoNext === 'function') {
            window.recognitionAutoNext();
        }
    }, 2000);
}

/**
 * 进入下一个Recognition单词（支持多选）
 */
function nextRecognitionWord() {
    console.log('➡️ 进入下一个Recognition单词');
    
    // 🚀 重置当前数据（支持多选）
    currentRecognitionData = null;
    selectedRecognitionOptions = [];
    window.lastRecognitionResult = null;
    
    // 直接调用fetchWord()进入下一个单词
    currentWordIndex++;
    fetchWord();
}

/**
 * 更新本地单词数据
 * @param {number} newStarLevel - 新的星级
 */
function updateLocalWordData(newStarLevel) {
    if (filteredWords[currentWordIndex]) {
        filteredWords[currentWordIndex].star_level = newStarLevel;
        console.log('🔄 本地单词数据已更新:', {
            word: filteredWords[currentWordIndex].word,
            newStarLevel: newStarLevel
        });
        
        // 更新学习计划显示
        if (typeof displayStudyPlan === 'function') {
            displayStudyPlan();
        }
    }
}

/**
 * 更新积分显示
 * @param {number} pointsChange - 积分变化
 */
function updateScoreDisplay(pointsChange) {
    score += pointsChange;
    userPoints += pointsChange;
    updateScore();
    
    console.log(`💰 积分更新: ${pointsChange > 0 ? '+' : ''}${pointsChange}, 当前积分: ${userPoints}`);
}